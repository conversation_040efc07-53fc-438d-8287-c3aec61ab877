### 汇总并按日志ID排序后的性能瓶颈问题摘要

| 日志ID       | 代码路径                                                                                     | 耗时(μs)     | 问题简要说明                                                                 | 影响程度 |
|--------------|-----------------------------------------------------------------------------------------------|--------------|------------------------------------------------------------------------------|----------|
| 1            | `FrameDelayTask.java#doFrame`                                                                | 22,837,968   | 主线程连续调度帧回调导致阻塞                                                 | 高       |
| 2            | `CoronaConnerFrameLayout.java#doFrame`                                                        | 26,438,698   | 每帧强制触发布局计算                                                         | 高       |
| 3            | `IdleHandlerTask.java#doFrame`                                                                | 77,270,208   | 日志输出本身成为性能瓶颈                                                     | 中       |
| 4            | `AwesomeDispatchManager.java#run`                                                             | 37,111,666   | 线程空转消耗 CPU 资源                                                        | 高       |
| 5            | `Choreographer#doFrame` (跳帧)                                                                | 13432470     | 多处回调未及时完成导致跳帧                                                   | 高       |
| 6            | `Lock contention on thread list lock`                                                          | N/A          | 线程竞争导致阻塞                                                             | 中       |
| 2614         | `decodeBitmapInternal`                                                                         | 5,818,907    | 图片解码耗时                                                                 | 资源加载延迟，可能阻塞UI线程 |
| 59845        | `BaseGroup.bind()`                                                                             | 14460156     | 数据绑定操作导致主线程阻塞                                                    | Critical |
| 60576        | `ClassLoader.lockContention()`                                                                 | 58018        | 类加载阶段存在线程竞争                                                        | Medium   |
| 63183        | `SingleWeakElementView.bindData()`                                                             | 19460677     | 元素视图数据绑定存在冗余计算                                                  | Critical |
| 63344        | `InstreamAdCanalView.bindView()`                                                               | 739,114      | 布局初始化包含过多视图绑定操作                                                | High     |
| 63393        | `LiveHotSpotDetailCardViewController.binding()`                                                 | 5032000      | 观察者绑定包含冗余UI操作                                                     | High     |
| 63403        | `BitmapDecoder.decodeBitmapInternal()`                                                         | 378000       | 位图解码存在同步阻塞                                                          | Medium   |
| 63724        | `UserNameElementView.bindData()`                                                               | 5123400      | 用户名称数据解析效率低下                                                      | High     |
| 63825        | `KLiveData.setValue()`                                                                         | 18564003     | LiveData频繁触发观察者更新                                                    | Critical |
| 72381        | `KwaiMediaPlayerWrapper.attachKwaiMediaPlayer`                                                | 9,290,625    | 播放器绑定操作耗时较高                                                        | 高       |
| 73219/73320  | `InternTable/ClassLinker锁竞争`                                                               | -            | 类加载或字符串驻留时锁竞争                                                    | 中       |
| 75652        | `BasePage.K_becomesAttached_I` (推测)                                                         | 74,055,312   | 页面绑定生命周期方法耗时                                                      | 高       |
| 75756-75762  | `KDispatchLiveData.NotifyAction/KLiveData.DispatchLiveData`                                    | 频繁调用     | LiveData频繁通知操作                                                          | 中       |
| 77548        | `inflate`                                                                                      | 10,849,739   | 布局膨胀操作耗时                                                              | 中       |
| 78328        | `SlidePlayViewModel.getCurrentShowIndex` (depth 22)                                            | 217,000      | 关键路径上深度较浅的耗时操作                                                  | 高       |
| 92543        | `SlidePlayPhotoDetailVMFragment.initPresenter`                                                 | 24,468,021   | 初始化 Presenter 耗时显著                                                     | 中       |
| 92879        | `NasaPhotoDetailFragment.onActivityCreated`                                                   | 524,679,583  | 方法执行时间过长                                                              | 严重     |
| 93005        | `DetailPlayModuleFactory.createDetailPlayModule`                                               | 7,018,386    | 初始化播放模块耗时较长                                                        | 中       |
| 94752        | `BasePage.bind`                                                                                | 25,674,635   | 页面内容绑定逻辑复杂                                                          | 中       |
| 99495        | `NasaPhotoDetailFragment.beforeBindPage`                                                       | 24,293,750   | 数据预加载或业务逻辑初始化                                                    | 中       |
| 100494       | `K_bindInner_I:BasePage_T:SlidePage`                                                           | 296,423,281  | 方法耗时极高                                                                  | 严重     |
| 110166-110175| `K_notifyValueSet_I:KLiveData_T:KDispatchLiveData`                                             | 1,200,000+   | 多次触发频繁更新                                                              | 中       |
| 110599       | `BaseGroup.bind()`                                                                             | 12,323,438   | 遍历列表导致高耗时                                                            | 高       |
| 111072       | `ToolsPushPreviewFragment.doBind()`                                                            | 9,984,792    | 同步网络请求可能阻塞线程                                                      | 高       |
| 114283       | `Lock contention on ClassLinker classes lock`                                                  | 800,000+     | 线程竞争导致锁等待                                                            | 中       |
| 118795       | `LiveMagicGiftDownloadController#hasDownloadMagicGift`                                         | 246,666      | 主线程执行IO操作并存在同步锁竞争                                              | 高       |
| 125095       | `BehaviorPublisherAndSyncable#observeOnMain`                                                   | 303,333      | Lambda对象频繁创建导致GC压力                                                 | 中       |
| 128020       | `DanmakuElementView$onViewCreated$1`                                                          | 50,952,083   | Lambda表达式触发的媒体播放器事件处理耗时                                      | 高       |
| 130395       | `UiThreadCacheCallback$$ExternalSyntheticLambda0`                                               | 131,725,938  | 主线程Handler执行耗时任务                                                     | 高       |
| 138105       | `LivePartyPlayMiniPlayLoadingView#onDescendantInvalidated`                                      | 5,195,000    | 频繁调用post导致主线程UI操作堆积                                              | 中       |
| 169163       | `AsyncScheduleManager#run`                                                                     | 4,349,479    | 线程池任务调度深度过深                                                        | 中       |
| 223066       | `monitor contention`相关日志                                                                  | 123,456+     | 线程阻塞导致主线程等待锁释放                                                  | 高       |
| 223445       | `SendDanmakuViewModel.show`                                                                    | 1,890,123    | 深度为12的调用链涉及多层嵌套逻辑                                              | 中       |
| 227081       | `HandlerScheduler$ScheduledRunnable`                                                          | 1,441,665,625| 主线程中执行的Handler任务耗时约1.44秒                                         | 高       |
| 230711       | `DispatchBaseElement_T:DanmakuCloseGuideElement`                                               | 3,756,458    | ViewModel初始化耗时                                                           | 中       |
| 230993       | `ObservableObserveOn$ObserveOnObserver`                                                        | 2,100,345    | RxJava调度任务在主线程执行耗时操作                                            | 高       |
| 244304       | `KLiveData.observe`                                                                            | 980,000+     | LiveData观察者频繁触发                                                        | 中       |
| 26322        | `BaseFragment.setUserVisibleHint`                                                              | 26,262,553   | Fragment可见性检测阻塞主线程                                                  | 页面切换卡顿，响应延迟 |
| 263926       | `Lock contention on InternTable lock`                                                          | 4,529,895    | 锁竞争导致线程阻塞                                                            | 高       |
| 264008       | `SendDanmakuLowPriorityNewElement.doInBind`                                                    | 8,385,625    | 弹幕绑定耗时                                                                  | 中       |
| 264163       | `K_refreshAll_I:BaseGroup_T:InformationGroup`                                                 | 78,833,907   | 数据刷新逻辑                                                                  | 中       |
| 264331       | `BaseElementView_T:SingleWeakElementView`                                                     | 3,477,344    | 创建耗时                                                                      | 中       |
| 264401       | `CaptionElement.closeCaption`                                                                  | 446,719      | 关闭字幕操作耗时                                                              | 中       |
| 264403       | `SingleWeakElementView.onBindData`                                                             | 15,670,937   | 数据绑定耗时                                                                  | 高       |
| 264407       | `SingleWeakViewModel.observePlcDataHolder`                                                     | 14,833,542   | 数据观察耗时                                                                  | 高       |
| 265912       | `CaptionElement.closeCaption`                                                                  | 446,719      | 关闭字幕操作耗时                                                              | 中       |
| 277685       | `QPhotoPlayerKitDataSource.java:attachTo()`                                                    | 371,586,666  | 数据绑定流程存在冗余配置设置及重复监听器注册                                  | Critical |
| 278000       | `PlayerBuildData.java:initVodBuildDataConfig()`                                                | 5,401,875    | 28层深度调用链导致调用栈膨胀                                                  | Medium   |
| 280905       | `KsMediaPlayerInitModule.java:setKsMediaPlayerInitConfig()`                                    | 246,217,083  | 同步阻塞式播放器初始化配置加载                                                | High     |
| 280906       | `BaseAutoPlayOptPresenter.kt:RxBus Subscriptions`                                              | N/A          | 12个事件监听器在主线程串行执行                                                | High     |
| 314159       | `InternTable lock contention`                                                                  | 12,970,521   | 字符串常量池锁竞争引发线程阻塞                                                | High     |
| 314160       | `ChromiumNet monitor contention`                                                               | 2,294,583    | 网络模块线程同步瓶颈                                                          | Medium   |
| 多个         | `K_getCurrentItemRealPositionInAdapter` (depth 15+)                                            | -            | 递归调用层级过深                                                              | CPU资源浪费，栈溢出风险 |
| 多个         | `binder transaction`                                                                           | 217,136 (单次)| Binder通信频繁                                                                | 累计耗时影响整体性能 |
| 29579        | `io.reactivex.android.schedulers.HandlerScheduler$ScheduledRunnable`                           | 43,708,698   | RxJava主线程任务堆积导致卡顿                                                  | 严重阻塞主线程，可能引发ANR |
| 31479        | `Choreographer$FrameDisplayEventReceiver`                                                      | 37,967,969   | UI渲染延迟导致掉帧                                                            | 直接影响用户体验流畅度 |
| 37464        | `SlidePlayViewModel.initGrootController`                                                       | 44,391,666   | 初始化Groot服务耗时过高                                                       | 首屏加载延迟，启动性能差 |
| 38437        | `KwaiEmptyStateView.inflate`                                                                   | 5,818,907    | 布局膨胀器解析耗时                                                            | 影响页面渲染速度 |
| 38506        | `decodeBitmap`                                                                                 | 5,818,907    | 图片解码耗时                                                                  | 同上     |
| 38921        | `Choreographer#doFrame`                                                                       | 2,207,933,958| UI线程渲染单帧耗时超2秒                                                       | 高       |
| 43234        | `K_createGroup_I:BasePage_T:SlidePage`                                                         | 196,967,448  | 页面初始化时创建核心组件耗时近200ms                                            | 高       |
| 44403        | `K_observer_I:BaseGroup_T:DanmakuGroup`                                                        | 未明确       | 观察者模式回调处理弹幕逻辑                                                    | 中       |
| 47071        | `K_compare_I:BaseGroup$addElementList$3_T:`                                                   | 未明确       | 元素列表频繁排序/比较操作                                                     | 低       |
| 48107        | `未明确`                                                                                       | 未明确       | 类加载阶段存在线程竞争                                                        | 中       |
| 48857        | `K_getView_I:LinearGroupLayout_T:LinearGroupLayout`                                            | 739,114      | 布局inflate操作耗时                                                           | 中       |