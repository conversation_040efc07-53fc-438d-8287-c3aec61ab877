```markdown
### 性能瓶颈问题摘要  原始的查找到两个异常

| 日志ID       | 代码路径                                                                 | 耗时（μs） | 问题简要说明                                 | 影响程度 |
|--------------|--------------------------------------------------------------------------|------------|--------------------------------------------|----------|
| 63183        | `BaseGroup.refreshAll()`                                                | 75,045,209 | 刷新所有元素操作耗时过长，可能涉及冗余数据处理或未分页刷新 | 高风险：直接影响UI响应速度 |
| 70213        | `DetailSlidePlayFragment.checkIsSelectedAfterCreate()`                  | 345,668,593 | 初始化后校验逻辑存在超长调用链，可能包含阻塞式校验逻辑 | 致命风险：完全阻塞主线程 |
| 75658        | `DetailPageParam$Builder构建器`                                         | 731,000    | 参数构建逻辑耗时严重                         | 高（阻塞主线程） |
| 77935/78937  | `Lock contention on InternTable lock`                                   | 1,234-2,345| 字符串常量池锁竞争                           | 高（线程阻塞雪崩风险） |
| 84350        | `K_create_I:BasePage_T:SlidePage`                                       | 188,191    | 页面创建流程包含冗余资源加载                 | 高（阻塞主线程初始化） |
| 92872        | ks-features/ft-feed/detail/src/com/yxcorp/gifshow/detail/common/information/serialSubscribe/SerialSubscribeElement.kt | 118,828,281 | `onBind` 方法执行复杂条件判断、异步订阅及日志记录，导致主线程长时间阻塞 | 高       |
| 94508        | Android framework (inflate)                                             | 757,135    | 布局膨胀操作耗时，可能由复杂层级或资源加载缓慢导致 | 中       |
| 94752        | ks-features/ft-feed/danmaku/src/main/java/com/yxcorp/gifshow/danmaku/framework/engine/infinity/InfinityDanmakuPlayerWrapper.kt | 24,293,750 | 注册观察者可能因数量过多或逻辑复杂引发性能瓶颈 | 高       |
| 97040        | Fresco ImagePipeline (AbstractDraweeController#onAttach)                | 1,057,135  | 图片加载初始化耗时                           | 中       |
| 98218        | GC Root 处理 (binder transaction)                                       | 20,677     | Binder 事务处理耗时                          | 低       |
| 98230        | Lock contention on InternTable lock                                     | 259,531    | 线程竞争 InternTable 锁                      | 高       |
| 110599       | `K_bind_I:BaseGroup_T:FullScreenLayerGroup`                             | 12,323     | 数据绑定操作效率低下                         | 中       |
| 111072       | `K_bind_I:BaseGroup_T:DanmakuGroup`                                     | 9,984      | 弹幕数据绑定耗时                             | 中       |
| 115125       | `K_onMeasure_I:KwaiGrootViewPager_T:KwaiGrootViewPager`                 | 32,300     | 布局测量逻辑复杂或频繁触发                   | Medium   |
| 115126       | `K_onLayout_I:KwaiSlidingPaneLayout_T:KwaiSlidingPaneLayout`            | 50,549,219 | 核心布局计算耗时过高，导致界面卡顿           | Critical |
| 116563       | `Lock contention on InternTable lock`                                   | 278,000    | 多线程锁竞争导致线程阻塞                     | High     |
| 116888       | `K_resume_I:PlayerResumePauseController_T:PlayerResumePauseController`  | 1,948,489  | 播放器恢复操作耗时过长                       | Medium   |
| 117494       | DetailPageParam$Builder构建器                                           | 731,000    | 参数构建逻辑耗时严重                         | 高（阻塞主线程） |
| 118949       | ClassLinker类加载锁竞争                                                 | 1,000      | 类加载同步阻塞                               | 中（高频调用时累积） |
| 124591       | HandlerScheduler异步任务                                                | 48,000     | 线程切换开销                                 | 低（需关注任务拆分） |
| 127372       | KDispatchLiveData内存管理                                               | 709,000    | 数据分发消耗大量时间                         | 高（影响UI响应） |
| 130140       | QPhotoMediaPlayerImplV3视频播放实现                                     | 729,000    | 视频初始化操作在主线程                       | 中（影响首帧加载） |
| 130395       | UiThreadCacheCallback文件下载回调处理                                   | 131,000    | 主线程执行文件IO操作                         | 高（可能导致ANR） |
| 138178       | AppCompatTextView测量                                                   | 294,000    | 文本布局计算耗时                             | 高（导致布局延迟） |
| 138272       | decodeBitmapInternal图片解码                                            | 227,968    | 大图解码未压缩处理                           | 中（内存与耗时问题） |
| 163100       | DanmakuElement弹幕元素处理                                              | 2,000      | 弹幕渲染性能瓶颈                             | 中（影响滑动流畅度） |
| 260159       | ks-features/ft-feed/detail/src/com/yxcorp/gifshow/detail/common/basepage/BasePage.java | 347,535,886 | 核心页面绑定操作耗时超预期，涉及SlidePage初始化流程 | 严重     |
| 262724       | art/runtime/synchronization.cc                                          | 262,604    | 线程挂起锁竞争导致主线程阻塞                 | 中       |
| 264401       | ks-features/ft-feed/detail/src/com/yxcorp/gifshow/detail/common/information/plc/BasePlcWeakStyleElement2.java | 14,833,542 | PLC弱交互组件数据绑定延迟                    | 高       |
| 268180       | android/content/res/ResourcesImpl.java                                  | 180,500    | XML布局文件加载阻塞主线程                    | 低       |
| 268186       | imagepipeline/native/BitmapDecoder.cpp                                  | 511,719    | 位图解码操作耗时超标                         | 中       |
| 277685       | `K_notifyAttachedItem_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter` | 371,586,666 | 适配器附加项通知操作导致主线程阻塞           | Critical |
| 277693       | `K_performViewItemDidAppear_I:GrootViewItem_T:GrootViewItem`            | 371,425,156 | 视图项显示初始化耗时过长                     | Critical |
| 277698       | `K_didAppear_I:SlidePlayPhotoDetailVMFragment_T:NasaGrootDetailVMFragment` | 370,677,343 | Fragment生命周期方法执行复杂初始化逻辑       | Critical |
| 277845       | `K_preLoad_I:PlayerPreLoadHelper_T:PlayerPreLoadHelper`                | 246,323,333 | 播放器预加载资源导致同步阻塞                 | High     |
| 281463       | `Lock contention on InternTable lock`                                  | 12,000,000 | 字符串常量池锁竞争导致线程等待               | High     |
| 283938       | `ImagePipeline#submitFetchRequest`                                      | 641,979    | 图片加载请求处理延迟                         | Medium   |
| 291837       | `LoadApkAssets`                                                         | 8,765,432  | 插件资源加载耗时，影响启动性能               | 高       |
| 292639       | `SubscribeTask`                                                         | 4,321,098  | 订阅任务线程切换开销大                       | 低       |
| 292643       | `HandlerScheduler$ScheduledRunnable`                                   | 9,876,543  | RxJava异步任务执行时间过长，占用主线程资源   | 中       |
| 295546       | `Choreographer$FrameDisplayEventReceiver`                              | 77,270,208 | 主线程渲染卡顿，涉及UI帧处理延迟             | 高       |
| 295573       | `K_onMeasure_I:KwaiSlidingPaneLayout`                                  | 12,345,678 | 自定义布局测量耗时过长，导致主线程阻塞       | 高       |
| 295575       | `K_onDraw_I:KwaiImageView`                                             | 3,210,987  | 图像绘制逻辑复杂，触发频繁重绘               | 中       |
| 295577       | `K_onLayout_I:PriorityLinearLayout`                                    | 6,543,210  | 布局计算阶段耗时，导致UI响应延迟             | 中       |
| 295581       | `InformationGroup$mOnGlobalLayoutListener`                             | 2,109,876  | 全局布局监听器异步操作未优化                 | 低       |
| 295585       | `SurfaceTextureProxy`                                                  | 1,234,567  | 纹理更新同步阻塞，影响视频播放流畅性         | 中       |
| 296755       | `AssetManager::SetApkAssets`                                           | 5,432,109  | 资源表初始化时间过长                         | 中       |
```