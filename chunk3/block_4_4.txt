id	dur	name	depth	thread_dur	ts
276804	239583	K_detached_I:BaseGroup_T:MiddleGroup	16.0		1469469315801227.0
276806	209636	K_detached$slide_play_detail_framework_release_I:DispatchBaseElement_T:AtlasPlaySegmentedProgressElement	17.0		1469469315823102.0
276824	222552	K_detached_I:BaseGroup_T:CenterEntranceGroup	16.0		1469469316139821.0
276843	4756719	K_detached_I:BaseGroup_T:RightActionBarGroup	16.0		1469469316456279.0
276863	280000	K_detached$slide_play_detail_framework_release_I:DispatchBaseElement_T:AvatarAndFollowElement	17.0		1469469316692685.0
276890	258698	K_detached$slide_play_detail_framework_release_I:DispatchBaseElement_T:RewardElement	17.0		1469469316990810.0
276917	291094	K_detached$slide_play_detail_framework_release_I:DispatchBaseElement_T:LikeElement	17.0		1469469317270133.0
276946	2589323	K_detached$slide_play_detail_framework_release_I:DispatchBaseElement_T:CommentElement	17.0		1469469317580290.0
276969	2248594	K_onDetached_I:CommentElement$8_T:	18.0		1469469317912269.0
276972	2207916	K_onDetachCommentIconBreathAnimation_I:CommentElement_T:CommentElement	19.0		1469469317944092.0
276991	367343	K_onAttachedStateChanged_I:GrowthShowCommentPluginImpl_T:GrowthShowCommentPluginImpl	20.0		1469469318211488.0
276995	330364	K_onPhotoDetached_I:GrowthShowCommentManager_T:GrowthShowCommentManager	21.0		1469469318239613.0
277023	1497760	K_setCommentIconAnimShow_I:CommentViewModel_T:CommentViewModel	20.0		1469469318645498.0
277043	1229896	K_setValue_I:KLiveData_T:KDispatchLiveData	21.0		1469469318901227.0
277044	1200885	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	22.0		1469469318923519.0
277049	1141979	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	23.0		1469469318975915.0
277052	1113646	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	24.0		1469469318997477.0
277054	1086146	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	25.0		1469469319017529.0
277056	1057187	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	26.0		1469469319039040.0
277059	1017396	K_onChanged_I:CommentElementView$onBindData$5_T:CommentElementView$onBindData$5	27.0		1469469319071019.0
277061	979688	K_onChanged_I:CommentElementView$onBindData$5_T:CommentElementView$onBindData$5	28.0		1469469319100185.0
277067	893593	K_stopCommentIconBreathAnimation_I:CommentElementView_T:CommentElementView	29.0		1469469319177790.0
277073	795208	K_resetCommentIconBreathAnimationViews_I:CommentElementView_T:CommentElementView	30.0		1469469319267269.0
277132	275938	K_detached$slide_play_detail_framework_release_I:DispatchBaseElement_T:CollectElement	17.0		1469469320191904.0
277152	246823	K_detached$slide_play_detail_framework_release_I:DispatchBaseElement_T:ShareElement	17.0		1469469320485550.0
277169	440416	K_detached$slide_play_detail_framework_release_I:BaseElement_T:MusicWheelElement	17.0		1469469320763988.0
277201	287969	K_detached_I:BaseGroup_T:PlayControllerGroup	16.0		1469469321318206.0
277202	254427	K_detached$slide_play_detail_framework_release_I:DispatchBaseElement_T:ProgressPreviewElement	17.0		1469469321342269.0
277225	329635	K_detached_I:BaseSingleElementGroup_T:NegativeFeedbackGroup	16.0		1469469321704873.0
277227	298698	K_detached$slide_play_detail_framework_release_I:DispatchBaseElement_T:NegativeFeedbackElement	17.0		1469469321725654.0
277250	796719	K_detached_I:BaseSingleElementGroup_T:EmptyPhotoGroup	16.0		1469469322133883.0
277252	765989	K_detached$slide_play_detail_framework_release_I:DispatchBaseElement_T:EmptyPhotoNoteElement	17.0		1469469322156696.0
277268	550364	K_onDetached_I:PageAttachChangedListener_T:	18.0		1469469322362790.0
277269	515209	K_onDetached_I:EmptyPhotoNoteElement$2_T:	19.0		1469469322385810.0
277272	251614	K_lambda$onBind$0_I:SlidePlayScaleHelpPresenter_T:ScaleHelpWithContentFramePresenter	20.0		1469469322425863.0
277305	233594	K_detached_I:BaseSingleElementGroup_T:SerialPayPhotoGroup	16.0		1469469323037008.0
277306	207344	K_detached$slide_play_detail_framework_release_I:DispatchBaseElement_T:SerialPausePayElement	17.0		1469469323056123.0
277325	1659792	K_detached_I:BaseGroup_T:FullScreenLayerGroup	16.0		1469469323358154.0
277327	226719	K_detached$slide_play_detail_framework_release_I:DispatchBaseElement_T:PlayFailedRetryElement	17.0		1469469323384248.0
277341	261979	K_detached$slide_play_detail_framework_release_I:DispatchBaseElement_T:PlayPauseCenterElement	17.0		1469469323630029.0
277372	200781	K_detached$slide_play_detail_framework_release_I:DispatchBaseElement_T:BottomProgressLoadingElement	17.0		1469469324106748.0
277385	461458	K_detached$slide_play_detail_framework_release_I:BaseElement_T:PayCourseTrailFinishElement	17.0		1469469324324821.0
277412	204583	K_detached$slide_play_detail_framework_release_I:DispatchBaseElement_T:CoronaPlayPayPauseElement	17.0		1469469324804769.0
277431	229375	K_detached_I:BaseSingleElementGroup_T:DisclaimerGroup	16.0		1469469325111696.0
277454	613073	K_detached_I:BaseGroup_T:DanmakuGroup	16.0		1469469325446383.0
277455	255104	K_detached$slide_play_detail_framework_release_I:DispatchBaseElement_T:DanmakuCloseGuideElement	17.0		1469469325466956.0
277472	307084	K_detached$slide_play_detail_framework_release_I:DispatchBaseElement_T:DanmakuElement	17.0		1469469325741383.0
277492	695573	K_detached_I:BaseGroup_T:TopRightGroup	16.0		1469469326151904.0
277508	448542	K_detached$slide_play_detail_framework_release_I:BaseElement_T:SaveTrafficReminderElement	17.0		1469469326388831.0
277540	665104	K_detached_I:BaseGroup_T:PlcStrongGroup	16.0		1469469326947581.0
277541	632500	K_detached$slide_play_detail_framework_release_I:DispatchBaseElement_T:SingleStrongStyleElement	17.0		1469469326972790.0
277561	409427	K_onDetached_I:DefaultPageAttachChangedListener_T:	18.0		1469469327187113.0
277562	380781	K_onDetached_I:BasePlcStrongStyleElement$1_T:	19.0		1469469327208727.0
277566	330885	K_onPageAttached_I:SingleStrongStyleElement_T:SingleStrongStyleElement	20.0		1469469327250290.0
277571	227396	K_onPageAttached_I:SingleStrongViewModel_T:SingleStrongViewModel	21.0		1469469327345550.0
277595	443125	K_detached_I:BaseGroup_T:BottomPanelGroup	16.0		1469469327707738.0
277598	201303	K_detached$slide_play_detail_framework_release_I:DispatchBaseElement_T:SurveyElement	17.0		1469469327730185.0
277626	250052	K_performTrackStage_I:DetailSlidePlayFragment_T:NasaGrootDetailVMFragment	14.0		1469469328428571.0
277685	371586666	K_notifyAttachedItem_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter	10.0		1469469329577790.0
277693	371425156	K_performViewItemDidAppear_I:GrootViewItem_T:GrootViewItem	11.0		1469469329727373.0
277694	371028072	K_performViewItemDidAppear_I:DetailSlidePlayFragment_T:NasaGrootDetailVMFragment	12.0		1469469329753363.0
277695	370805417	K_performViewItemDidAppear_I:GrootBaseFragment_T:NasaGrootDetailVMFragment	13.0		1469469329840654.0
277698	370677343	K_didAppear_I:SlidePlayPhotoDetailVMFragment_T:NasaGrootDetailVMFragment	14.0		1469469329960290.0
277702	275417	K_performTrackStage_I:DetailSlidePlayFragment_T:NasaGrootDetailVMFragment	15.0		1469469330171383.0
277710	274422187	K_callAttachedOnScrollEndListener_I:SlidePlayPhotoDetailVMFragment_T:NasaGrootDetailVMFragment	15.0		1469469330482790.0
277713	274345156	K_traverseListenersAttachedOrDidAppearByGroup_I:SlidePlayLifecycleHelper_T:SlidePlayLifecycleHelper	16.0		1469469330548519.0
277714	274193021	K_traverseListenersAttached_I:SlidePlayLifecycleHelper_T:SlidePlayLifecycleHelper	17.0		1469469330582321.0
277728	821979	K_attachedOnScrollEnd_I:FreeTrafficNotifyPresenter$1_T:	18.0		1469469330952477.0
277732	597396	K_lambda$showDetailNotify$0_I:FreeTrafficHelper_T:FreeTrafficHelper	19.0		1469469331051956.0
277776	249239532	K_attachedOnScrollEnd_I:SlidePlayPhotoPreloadEmitterPresenter$2_T:	18.0		1469469331939508.0
277789	248813907	K_schedulePreload_I:SlidePlayPhotoPreloadEmitterPresenter_T:SlidePlayPhotoPreloadEmitterPresenter	19.0		1469469332341435.0
277791	248756094	K_preload_I:SlidePlayPhotoPreloadEmitterPresenter_T:SlidePlayPhotoPreloadEmitterPresenter	20.0		1469469332376331.0
277823	247214531	K_preload_I:SlidePlayPhotoPreloadOptPresenter_T:SlidePlayPhotoPreloadOptPresenter	21.0		1469469333007894.0
277833	246992552	K_startPreload_I:SlidePlayPhotoPreloadOptPresenter_T:SlidePlayPhotoPreloadOptPresenter	22.0		1469469333201956.0
277836	279948	K_logInfo_I:SlidePlayPhotoPreloadOptPresenter_T:SlidePlayPhotoPreloadOptPresenter	23.0		1469469333265446.0
277845	246323333	K_preLoad_I:PlayerPreLoadHelper_T:PlayerPreLoadHelper	23.0		1469469333616123.0
277848	246217083	K_preLoad_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	24.0		1469469333700446.0
277850	894948	K_logPlayInfo_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	25.0		1469469333727477.0
277852	665208	K_getPlayModuleInfo_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	26.0		1469469333861800.0
277887	245245000	K_internalCreatePlayer_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	25.0		1469469334654821.0
277904	389636	K_createPlayBuildDataKpMid_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	26.0		1469469335004456.0
277924	850261	K_getMediaType_I:QPhotoMediaType_T:QPhotoMediaType	26.0		1469469335491383.0
277998	6243594	K_invoke_I:PlayerBuildData$1_T:	26.0		1469469337958310.0
277999	6203646	K_invoke_I:PlayerBuildData$1_T:	27.0		1469469337990029.0
278000	5401875	K_initVodBuildDataConfig_I:PlayerBuildData_T:PlayerBuildData	28.0		1469469338014873.0
278001	4116927	K_getUserSelectMultiRateMode_I:VideoQualitySwitchUtil_T:VideoQualitySwitchUtil	29.0		1469469338044456.0
278002	3814896	K_isEnable_I:VideoQualitySwitchUtil_T:VideoQualitySwitchUtil	30.0		1469469338071800.0
278021	3237448	K_getMultiRateListCompat_I:VideoQualitySwitchUtil_T:VideoQualitySwitchUtil	31.0		1469469338534404.0
278031	2953177	K_getFeedManifestIdList_I:VideoQualitySwitchUtil_T:VideoQualitySwitchUtil	32.0		1469469338808050.0
278038	2754740	K_getFeedManifestIdList_I:VideoQualitySwitchUtil_T:VideoQualitySwitchUtil	33.0		1469469338999977.0
278147	302812	K_getDetailMaxSpeedKbps_I:PhotoPlayerConfig_T:PhotoPlayerConfig	29.0		1469469342214769.0
278207	684635	K_lambda$createPlayBuildDataKpMid$2_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	28.0		1469469343499300.0
278249	774635	K_getMediaType_I:QPhotoMediaType_T:QPhotoMediaType	26.0		1469469344538467.0
278287	864948	K_configManifestShortVideoPlayer_I:QPhotoInternalPlayerBuilderKpMid3_T:QPhotoInternalPlayerBuilderKpMid3	26.0		1469469345474821.0
278297	228646	K_mp4AbrModelPath_I:QPhotoPlayerHlsAbrConf_T:QPhotoPlayerHlsAbrConf	27.0		1469469345867008.0
278312	497136	K_buildManifestShortVideoSwitcher_I:QPhotoInternalPlayerBuilderKpMid3_T:QPhotoInternalPlayerBuilderKpMid3	26.0		1469469346380133.0
278317	386459	K_<init>_I:ManifestShortVideoSwitcherKpMid_T:ManifestShortVideoSwitcherKpMid	27.0		1469469346483310.0
278318	225573	K_initWithManifest_I:ManifestShortVideoSwitcherKpMid_T:ManifestShortVideoSwitcherKpMid	28.0		1469469346513154.0
278336	921198	K_hackTriggerSwitchSync_I:KpMidSwitcherAdapter_T:KpMidSwitcherAdapter	26.0		1469469347085029.0
278337	885156	K_switchHost_I:KpMidSwitcherAdapter_T:KpMidSwitcherAdapter	27.0		1469469347112477.0
278343	613490	K_lambda$switchHost$4_I:KpMidSwitcherAdapter_T:KpMidSwitcherAdapter	28.0		1469469347374560.0
278345	344011	K_switchSource_I:ManifestShortVideoSwitcherKpMid_T:ManifestShortVideoSwitcherKpMid	29.0		1469469347410185.0
278346	303333	K_switchInternal_I:ManifestShortVideoSwitcherKpMid_T:ManifestShortVideoSwitcherKpMid	30.0		1469469347443988.0
278348	263072	K_makePlaySource_I:ManifestShortVideoSwitcherKpMid_T:ManifestShortVideoSwitcherKpMid	31.0		1469469347476488.0
278371	1995677	K_injectDataSource_I:KpMidSwitcherAdapter_T:KpMidSwitcherAdapter	26.0		1469469348333154.0
278373	1948385	K_prepareDataSourceWayne_I:ManifestShortVideoSwitcherKpMid$MsvPlaySource_T:MsvPlaySource	27.0		1469469348365915.0
278388	222188	K_i_I:WayneLogImpl_T:WayneLogImpl	28.0		1469469348916435.0
278411	512291	K_i_I:WayneLogImpl_T:WayneLogImpl	28.0		1469469349766540.0
278437	289375	K_getBoolean_I:PhotoPlayerConfig$KwaiPlayerSwitcherDebug_T:KwaiPlayerSwitcherDebug	26.0		1469469351298883.0
278441	290260	K_getSwitchProvider_I:WayneInjectConfigAdapter$1_T:WayneInjectConfigAdapter$1	26.0		1469469351730498.0
278443	209479	K_getPlayerSwitchProvider_I:PhotoPlayerConfig_T:PhotoPlayerConfig	27.0		1469469351799092.0
278469	217917	K_i_I:WayneLogImpl_T:WayneLogImpl	26.0		1469469353665185.0
278489	219636	K_run_I:KsMediaPlayerInitModule$3_T:	26.0		1469469354586904.0
278492	200469	K_<init>_I:KwaiPlayerBaseBuilder_T:KwaiPlayerVodBuilder	26.0		1469469354878258.0
278505	245312	K_setSwitchProvider_I:KwaiPlayerBaseBuilder_T:KwaiPlayerVodBuilder	26.0		1469469355510394.0
278519	228385	K_build_I:ProductContext$Builder_T:Builder	26.0		1469469356226175.0
278958	321458	K_enableUseAemon_I:WayneInjectConfigAdapter$1_T:WayneInjectConfigAdapter$1	26.0		1469469368951644.0
278960	286458	K_getPlayerVodAemonConfig_I:PhotoPlayerConfig_T:PhotoPlayerConfig	27.0		1469469368975238.0
278984	155067031	K_build_I:KwaiPlayerVodBuilder_T:KwaiPlayerVodBuilder	26.0		1469469369383675.0
278989	2116563	K_newCreator_I:KwaiPlayerCreator_T:KwaiPlayerCreator	27.0		1469469369478675.0
278999	1878385	K_<init>_I:KwaiPlayerCreatorAemonImpl_T:KwaiPlayerCreatorAemonImpl	28.0		1469469369706696.0
279027	91580730	K_create_I:KwaiPlayerCreatorAemonImpl_T:KwaiPlayerCreatorAemonImpl	27.0		1469469371630185.0
279028	91546354	K_<init>_I:KwaiPlayerCreatorAemonImpl$InternalKwaiPlayerImpl_T:InternalKwaiPlayerImpl	28.0		1469469371651956.0
280718	239896	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	29.0		1469469457886071.0
280739	4616823	K_setupSwitch_I:KwaiPlayerCreatorAemonImpl$InternalKwaiPlayerImpl_T:InternalKwaiPlayerImpl	29.0		1469469458568310.0
280742	1030730	K_getJSON_I:PhotoPlayerConfig$KwaiPlayerSwitcherDebug_T:KwaiPlayerSwitcherDebug	30.0		1469469458649612.0
280743	965886	K_getJSON_I:PhotoPlayerConfig$KwaiPlayerSwitcher_T:KwaiPlayerSwitcher	31.0		1469469458689508.0
280769	280677	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	30.0		1469469459941071.0
280789	277448	K_getJSON_I:PhotoPlayerConfig$KwaiPlayerSwitcherDebug_T:KwaiPlayerSwitcherDebug	30.0		1469469460772946.0
280791	225000	K_getJSON_I:PhotoPlayerConfig$KwaiPlayerSwitcher_T:KwaiPlayerSwitcher	31.0		1469469460808362.0
280797	205729	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	30.0		1469469461174300.0
280833	60941094	K_applyTo_I:KwaiPlayerVodBuilder_T:KwaiPlayerVodBuilder	27.0		1469469463401748.0
280834	53706927	K_applyTo_I:KwaiPlayerBaseBuilder_T:KwaiPlayerVodBuilder	28.0		1469469463431696.0
280835	356354	K_<init>_I:KwaiPlayerAspectAwesomeCache_T:KwaiPlayerAspectAwesomeCache	29.0		1469469463673050.0
280838	270052	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	29.0		1469469464117842.0
280841	305885	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	29.0		1469469464507998.0
280850	262708	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	29.0		1469469465725394.0
280857	218333	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	29.0		1469469466567790.0
280890	254375	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	29.0		1469469469774092.0
280904	497968	K_getCurrentThermalStatus_I:KwaiBatteryInfoUtil_T:KwaiBatteryInfoUtil	29.0		1469469470834092.0
280905	335000	binder transaction	30.0		1469469470950810.0
280936	201406	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	29.0		1469469473089769.0
280944	209792	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	29.0		1469469474070602.0
281005	36772135	K_setConfigFromSwitchProvider_I:KwaiPlayerVodBuilder_T:KwaiPlayerVodBuilder	29.0		1469469480197165.0
281010	5467500	K_setConfigFromSwitchProvider_I:KwaiPlayerBaseBuilder_T:KwaiPlayerVodBuilder	30.0		1469469480271331.0
281014	210156	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	31.0		1469469480611852.0
281020	211562	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	31.0		1469469481111123.0
281028	249583	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	31.0		1469469481609352.0
281065	236979	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	31.0		1469469482666019.0
281096	224375	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	31.0		1469469483637737.0
281131	261927	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	31.0		1469469484583310.0
281191	231563	K_getBoolean_I:PhotoPlayerConfig$KwaiPlayerSwitcherDebug_T:KwaiPlayerSwitcherDebug	30.0		1469469486902008.0
281240	4487084	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	30.0		1469469489657581.0
281242	4176145	Contending for pthread mutex	31.0		1469469489925290.0
281289	257240	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	30.0		1469469494608310.0
281463	12970521	Lock contention on InternTable lock (owner tid: 27818)	30.0		1469469498144977.0
281660	1754948	Lock contention on ClassLinker classes lock (owner tid: 27818)	30.0		1469469511532060.0
281684	637813	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	30.0		1469469513317581.0
281688	275521	Lock contention on thread suspend count lock (owner tid: 27818)	31.0		1469469513446487.0
281700	2556719	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	30.0		1469469514275237.0
281703	2313490	monitor contention with owner ChromiumNet (76) at boolean android.os.MessageQueue.enqueueMessage(android.os.Message			31.0
281704	2294583	Lock contention on a monitor lock (owner tid: 27907)	32.0		1469469514430290.0
281723	210469	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	28.0		1469469517720446.0
281776	262344	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	28.0		1469469521255498.0
281859	262917	K_setDataSourceSeekReopenThresholdKB_I:KwaiPlayerAspectAwesomeCache_T:KwaiPlayerAspectAwesomeCache	26.0		1469469525258102.0
281866	266823	K_setCacheDownloadConnectTimeoutMs_I:KwaiPlayerAspectAwesomeCache_T:KwaiPlayerAspectAwesomeCache	26.0		1469469525673571.0
281941	332864	K_setVodP2spOffThreshold_I:KwaiPlayerAspectAwesomeCache_T:KwaiPlayerAspectAwesomeCache	26.0		1469469528966592.0
281944	234740	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	27.0		1469469529029404.0
281963	285729	K_setMp4AbrModelPath_I:AspectKwaiVodAdaptive_T:AspectKwaiVodAdaptive	26.0		1469469529668623.0
281967	201979	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	27.0		1469469529738206.0
282031	515990	K_setAwesomeCacheCallback_I:KwaiPlayerAspectAwesomeCache_T:KwaiPlayerAspectAwesomeCache	26.0		1469469532369977.0
282035	401458	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	27.0		1469469532448415.0
282036	254219	Lock contention on thread suspend count lock (owner tid: 27882)	28.0		1469469532469196.0
282121	624427	K_build_I:MediaCdnLoggerFactoryImpl_T:MediaCdnLoggerFactoryImpl	26.0		1469469535789196.0
282326	404115	K_accept_I:DefaultFrameUiModule$createBinder$1$bindViewToViewModel$4_T:	26.0		1469469540643362.0
282328	362969	K_setSurfaceType_I:PlayerKitContentFrame_T:PlayerKitContentFrame	27.0		1469469540674300.0
282351	1578021	K_accept_I:DefaultFrameUiModule$createBinder$1$bindViewToViewModel$1_T:	26.0		1469469541131644.0
282353	1529740	K_setPlayerInterface_I:PlayerKitContentFrame_T:PlayerKitContentFrame	27.0		1469469541173258.0
282356	1475677	K_setPlayerWithoutCleanSurface_I:PlayerKitContentFrame_T:PlayerKitContentFrame	28.0		1469469541220342.0
282363	1368385	K_setupNewPlayer_I:PlayerKitContentFrame_T:PlayerKitContentFrame	29.0		1469469541321019.0
282373	477239	K_bindSurfaceToPlayer_I:PlayerKitContentFrame_T:PlayerKitContentFrame	30.0		1469469541479092.0
282374	262813	K_logMsg_I:PlayerKitContentFrame_T:PlayerKitContentFrame	31.0		1469469541500029.0
282398	227553	K_refresh_I:PlayerKitContentFrame_T:PlayerKitContentFrame	30.0		1469469542453987.0
282407	296302	K_logMsg_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	26.0		1469469542818571.0
282420	791927	K_<init>_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	26.0		1469469543169196.0
282421	538438	K_<init>_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	27.0		1469469543192112.0
282444	9334687	K_initPlayerWithReadyData_I:QPhotoMediaPlayerImplV3_T:QPhotoMediaPlayerImplV3	26.0		1469469544023519.0
282445	271354	K_logMsg_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	27.0		1469469544079821.0
282453	8791875	K_buildMultisourcePlayer_I:QPhotoMediaPlayerImplV3_T:QPhotoMediaPlayerImplV3	27.0		1469469544394092.0
282454	686718	K_<init>_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	28.0		1469469544422269.0
282455	452552	K_<init>_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	29.0		1469469544443623.0
282489	4114271	K_<init>_I:MultiSourceMediaPlayerImplV3_T:MultiSourceMediaPlayerImplV3	28.0		1469469545125446.0
282492	845261	K_<init>_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	29.0		1469469545176435.0
282516	294531	K_logMsg_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	30.0		1469469545666904.0
282530	3147552	K_attachKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	29.0		1469469546040967.0
282536	315573	K_logMsg_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	30.0		1469469546120758.0
282552	269427	K_innerDetach_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	30.0		1469469546476331.0
282568	318229	K_attach_I:PlayerLoggerWrapper_T:PlayerLoggerWrapper	30.0		1469469546804196.0
282582	1744635	K_setPlayerAttribute_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	30.0		1469469547151175.0
282586	348125	K_setVolume_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	31.0		1469469547229560.0
282604	288229	K_setLooping_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	31.0		1469469547649769.0
282615	392864	K_setSpeed_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	31.0		1469469547995290.0
282629	350052	K_setKwaivppFilters_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	31.0		1469469548420967.0
282665	3912656	K_attachKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	28.0		1469469549264717.0
282670	279896	K_logMsg_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	29.0		1469469549374560.0
282686	405000	K_innerDetach_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	29.0		1469469549674456.0
282688	219687	K_detach_I:PlayerLoggerWrapper_T:PlayerLoggerWrapper	30.0		1469469549693050.0
282696	356771	K_attach_I:PlayerLoggerWrapper_T:PlayerLoggerWrapper	29.0		1469469550129612.0
282710	1825157	K_setPlayerAttribute_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	29.0		1469469550507685.0
282713	325730	K_setVolume_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	30.0		1469469550592737.0
282714	289687	K_setVolume_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	31.0		1469469550620550.0
282722	322604	K_setLooping_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	30.0		1469469551005029.0
282723	281198	K_setLooping_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	31.0		1469469551033675.0
282728	323750	K_setSpeed_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	30.0		1469469551350862.0
282729	294844	K_setSpeed_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	31.0		1469469551371227.0
282740	506146	K_setKwaivppFilters_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	30.0		1469469551695758.0
282741	467396	K_setKwaivppFilters_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	31.0		1469469551725706.0
282742	239167	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	32.0		1469469551803935.0
282757	610729	K_reNotifyListeners_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	29.0		1469469552558831.0
282763	383438	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	30.0		1469469552777477.0
282764	306666	K_logMsg_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	31.0		1469469552807946.0
282779	19091875	K_attachKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	26.0		1469469553384404.0
282785	392136	K_logMsg_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	27.0		1469469553542008.0
282787	256666	K_getLogPrefix_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	28.0		1469469553588519.0
282794	1440885	K_innerDetach_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	27.0		1469469553957477.0
282798	1279323	K_unRegisterPlayerListeners_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	28.0		1469469554105498.0
282845	434271	K_attach_I:PlayerLoggerWrapper_T:PlayerLoggerWrapper	27.0		1469469555445237.0
282865	2225104	K_setPlayerAttribute_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	27.0		1469469555904665.0
282874	377917	K_setVolume_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	28.0		1469469556025237.0
282876	352604	K_setVolume_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	29.0		1469469556043779.0
282878	324583	K_setVolume_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	30.0		1469469556062894.0
282889	401302	K_setLooping_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	28.0		1469469556518050.0
282891	373854	K_setLooping_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	29.0		1469469556539352.0
282893	342709	K_setLooping_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	30.0		1469469556563206.0
282899	530156	K_setSpeed_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	28.0		1469469556944404.0
282900	500573	K_setSpeed_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	29.0		1469469556964560.0
282901	471875	K_setSpeed_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	30.0		1469469556982321.0
282902	253334	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	31.0		1469469557032112.0
282908	509584	K_setKwaivppFilters_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	28.0		1469469557497060.0
282909	474688	K_setKwaivppFilters_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	29.0		1469469557524508.0
282910	443021	K_setKwaivppFilters_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	30.0		1469469557547477.0
282913	203281	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	31.0		1469469557756331.0
282920	3606250	K_registerPlayerListeners_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	27.0		1469469558148571.0
283089	10456146	K_reNotifyListeners_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	27.0		1469469562009144.0
283106	871146	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	28.0		1469469562222269.0
283107	344115	K_logMsg_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	29.0		1469469562241227.0
283110	218802	K_getLogPrefix_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	30.0		1469469562290915.0
283130	478802	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	29.0		1469469562607894.0
283131	418177	K_logMsg_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	30.0		1469469562625550.0
283134	284270	K_getLogPrefix_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	31.0		1469469562682842.0
283158	6052604	K_lambda$new$0_I:SlidePlayPhotoPreloadEmitterPresenter_T:SlidePlayPhotoPreloadEmitterPresenter	28.0		1469469563222373.0
283172	5645469	K_schedulePreload_I:SlidePlayPhotoPreloadEmitterPresenter_T:SlidePlayPhotoPreloadEmitterPresenter	29.0		1469469563616696.0
283174	5608229	K_preload_I:SlidePlayPhotoPreloadEmitterPresenter_T:SlidePlayPhotoPreloadEmitterPresenter	30.0		1469469563640498.0
283205	2220104	K_accept_I:SerializedRelay_T:SerializedRelay	31.0		1469469567011071.0
283207	2168020	K_accept_I:PublishRelay_T:PublishRelay	32.0		1469469567034665.0
283208	1736042	Lock contention on Region lock (owner tid: 27818)	33.0		1469469567261852.0
283241	1739896	K_onPlayerStateChanged_I:MilanoItemPlayerPresenter$3_T:	28.0		1469469569997321.0
283244	385469	K_getCurrentPhoto_I:SlidePlayViewModel_T:SlidePlayViewModel	29.0		1469469570090133.0
283255	259531	K_lambda$onBind$2_I:SlideUserProcessLoggerPresenter_T:SlideUserProcessLoggerPresenter	29.0		1469469571313050.0
283259	209428	K_onPlayerStateChanged_I:SidebarProgressElement$mPlayerStateChangedListener$2$1_T:	28.0		1469469571970237.0
283268	1374687	K_logMsg_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	26.0		1469469572541696.0
283270	687500	K_getLogPrefix_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	27.0		1469469572776852.0
283273	315990	K_toString_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	28.0		1469469572835029.0
283275	249531	K_getPlayerIdIfAny_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	29.0		1469469572882894.0
283278	203646	K_getPlayerIdIfAny_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	30.0		1469469572917373.0
283284	254010	K_toString_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	28.0		1469469573181540.0
283293	816511	K_reNotify_I:KpMidSwitcherAdapter_T:KpMidSwitcherAdapter	26.0		1469469573972060.0
283294	707709	K_onSwitched_I:MultiSourceMediaPlayerImplV3_T:MultiSourceMediaPlayerImplV3	27.0		1469469574068987.0
283301	242969	K_dispatchSwitchSucceed_I:MultiSourceMediaPlayerImplV3_T:MultiSourceMediaPlayerImplV3	28.0		1469469574521331.0
283309	1258541	K_getMediaType_I:QPhotoMediaType_T:QPhotoMediaType	26.0		1469469574844769.0
283310	323386	K_isLocalVideoPhoto_I:QPhotoContentUtil_T:QPhotoContentUtil	27.0		1469469574883779.0
283366	820261	K_setLooping_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	26.0		1469469576775133.0
283368	774427	K_setLooping_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	27.0		1469469576809873.0
283370	733854	K_setLooping_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	28.0		1469469576839092.0
283372	678854	K_setLooping_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	29.0		1469469576878467.0
283377	221198	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	30.0		1469469577025862.0
283397	327188	K_setScreenOnWhilePlaying_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	26.0		1469469577640654.0
283399	274479	K_setScreenOnWhilePlaying_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	27.0		1469469577675758.0
283401	220260	K_setScreenOnWhilePlaying_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	28.0		1469469577707998.0
283405	1826563	K_logPlayInfo_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	26.0		1469469578052008.0
283409	1455729	K_getPlayModuleInfo_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	27.0		1469469578185446.0
283413	501510	K_toString_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	28.0		1469469578289300.0
283415	427708	K_getPlayerIdIfAny_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	29.0		1469469578335290.0
283417	343542	K_getPlayerIdIfAny_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	30.0		1469469578392633.0
283420	225000	K_getPlayerIdIfAny_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	31.0		1469469578499612.0
283493	472031	K_accept_I:SerializedRelay_T:SerializedRelay	21.0		1469469580629404.0
283494	396875	K_accept_I:PublishRelay_T:PublishRelay	22.0		1469469580675602.0
283519	14316042	K_attachedOnScrollEnd_I:NasaStartupPresenter$1_T:	18.0		1469469582065498.0
283523	246354	K_needRequestPlc_I:NasaPlcUtils_T:NasaPlcUtils	19.0		1469469582710394.0
283536	3164271	K_getRequestParams_I:NasaCommentCreatorPresenter$2_T:NasaCommentCreatorPresenter$2	19.0		1469469584584248.0
283538	517552	K_couldShow_I:CommentGuessSearchTopBar_T:CommentGuessSearchTopBar	20.0		1469469584877790.0
283541	276667	K_getCommentGuessSearch_I:CommentGuessSearchTopBar_T:CommentGuessSearchTopBar	21.0		1469469585105654.0
283547	912812	K_e_I:TunaPlcLogger_T:TunaPlcLogger	20.0		1469469585506800.0
283558	283646	K_couldShow_I:CommentGuessSearchTopBar_T:CommentGuessSearchTopBar	20.0		1469469586982998.0
283567	289531	K_e_I:TunaPlcLogger_T:TunaPlcLogger	20.0		1469469587332425.0
283581	672917	K_getRealCountInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	19.0		1469469587893102.0
283586	415730	K_getRealCountInAdapter_I:PositionService_T:PositionService	20.0		1469469588138987.0
283588	283906	K_logInfo_I:GrootLogger_T:GrootLogger	21.0		1469469588258467.0
283599	863021	K_getCurrentItemRealPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	19.0		1469469588638206.0
283612	635782	K_getCurrentRealPositionInAdapter_I:PositionService_T:PositionService	20.0		1469469588853987.0
283613	321615	K_getCurrentItemPositionInViewPager_I:PositionService_T:PositionService	21.0		1469469588884560.0
283614	275781	K_logInfo_I:GrootLogger_T:GrootLogger	22.0		1469469588916800.0
283626	219479	K_logInfo_I:GrootLogger_T:GrootLogger	21.0		1469469589257529.0
283638	753282	K_getLastShowType_I:SlidePlayViewModel_T:SlidePlayViewModel	19.0		1469469589568362.0
283653	397864	K_getLastShowItemEnterType_I:ViewItemService_T:ViewItemService	20.0		1469469589912217.0
283659	228750	K_logInfo_I:GrootLogger_T:GrootLogger	21.0		1469469590067633.0
283671	509063	K_getRealCountInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	19.0		1469469590516331.0
283677	300677	K_getRealCountInAdapter_I:PositionService_T:PositionService	20.0		1469469590713050.0
283680	211094	K_logInfo_I:GrootLogger_T:GrootLogger	21.0		1469469590790810.0
283692	437761	K_getPhotoInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	19.0		1469469591231279.0
283701	243646	K_getPhotoInAdapter_I:KwaiDataSourceService_T:KwaiDataSourceService	20.0		1469469591412529.0
283848	653907	K_attachedOnScrollEnd_I:SlidePlayGlobalPokePresenter$1_T:	18.0		1469469596662737.0
283849	582135	K_refreshPokeStatus_I:SlidePlayGlobalPokePresenter_T:SlidePlayGlobalPokePresenter	19.0		1469469596721696.0
283851	509740	K_isShowPokeButton_I:SlidePlayGlobalPokePresenter_T:SlidePlayGlobalPokePresenter	20.0		1469469596768154.0
283863	269010	K_isFollowed_I:SlidePlayGlobalPokePresenter_T:SlidePlayGlobalPokePresenter	21.0		1469469596995967.0
283882	5323281	K_attachedOnScrollEnd_I:SlidePlayGlobalUserInfoPresenter$1_T:	18.0		1469469597371227.0
283884	5239062	K_init_I:SlidePlayGlobalUserInfoPresenter_T:NasaUserInfoPresenter	19.0		1469469597443571.0
283891	498386	WrappingUtils#maybeApplyLeafRounding	20.0		1469469597557112.0
283913	678334	AbstractDraweeControllerBuilder#buildController	20.0		1469469598780133.0
283914	624792	PipelineDraweeControllerBuilder#obtainController	21.0		1469469598807998.0
283917	385781	PipelineDraweeController#initialize	22.0		1469469598880029.0
283918	333698	AbstractDraweeController#init	23.0		1469469598903102.0
283929	2391407	AbstractDraweeController#onAttach	20.0		1469469599537685.0
283931	2342135	AbstractDraweeController#submitRequest	21.0		1469469599565967.0
283938	641979	ImagePipeline#submitFetchRequest	22.0		1469469599769977.0
283940	594791	CloseableProducerToDataSourceAdapter#create	23.0		1469469599805915.0
283941	565885	AbstractProducerToDataSourceAdapter()	24.0		1469469599822894.0
283945	436198	AbstractProducerToDataSourceAdapter()->produceResult	25.0		1469469599939508.0
283947	400364	BitmapMemoryCacheProducer#produceResults	26.0		1469469599955446.0
283955	1261823	K_run_I:AbstractDataSource$1_T:	22.0		1469469600457998.0
283963	438490	ImagePipeline#submitFetchRequest	23.0		1469469600611904.0
283964	401406	CloseableProducerToDataSourceAdapter#create	24.0		1469469600637581.0
283965	374427	AbstractProducerToDataSourceAdapter()	25.0		1469469600653154.0
283968	285260	AbstractProducerToDataSourceAdapter()->produceResult	26.0		1469469600729821.0
283970	250677	BitmapMemoryCacheProducer#produceResults	27.0		1469469600746123.0
283976	623125	K_run_I:AbstractDataSource$1_T:	23.0		1469469601083779.0
283982	467084	ImagePipeline#submitFetchRequest	24.0		1469469601219560.0
283984	430782	CloseableProducerToDataSourceAdapter#create	25.0		1469469601244612.0
283985	404636	AbstractProducerToDataSourceAdapter()	26.0		1469469601259560.0
283990	320469	AbstractProducerToDataSourceAdapter()->produceResult	27.0		1469469601332477.0
283992	294063	BitmapMemoryCacheProducer#produceResults	28.0		1469469601347529.0
284095	610885	K_attachedOnScrollEnd_I:GrowthFreeTrafficPresenter$mAttachChangedListener$1_T:	18.0		1469469604019248.0
284099	475625	K_isHitFeedFreeTraffic_I:GrowthFreeTrafficPluginImpl_T:GrowthFreeTrafficPluginImpl	19.0		1469469604135394.0
284131	95230260	K_attached_I:BasePage_T:SlidePage	15.0		1469469604984925.0
284137	94977552	K_attachedInner_I:BasePage_T:SlidePage	16.0		1469469605231748.0
284143	1938593	K_attached_I:BaseGroup_T:SideProgressGroup	17.0		1469469605430342.0
284152	1464115	K_attached$slide_play_detail_framework_release_I:DispatchBaseElement_T:SidebarProgressElement	18.0		1469469605712477.0
284178	865104	K_onAttached_I:SidebarProgressElement$mPhotoDetailAttachChangedListener$1_T:	19.0		1469469606299352.0
284186	212136	K_getCurrProgress_I:SwipeToProfileFeedMovement_T:SwipeToProfileFeedMovement	20.0		1469469606564404.0
284192	324792	K_setAnimationFrameProgress_I:SidebarProgressViewModel_T:SidebarProgressViewModel	20.0		1469469606827998.0
284193	270625	K_setValue_I:KLiveData_T:KDispatchLiveData	21.0		1469469606870654.0
284195	231250	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	22.0		1469469606899300.0
284225	25691927	K_attached_I:BaseGroup_T:InformationGroup	17.0		1469469607521123.0
284235	229583	K_attached$slide_play_detail_framework_release_I:DispatchBaseElement_T:InformationPosition1DummyElement	18.0		1469469607805915.0
284263	299271	K_attached$slide_play_detail_framework_release_I:DispatchBaseElement_T:RecoReasonElement	18.0		1469469608287685.0
284303	329010	K_attached$slide_play_detail_framework_release_I:DispatchBaseElement_T:MerchantCommentElement	18.0		1469469609061748.0
284315	426302	K_attached$slide_play_detail_framework_release_I:DispatchBaseElement_T:UserNameElement	18.0		1469469609453727.0
284340	303386	K_attached$slide_play_detail_framework_release_I:DispatchBaseElement_T:CoCreatorAuthorsElement	18.0		1469469610207060.0
284351	874531	K_attached$slide_play_detail_framework_release_I:DispatchBaseElement_T:CaptionElement	18.0		1469469610538415.0
284360	625364	K_onAttached_I:CaptionElement$1_T:	19.0		1469469610776696.0
284363	518906	K_onTagShow_I:CaptionElement_T:CaptionElement	20.0		1469469610873571.0
284364	413125	K_isTagVisible_I:CaptionElement_T:CaptionElement	21.0		1469469610926279.0
284401	2225989	FullSuspendCheck	18.0		1469469612048519.0
284402	293437	Lock contention on thread suspend count lock (owner tid: 27818)	19.0		1469469612054248.0
284442	651354	K_onElementShow_I:MusicLabelElement_T:MusicLabelElement	18.0		1469469614462269.0
284473	225156	K_attached$slide_play_detail_framework_release_I:DispatchBaseElement_T:PoiCollectElement	18.0		1469469615174404.0
284489	561615	K_attached$slide_play_detail_framework_release_I:BaseElement_T:PushNegativeFeedbackEntryElement	18.0		1469469615450654.0
284519	216510	K_attached$slide_play_detail_framework_release_I:DispatchBaseElement_T:AdTkActionBarElement	18.0		1469469616042477.0
284553	207396	K_attached$slide_play_detail_framework_release_I:DispatchBaseElement_T:QuestionnaireElement	18.0		1469469616750550.0
284576	203125	K_attached$slide_play_detail_framework_release_I:DispatchBaseElement_T:IntensifyFollowElement	18.0		1469469617215498.0
284594	315521	K_attached$slide_play_detail_framework_release_I:DispatchBaseElement_T:SendDanmakuHighPriorityNewElement	18.0		1469469617678050.0
284602	374531	K_attached$slide_play_detail_framework_release_I:DispatchBaseElement_T:QuickCommentElement	18.0		1469469618022529.0
284625	488750	K_attached$slide_play_detail_framework_release_I:BaseElement_T:ShootWithHimElement	18.0		1469469618445810.0
284641	205938	K_attached$slide_play_detail_framework_release_I:DispatchBaseElement_T:QuickShareElement	18.0		1469469618963102.0
284647	281875	K_attached$slide_play_detail_framework_release_I:DispatchBaseElement_T:SendDanmakuLowPriorityNewElement	18.0		1469469619197633.0
284666	13267031	K_onElementShow_I:BaseSendDanmakuNewElement_T:SendDanmakuLowPriorityNewElement	18.0		1469469619751748.0
284670	7454427	K_logDanmakuInputShow_I:NasaDanmakuLogger_T:NasaDanmakuLogger	19.0		1469469619918310.0
284680	667292	K_getContentPackage_I:NasaDanmakuLogger_T:NasaDanmakuLogger	20.0		1469469620139508.0
284766	3423073	K_logEvent_I:LogManager_T:LogManager--action: 0 content_package <   photo_package <     atlastype: 0     author_id: 1988932079 	20.0		1469469623891852.0
284847	5569584	K_logDanmakuInputCloseBtnShow_I:NasaDanmakuLogger_T:NasaDanmakuLogger	19.0		1469469627440810.0
284848	670885	K_getContentPackage_I:NasaDanmakuLogger_T:NasaDanmakuLogger	20.0		1469469627524821.0
284933	2051719	K_logEvent_I:LogManager_T:LogManager--action: 0 content_package <   photo_package <     atlastype: 0     author_id: 1988932079 	20.0		1469469630947008.0
284990	11271041	K_attached_I:BaseGroup_T:BottomGroup	17.0		1469469633361019.0
285041	9191615	K_onElementShow_I:BaseGeneralBottomEntryElement_T:GeneralBottomEntryElement	18.0		1469469634803258.0
285044	8728854	K_logShowEvent_I:BaseGeneralBottomEntryElement_T:GeneralBottomEntryElement	19.0		1469469634897008.0
285050	841563	K_getElementPackage$default_I:BaseGeneralBottomEntryElement_T:BaseGeneralBottomEntryElement	20.0		1469469635122685.0
285052	786146	K_getElementPackage_I:BaseGeneralBottomEntryElement_T:GeneralBottomEntryElement	21.0		1469469635163779.0
285081	641198	K_getContentPackage_I:BaseGeneralBottomEntryElement_T:GeneralBottomEntryElement	20.0		1469469636040081.0
285250	1931250	K_logEvent_I:LogManager_T:LogManager--action: 0 area_package <   name: PHOTO_PLAY_FUNC   params:  > content_package <   pho	20.0		1469469641673623.0
285267	302708	K_preloadMiniAppIfNeed_I:BaseGeneralBottomEntryElement_T:GeneralBottomEntryElement	19.0		1469469643680967.0
285289	1098750	K_attached_I:BaseGroup_T:MiddleGroup	17.0		1469469644779612.0
285294	499896	K_attached$slide_play_detail_framework_release_I:DispatchBaseElement_T:AtlasPlaySegmentedProgressElement	18.0		1469469645005810.0
285295	375468	K_isDispatchEnable_I:DispatchBaseElement_T:AtlasPlaySegmentedProgressElement	19.0		1469469645035342.0
285322	580938	K_attached_I:BaseGroup_T:CenterEntranceGroup	17.0		1469469646023310.0
285348	47157396	K_attached_I:BaseGroup_T:RightActionBarGroup	17.0		1469469646739977.0
285380	307343	K_attached$slide_play_detail_framework_release_I:DispatchBaseElement_T:AvatarAndFollowElement	18.0		1469469647309040.0
285420	232188	K_attached$slide_play_detail_framework_release_I:DispatchBaseElement_T:RewardElement	18.0		1469469647887581.0
285437	281302	K_attached$slide_play_detail_framework_release_I:DispatchBaseElement_T:LikeElement	18.0		1469469648141696.0
285468	2895885	K_attached$slide_play_detail_framework_release_I:DispatchBaseElement_T:CommentElement	18.0		1469469648619352.0
285483	2660208	K_onAttached_I:CommentElement$8_T:	19.0		1469469648844821.0
285487	2611979	K_bindShowCommentAnimListenerOnAttach_I:CommentElement_T:CommentElement	20.0		1469469648883102.0
285505	387187	K_onAttachedStateChanged_I:GrowthShowCommentPluginImpl_T:GrowthShowCommentPluginImpl	21.0		1469469649240394.0
285510	286042	K_onPhotoAttached_I:GrowthShowCommentManager_T:GrowthShowCommentManager	22.0		1469469649332998.0
285535	1230625	K_lambda$bindShowCommentAnimListenerOnAttach$13_I:CommentElement_T:CommentElement	21.0		1469469649879769.0
285541	661667	K_<init>_I:KwaiPlayerPlayOrNotAysncHelper_T:KwaiPlayerPlayOrNotAysncHelper	22.0		1469469649988154.0
285584	317552	K_onVideoPlayStateChanged_I:GrowthShowCommentPluginImpl_T:GrowthShowCommentPluginImpl	22.0		1469469650784769.0
285586	261615	K_onVideoPlayStateChanged_I:GrowthShowCommentManager_T:GrowthShowCommentManager	23.0		1469469650832425.0
285630	14648906	K_attached$slide_play_detail_framework_release_I:DispatchBaseElement_T:CollectElement	18.0		1469469651748154.0
285638	14299687	K_onAttached_I:DefaultPageAttachChangedListener_T:	19.0		1469469652085967.0
285639	14230052	K_onAttached_I:CollectElement$onBind$4_T:	20.0		1469469652142008.0
285645	13338802	K_lambda$initLogDelegate$0_I:DebugLogger_T:DebugLogger	21.0		1469469652546123.0
285646	13030104	monitor contention with owner ChromiumNet (76) at boolean android.os.MessageQueue.enqueueMessage(android.os.Message			22.0
285647	12992761	Lock contention on a monitor lock (owner tid: 27907)	23.0		1469469652702112.0
285963	7787239	K_onElementShow_I:CollectElement_T:CollectElement	18.0		1469469666696019.0
285968	7703750	K_logCollectShow_I:CollectElement_T:CollectElement	19.0		1469469666771227.0
286135	2886928	K_logEvent_I:LogManager_T:LogManager--action: 0 content_package <   photo_package <     atlastype: 0     author_id: 1988932079 	20.0		1469469671574664.0
286196	280157	K_attached$slide_play_detail_framework_release_I:DispatchBaseElement_T:ShareElement	18.0		1469469674516487.0
286217	632865	K_attached$slide_play_detail_framework_release_I:BaseElement_T:MusicWheelElement	18.0		1469469675080706.0
286223	302812	K_log_I:SlidePlayMonitorLogger_T:SlidePlayMonitorLogger	19.0		1469469675236852.0
286244	17744948	K_onElementShow_I:MusicWheelElement_T:MusicWheelElement	18.0		1469469675910654.0
286269	17241406	K_run_I:MusicWheelElement$onElementShow$1_T:	19.0		1469469676407529.0
286275	348333	K_getLogParam_I:MusicWheelElement_T:MusicWheelElement	20.0		1469469676485967.0
286276	262812	K_allowExtend_I:MusicWheelElement_T:MusicWheelElement	21.0		1469469676540550.0
286317	15945365	K_logShowEvent_I:DetailPhotoLogPresenter$2_T:	20.0		1469469677695185.0
286340	15234323	K_apply_I:MusicWheelElement$onElementShow$1$logParam$1_T:	21.0		1469469678267060.0
286341	15207760	K_apply_I:MusicWheelElement$onElementShow$1$logParam$1_T:	22.0		1469469678286852.0
286352	14673541	Lock contention on Region lock (owner tid: 27818)	23.0		1469469678484769.0
286853	401198	K_attached_I:BaseGroup_T:PlayControllerGroup	17.0		1469469693989873.0
286882	367760	K_attached_I:BaseSingleElementGroup_T:EmptyPhotoGroup	17.0		1469469694733623.0
286883	342344	K_attached$slide_play_detail_framework_release_I:DispatchBaseElement_T:EmptyPhotoNoteElement	18.0		1469469694749560.0
286890	221510	K_onAttached_I:PageAttachChangedListener_T:	19.0		1469469694864144.0
286922	1212500	K_attached_I:BaseGroup_T:FullScreenLayerGroup	17.0		1469469695389925.0
286961	259167	K_attached$slide_play_detail_framework_release_I:BaseElement_T:PayCourseTrailFinishElement	18.0		1469469696123310.0
287003	715156	K_attached_I:BaseGroup_T:DanmakuGroup	17.0		1469469696879873.0
287047	621927	K_attached_I:BaseGroup_T:TopRightGroup	17.0		1469469697660550.0
287059	255156	K_attached$slide_play_detail_framework_release_I:BaseElement_T:SaveTrafficReminderElement	18.0		1469469697937998.0
287081	831511	K_attached_I:BaseGroup_T:PlcStrongGroup	17.0		1469469698356539.0
287084	527968	K_attached$slide_play_detail_framework_release_I:DispatchBaseElement_T:SingleStrongStyleElement	18.0		1469469698484821.0
287089	371875	K_onAttached_I:DefaultPageAttachChangedListener_T:	19.0		1469469698633154.0
287090	332917	K_onAttached_I:BasePlcStrongStyleElement$1_T:	20.0		1469469698665810.0
287091	303125	K_onPageAttached_I:SingleStrongStyleElement_T:SingleStrongStyleElement	21.0		1469469698689612.0
287094	201198	K_onPageAttached_I:SingleStrongViewModel_T:SingleStrongViewModel	22.0		1469469698785550.0
287106	709531	K_attached_I:BaseGroup_T:BottomPanelGroup	17.0		1469469699285706.0
287109	205208	K_attached$slide_play_detail_framework_release_I:DispatchBaseElement_T:SurveyElement	18.0		1469469699452477.0
287129	274219	K_performTrackStage_I:DetailSlidePlayFragment_T:NasaGrootDetailVMFragment	15.0		1469469700237529.0
287156	1291250	K_notifyAttached_I:StickyMilanoAttachChangedListenerManager_T:StickyMilanoAttachChangedListenerManager	10.0		1469469701340289.0
287160	210938	K_attached_I:FeaturedPoorNetworkVideoPlayOptPresenter$2_T:	11.0		1469469701444508.0
287177	717656	K_attached_I:MilanoProfileFeedAvatarPresenter$1_T:	11.0		1469469701812998.0
287178	672396	K_updateProfileFeedTitle_I:MilanoProfileFeedAvatarPresenter_T:MilanoProfileFeedAvatarPresenter	12.0		1469469701850237.0
287219	304479	K_didAppear_I:SlideUserProcessLoggerPresenter$1_T:	10.0		1469469702754248.0
287221	237344	K_updateWatchId_I:SlideUserProcessLoggerPresenter_T:SlideUserProcessLoggerPresenter	11.0		1469469702816123.0
287234	520261	K_didAppear_I:FeaturedDiscardPrefetchVideoPresenter$1_T:	10.0		1469469703078206.0
287301	82831145	traversal	3.0		1469469704066019.0
287304	48578386	measure	4.0		1469469704117581.0
287305	48498594	K_onMeasure_I:KwaiSlidingPaneLayout_T:KwaiSlidingPaneLayout	5.0		1469469704178310.0
287306	48474636	K_onMeasure_I:SlidingPaneLayout_T:KwaiSlidingPaneLayout	6.0		1469469704195289.0
287308	48435416	K_onMeasure_I:ScrollStrategyViewPager_T:ScrollStrategyViewPager	7.0		1469469704217321.0
287309	48413385	K_onMeasure_I:ViewPager_T:ScrollStrategyViewPager	8.0		1469469704232529.0
287312	48268282	K_onMeasure_I:ConstraintLayout_T:ConstraintLayout	9.0		1469469704367685.0
287323	31188177	K_onMeasure_I:KwaiGrootViewPager_T:KwaiGrootViewPager	10.0		1469469704573675.0
287324	31163541	K_onMeasure_I:VerticalViewPager_T:KwaiGrootViewPager	11.0		1469469704591748.0
287525	680104	K_onMeasure_I:AvatarWithFollowLiving_T:AvatarWithFollowLiving	12.0		1469469708724404.0
287529	449167	K_onMeasure_I:AvatarWithFollow_T:AvatarWithFollow	13.0		1469469708873206.0
287558	317291	K_onMeasure_I:ConstraintLayout_T:ConstraintLayout	12.0		1469469709715446.0
287575	232656	K_onMeasure_I:AppCompatTextView_T:AppCompatTextView	12.0		1469469710242425.0
287592	3093125	K_onMeasure_I:AppCompatTextView_T:AppCompatTextView	12.0		1469469710497633.0
287696	2851407	K_onMeasure_I:UserNameLinearLayout_T:UserNameLinearLayout	12.0		1469469713939039.0
287699	2719375	K_onMeasure_I:PriorityLinearLayout_T:UserNameLinearLayout	13.0		1469469714061331.0
287700	1328021	K_generateMeasureList_I:PriorityLinearLayout_T:UserNameLinearLayout	14.0		1469469714085602.0
287808	3444896	K_onMeasure_I:ConstraintLayout_T:ConstraintLayout	12.0		1469469716933258.0
287814	2962864	K_onMeasure_I:CaptionTextView_T:CaptionTextView	13.0		1469469717096123.0
287816	2915781	K_onMeasure_I:AppCompatTextView_T:CaptionTextView	14.0		1469469717123258.0
287963	802552	K_onMeasure_I:MusicLabelLayout_T:MusicLabelLayout	12.0		1469469720432581.0
287965	770156	K_onMeasure_I:PriorityLinearLayout_T:MusicLabelLayout	13.0		1469469720456279.0
287968	325573	K_generateMeasureList_I:PriorityLinearLayout_T:MusicLabelLayout	14.0		1469469720484925.0
288010	3093229	K_onMeasure_I:UserNameLinearLayout_T:UserNameLinearLayout	12.0		1469469722007529.0
288012	3060260	K_onMeasure_I:PriorityLinearLayout_T:UserNameLinearLayout	13.0		1469469722034144.0
288013	1533698	K_generateMeasureList_I:PriorityLinearLayout_T:UserNameLinearLayout	14.0		1469469722054039.0
288090	209427	K_getChildAt_I:PriorityLinearLayout_T:UserNameLinearLayout	14.0		1469469724700133.0
288097	853802	K_onMeasure_I:ConstraintLayout_T:ConstraintLayout	12.0		1469469725120237.0
288098	706771	K_onMeasure_I:CaptionTextView_T:CaptionTextView	13.0		1469469725177060.0
288099	680521	K_onMeasure_I:AppCompatTextView_T:CaptionTextView	14.0		1469469725194404.0
288101	477969	K_onMeasure_I:MusicLabelLayout_T:MusicLabelLayout	12.0		1469469725999664.0
288102	455521	K_onMeasure_I:PriorityLinearLayout_T:MusicLabelLayout	13.0		1469469726015914.0
288121	554218	K_onMeasure_I:AvatarWithFollowLiving_T:AvatarWithFollowLiving	12.0		1469469726513519.0
288124	401302	K_onMeasure_I:AvatarWithFollow_T:AvatarWithFollow	13.0		1469469726609092.0
288130	219740	K_getAvatarBottomMargin_I:RightActionBarAdaptUtils_T:RightActionBarAdaptUtils	14.0		1469469726780862.0
288146	499323	K_onMeasure_I:AvatarWithFollowLiving_T:AvatarWithFollowLiving	12.0		1469469727549404.0
288150	345000	K_onMeasure_I:AvatarWithFollow_T:AvatarWithFollow	13.0		1469469727634508.0
288178	1719010	K_onMeasure_I:UserNameLinearLayout_T:UserNameLinearLayout	12.0		1469469728604873.0
288179	1694792	K_onMeasure_I:PriorityLinearLayout_T:UserNameLinearLayout	13.0		1469469728622529.0
288180	788594	K_generateMeasureList_I:PriorityLinearLayout_T:UserNameLinearLayout	14.0		1469469728643727.0
288269	696875	K_onMeasure_I:ConstraintLayout_T:ConstraintLayout	12.0		1469469730344248.0
288271	566406	K_onMeasure_I:CaptionTextView_T:CaptionTextView	13.0		1469469730396592.0
288272	536146	K_onMeasure_I:AppCompatTextView_T:CaptionTextView	14.0		1469469730419039.0
288280	462396	K_onMeasure_I:MusicLabelLayout_T:MusicLabelLayout	12.0		1469469731064664.0
288281	438906	K_onMeasure_I:PriorityLinearLayout_T:MusicLabelLayout	13.0		1469469731081904.0
288310	1754271	K_onMeasure_I:UserNameLinearLayout_T:UserNameLinearLayout	12.0		1469469731797998.0
288311	1726770	K_onMeasure_I:PriorityLinearLayout_T:UserNameLinearLayout	13.0		1469469731818467.0
288312	853802	K_generateMeasureList_I:PriorityLinearLayout_T:UserNameLinearLayout	14.0		1469469731833779.0
288424	779375	K_onMeasure_I:ConstraintLayout_T:ConstraintLayout	12.0		1469469733570914.0
288427	646718	K_onMeasure_I:CaptionTextView_T:CaptionTextView	13.0		1469469733625342.0
288429	617709	K_onMeasure_I:AppCompatTextView_T:CaptionTextView	14.0		1469469733642789.0
288443	472032	K_onMeasure_I:MusicLabelLayout_T:MusicLabelLayout	12.0		1469469734372112.0
288444	449167	K_onMeasure_I:PriorityLinearLayout_T:MusicLabelLayout	13.0		1469469734388102.0
288467	466198	K_onMeasure_I:AvatarWithFollowLiving_T:AvatarWithFollowLiving	12.0		1469469734877685.0
288470	322604	K_onMeasure_I:AvatarWithFollow_T:AvatarWithFollow	13.0		1469469734964352.0
288497	16685625	K_onMeasure_I:KwaiGrootViewPager_T:KwaiGrootViewPager	10.0		1469469735900914.0
288498	16659896	K_onMeasure_I:VerticalViewPager_T:KwaiGrootViewPager	11.0		1469469735919508.0
288511	478802	K_onMeasure_I:AvatarWithFollowLiving_T:AvatarWithFollowLiving	12.0		1469469736289352.0
288515	326979	K_onMeasure_I:AvatarWithFollow_T:AvatarWithFollow	13.0		1469469736370498.0
288540	1760833	K_onMeasure_I:UserNameLinearLayout_T:UserNameLinearLayout	12.0		1469469737288154.0
288542	1723021	K_onMeasure_I:PriorityLinearLayout_T:UserNameLinearLayout	13.0		1469469737319612.0
288543	899687	K_generateMeasureList_I:PriorityLinearLayout_T:UserNameLinearLayout	14.0		1469469737334873.0
288627	716041	K_onMeasure_I:ConstraintLayout_T:ConstraintLayout	12.0		1469469739068519.0
288629	585834	K_onMeasure_I:CaptionTextView_T:CaptionTextView	13.0		1469469739122008.0
288631	551406	K_onMeasure_I:AppCompatTextView_T:CaptionTextView	14.0		1469469739148519.0
288634	461823	K_onMeasure_I:MusicLabelLayout_T:MusicLabelLayout	12.0		1469469739807946.0
288635	432344	K_onMeasure_I:PriorityLinearLayout_T:MusicLabelLayout	13.0		1469469739831748.0
288658	1757240	K_onMeasure_I:UserNameLinearLayout_T:UserNameLinearLayout	12.0		1469469740524039.0
288659	1730469	K_onMeasure_I:PriorityLinearLayout_T:UserNameLinearLayout	13.0		1469469740541904.0
288660	808854	K_generateMeasureList_I:PriorityLinearLayout_T:UserNameLinearLayout	14.0		1469469740556383.0
288737	675260	K_onMeasure_I:ConstraintLayout_T:ConstraintLayout	12.0		1469469742299352.0
288740	553698	K_onMeasure_I:CaptionTextView_T:CaptionTextView	13.0		1469469742348310.0
288742	530521	K_onMeasure_I:AppCompatTextView_T:CaptionTextView	14.0		1469469742363935.0
288754	436927	K_onMeasure_I:MusicLabelLayout_T:MusicLabelLayout	12.0		1469469742995706.0
288755	416042	K_onMeasure_I:PriorityLinearLayout_T:MusicLabelLayout	13.0		1469469743010862.0
288772	488229	K_onMeasure_I:AvatarWithFollowLiving_T:AvatarWithFollowLiving	12.0		1469469743469717.0
288775	342447	K_onMeasure_I:AvatarWithFollow_T:AvatarWithFollow	13.0		1469469743560342.0
288796	425416	K_onMeasure_I:AvatarWithFollowLiving_T:AvatarWithFollowLiving	12.0		1469469744371071.0
288799	294062	K_onMeasure_I:AvatarWithFollow_T:AvatarWithFollow	13.0		1469469744447373.0
288822	1764844	K_onMeasure_I:UserNameLinearLayout_T:UserNameLinearLayout	12.0		1469469745282581.0
288824	1740625	K_onMeasure_I:PriorityLinearLayout_T:UserNameLinearLayout	13.0		1469469745300602.0
288827	893281	K_generateMeasureList_I:PriorityLinearLayout_T:UserNameLinearLayout	14.0		1469469745315342.0
288902	730416	K_onMeasure_I:ConstraintLayout_T:ConstraintLayout	12.0		1469469747064821.0
288904	597396	K_onMeasure_I:CaptionTextView_T:CaptionTextView	13.0		1469469747121748.0
288905	554688	K_onMeasure_I:AppCompatTextView_T:CaptionTextView	14.0		1469469747156487.0
288911	445729	K_onMeasure_I:MusicLabelLayout_T:MusicLabelLayout	12.0		1469469747818675.0
288912	423594	K_onMeasure_I:PriorityLinearLayout_T:MusicLabelLayout	13.0		1469469747834925.0
288932	1669635	K_onMeasure_I:UserNameLinearLayout_T:UserNameLinearLayout	12.0		1469469748522998.0
288933	1646718	K_onMeasure_I:PriorityLinearLayout_T:UserNameLinearLayout	13.0		1469469748539092.0
288934	732552	K_generateMeasureList_I:PriorityLinearLayout_T:UserNameLinearLayout	14.0		1469469748553935.0
289008	676719	K_onMeasure_I:ConstraintLayout_T:ConstraintLayout	12.0		1469469750220289.0
289009	545938	K_onMeasure_I:CaptionTextView_T:CaptionTextView	13.0		1469469750270185.0
289010	521458	K_onMeasure_I:AppCompatTextView_T:CaptionTextView	14.0		1469469750286592.0
289019	429323	K_onMeasure_I:MusicLabelLayout_T:MusicLabelLayout	12.0		1469469750919144.0
289020	407136	K_onMeasure_I:PriorityLinearLayout_T:MusicLabelLayout	13.0		1469469750935185.0
289038	467761	K_onMeasure_I:AvatarWithFollowLiving_T:AvatarWithFollowLiving	12.0		1469469751382581.0
289041	320416	K_onMeasure_I:AvatarWithFollow_T:AvatarWithFollow	13.0		1469469751466123.0
289058	433646	K_onMeasure_I:ConstraintLayout_T:ConstraintLayout	12.0		1469469751932008.0
289059	277656	Lock contention on thread suspend count lock (owner tid: 27882)	13.0		1469469751968050.0
289077	12543541	layout	4.0		1469469752834717.0
289078	12014479	K_onLayout_I:KwaiSlidingPaneLayout_T:KwaiSlidingPaneLayout	5.0		1469469752861071.0
289079	11983906	K_onLayout_I:SlidingPaneLayout_T:KwaiSlidingPaneLayout	6.0		1469469752882217.0
289080	11936094	K_onLayout_I:ViewPager_T:ScrollStrategyViewPager	7.0		1469469752906331.0
289081	11880990	K_onLayout_I:ConstraintLayout_T:ConstraintLayout	8.0		1469469752928310.0
289085	11733646	K_onLayout_I:KwaiGrootViewPager_T:KwaiGrootViewPager	9.0		1469469752974560.0
289086	11623489	K_onLayout_I:VerticalViewPager_T:KwaiGrootViewPager	10.0		1469469752997321.0
289091	948750	K_onLayoutChange_I:DefaultFrameViewModel_T:DefaultFrameViewModel	11.0		1469469753163362.0
289092	898750	K_onLayoutChange_I:PlayerContentPositionLocationPresenter$mOnLayoutChangeListener$1_T:	12.0		1469469753206071.0
289093	844844	K_emitPlayerContentViewPositionOnScreen_I:PlayerContentPositionLocationPresenter_T:PlayerContentPositionLocationPresenter	13.0		1469469753250498.0
289096	392604	K_lambda$beforeBindPage$18_I:NasaGrootDetailVMFragment_T:NasaGrootDetailVMFragment	14.0		1469469753605654.0
289097	365104	K_accept_I:LandscapeCenterEntranceElement$onBind$3_T:	15.0		1469469753626279.0
289098	341875	K_accept_I:LandscapeCenterEntranceElement$onBind$3_T:	16.0		1469469753642789.0
289099	311302	K_changeVerticalLocation_I:LandscapeCenterEntranceElement_T:LandscapeCenterEntranceElement	17.0		1469469753667112.0
289107	550990	K_onLayoutChange_I:PlayerContentPositionLocationPresenter$mOnLayoutChangeListener$1_T:	11.0		1469469754141487.0
289108	524427	K_emitPlayerContentViewPositionOnScreen_I:PlayerContentPositionLocationPresenter_T:PlayerContentPositionLocationPresenter	12.0		1469469754160081.0
289111	300677	K_lambda$beforeBindPage$18_I:NasaGrootDetailVMFragment_T:NasaGrootDetailVMFragment	13.0		1469469754271383.0
289112	274896	K_accept_I:LandscapeCenterEntranceElement$onBind$3_T:	14.0		1469469754289925.0
289113	250938	K_accept_I:LandscapeCenterEntranceElement$onBind$3_T:	15.0		1469469754306487.0
289114	216042	K_changeVerticalLocation_I:LandscapeCenterEntranceElement_T:LandscapeCenterEntranceElement	16.0		1469469754334300.0
289159	286615	K_onSizeChanged_I:PlayerKitContentFrame_T:PlayerKitContentFrame	11.0		1469469755341435.0
289165	568750	K_onLayoutChange_I:DefaultFrameViewModel_T:DefaultFrameViewModel	11.0		1469469755730394.0
289166	532604	K_onLayoutChange_I:PlayerContentPositionLocationPresenter$mOnLayoutChangeListener$1_T:	12.0		1469469755759092.0
289167	503854	K_emitPlayerContentViewPositionOnScreen_I:PlayerContentPositionLocationPresenter_T:PlayerContentPositionLocationPresenter	13.0		1469469755779248.0
289170	303490	K_lambda$beforeBindPage$18_I:NasaGrootDetailVMFragment_T:NasaGrootDetailVMFragment	14.0		1469469755887060.0
289171	265052	K_accept_I:LandscapeCenterEntranceElement$onBind$3_T:	15.0		1469469755919456.0
289172	243958	K_accept_I:LandscapeCenterEntranceElement$onBind$3_T:	16.0		1469469755935081.0
289173	220312	K_changeVerticalLocation_I:LandscapeCenterEntranceElement_T:LandscapeCenterEntranceElement	17.0		1469469755952842.0
289181	510677	K_onLayoutChange_I:PlayerContentPositionLocationPresenter$mOnLayoutChangeListener$1_T:	11.0		1469469756318779.0
289182	480625	K_emitPlayerContentViewPositionOnScreen_I:PlayerContentPositionLocationPresenter_T:PlayerContentPositionLocationPresenter	12.0		1469469756340602.0
289185	295521	K_lambda$beforeBindPage$18_I:NasaGrootDetailVMFragment_T:NasaGrootDetailVMFragment	13.0		1469469756444039.0
289186	272708	K_accept_I:LandscapeCenterEntranceElement$onBind$3_T:	14.0		1469469756460654.0
289187	250053	K_accept_I:LandscapeCenterEntranceElement$onBind$3_T:	15.0		1469469756477164.0
289188	225677	K_changeVerticalLocation_I:LandscapeCenterEntranceElement_T:LandscapeCenterEntranceElement	16.0		1469469756495602.0
289225	2433698	K_onLayout_I:AvatarWithFollowLiving_T:AvatarWithFollowLiving	11.0		1469469758254248.0
289239	1862135	K_onLayout_I:AvatarWithFollow_T:AvatarWithFollow	12.0		1469469758815967.0
289253	827083	Lock contention on InternTable lock (owner tid: 27882)	13.0		1469469759478623.0
289255	303229	K_onLayout_I:FollowButtonView_T:FollowButtonView	13.0		1469469760361175.0
289260	1202188	K_onSizeChanged_I:KwaiSizeAdjustableTextView_T:KwaiSizeAdjustableTextView	11.0		1469469761055081.0
289274	464531	K_onLayout_I:KwaiSizeAdjustableTextView_T:KwaiSizeAdjustableTextView	11.0		1469469762395446.0
289283	620364	K_onLayoutChange_I:InformationGroup$mLayoutChangeListener$1_T:	11.0		1469469763846748.0
289284	591875	K_forceLayout_I:LinearGroupLayout_T:LinearGroupLayout	12.0		1469469763867529.0
289313	1057760	K_onGlobalLayout_I:InformationGroup$mOnGlobalLayoutListener$1_T:	4.0		1469469765534196.0
289315	977083	K_notifyInformationPosition_I:InformationGroupEventBus_T:InformationGroupEventBus	5.0		1469469765607998.0
289316	939635	K_accept_I:BasePage$bindInner$$inlined$dispatchRun$lambda$16_T:	6.0		1469469765638102.0
289317	911927	K_accept_I:BasePage$bindInner$$inlined$dispatchRun$lambda$16_T:	7.0		1469469765658519.0
289321	630937	K_notifyInformationPosition_I:CenterEntranceGroupEventBus_T:CenterEntranceGroupEventBus	8.0		1469469765932425.0
289322	573281	K_accept_I:LandscapeCenterEntranceElement$onBind$4_T:	9.0		1469469765982425.0
289323	542760	K_accept_I:LandscapeCenterEntranceElement$onBind$4_T:	10.0		1469469766004977.0
289324	509271	K_changeVerticalLocation_I:LandscapeCenterEntranceElement_T:LandscapeCenterEntranceElement	11.0		1469469766031331.0
289344	274375	K_onGlobalLayout_I:CaptionElementView$2_T:	4.0		1469469766618831.0
289351	1117240	K_onGlobalLayout_I:InformationGroup$mOnGlobalLayoutListener$1_T:	4.0		1469469767010862.0
289353	1055208	K_notifyInformationPosition_I:InformationGroupEventBus_T:InformationGroupEventBus	5.0		1469469767065967.0
289354	1007709	K_accept_I:BasePage$bindInner$$inlined$dispatchRun$lambda$16_T:	6.0		1469469767106383.0
289355	980989	K_accept_I:BasePage$bindInner$$inlined$dispatchRun$lambda$16_T:	7.0		1469469767126019.0
289360	880052	K_notifyInformationPosition_I:CenterEntranceGroupEventBus_T:CenterEntranceGroupEventBus	8.0		1469469767219873.0
289361	848541	K_accept_I:LandscapeCenterEntranceElement$onBind$4_T:	9.0		1469469767243623.0
289363	821927	K_accept_I:LandscapeCenterEntranceElement$onBind$4_T:	10.0		1469469767263362.0
289364	790937	K_changeVerticalLocation_I:LandscapeCenterEntranceElement_T:LandscapeCenterEntranceElement	11.0		1469469767287269.0
289370	297240	monitor contention with owner ChromiumNet (76) at boolean android.os.MessageQueue.enqueueMessage(android.os.Message			12.0
289372	261094	Lock contention on a monitor lock (owner tid: 27907)	13.0		1469469767445706.0
289398	1395937	K_onGlobalLayout_I:InformationGroup$mOnGlobalLayoutListener$1_T:	4.0		1469469768301592.0
289400	1323021	K_notifyInformationPosition_I:InformationGroupEventBus_T:InformationGroupEventBus	5.0		1469469768367112.0
289401	1289531	K_accept_I:BasePage$bindInner$$inlined$dispatchRun$lambda$16_T:	6.0		1469469768393154.0
289402	1261406	K_accept_I:BasePage$bindInner$$inlined$dispatchRun$lambda$16_T:	7.0		1469469768413623.0
289406	1161511	K_notifyInformationPosition_I:CenterEntranceGroupEventBus_T:CenterEntranceGroupEventBus	8.0		1469469768506539.0
289411	1127032	K_accept_I:LandscapeCenterEntranceElement$onBind$4_T:	9.0		1469469768533414.0
289416	1100521	K_accept_I:LandscapeCenterEntranceElement$onBind$4_T:	10.0		1469469768552998.0
289424	1055156	K_changeVerticalLocation_I:LandscapeCenterEntranceElement_T:LandscapeCenterEntranceElement	11.0		1469469768591123.0
289441	482448	K_getLandscapeEntrancePositionOpt_I:NasaExperimentUtils_T:NasaExperimentUtils	12.0		1469469768787269.0
289476	16773698	draw	4.0		1469469770104248.0
289479	14824323	Record View#draw()	5.0		1469469770151279.0
289516	265312	K_onDraw_I:KwaiImageView_T:KwaiImageView	6.0		1469469771963571.0
289517	221875	K_onDraw_I:KwaiBindableImageView_T:KwaiImageView	7.0		1469469771989039.0
289555	9051667	K_onSurfaceTextureAvailable_I:SurfaceTextureProxy_T:SurfaceTextureProxy	6.0		1469469773741435.0
289556	8058490	K_onSurfaceTextureAvailable_I:TextureViewSurfaceUpdateHelper_T:TextureViewSurfaceUpdateHelper	7.0		1469469773944039.0
289557	8015625	K_doSetPlayerIfCan_I:TextureViewSurfaceUpdateHelper_T:TextureViewSurfaceUpdateHelper	8.0		1469469773976956.0
289558	284479	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	9.0		1469469774136696.0
289807	296198	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	9.0		1469469781438519.0
289832	673646	K_onSurfaceTextureAvailable_I:PlayerKitContentFrame$3_T:PlayerKitContentFrame$3	7.0		1469469782111487.0
289833	633230	K_quickRunOnUiThread_I:ThreadUtils_T:ThreadUtils	8.0		1469469782144039.0
289839	548333	K_lambda$onSurfaceTextureAvailable$0_I:PlayerKitContentFrame$3_T:PlayerKitContentFrame$3	9.0		1469469782221852.0
289847	234062	K_onSurfaceTextureSizeChanged_I:PlayerKitContentFrame$3_T:PlayerKitContentFrame$3	10.0		1469469782528727.0
