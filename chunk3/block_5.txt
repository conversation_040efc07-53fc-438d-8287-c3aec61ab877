id	dur	name	depth	thread_dur	ts
291832	22837968	android.os.Handler: com.kwai.plugin.dva.feature.core.loader.FeaturePluginLoader$$ExternalSyntheticLambda0	0.0		1469469824895342.0
291837	830000	LoadApkAssets(/data/user/0/com.smile.gifmaker/app_DvaPlugin/daenerysextend/531431635/daenerysextend_531431635.apk)	1.0		1469469825256227.0
291869	631458	AssetManager::SetApkAssets	1.0		1469469826106279.0
291904	20593646	Lock contention on thread list lock (owner tid: 27900)	1.0		1469469827111383.0
292639	23013750	android.os.Handler: io.reactivex.android.schedulers.HandlerScheduler$ScheduledRunnable	0.0		1469469856312737.0
292643	22950625	K_run_I:HandlerScheduler$ScheduledRunnable_T:ScheduledRunnable	1.0		1469469856365133.0
292645	22891927	K_run_I:ObservableSubscribeOn$SubscribeTask_T:SubscribeTask	2.0		1469469856410654.0
292647	22746146	K_lambda$onLaunchFinish$0_I:SmartAlbumModule_T:SmartAlbumModule	3.0		1469469856479925.0
292649	13754480	Lock contention on CHA lock (owner tid: 27976)	4.0		1469469856573414.0
293017	8294635	Lock contention on InternTable lock (owner tid: 27900)	4.0		1469469870413571.0
293287	285781	K_lambda$initLogDelegate$0_I:DebugLogger_T:DebugLogger	4.0		1469469878899300.0
293739	26438698	com.kwai.video.aemonplayer.AemonMediaPlayer$EventHandler: #200	0.0		1469469892880237.0
293741	203855	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	1.0		1469469893031591.0
293746	26002032	K_lambda$attachListeners$8_I:_2_AbstractPlayerListenerDelegate_T:WaynePlayer	1.0		1469469893295237.0
293949	244844	K_lambda$new$1_I:SlideVideoCoverPresenter_T:SlideVideoCoverPresenter	2.0		1469469918225602.0
295546	77270208	android.view.Choreographer$FrameHandler: android.view.Choreographer$FrameDisplayEventReceiver	0.0		1469469980212269.0
295547	77208906	Choreographer#doFrame 13432470	1.0		1469469980260810.0
295548	77139115	Choreographer#skippedFrames 1	2.0		1469469980320966.0
295549	872396	animation	3.0		1469469980370185.0
295562	211041	K_lambda$showAvatarBorder$21_I:AvatarAndFollowBaseElementView_T:AvatarAndFollowElementView	4.0		1469469980696123.0
295570	76145365	traversal	3.0		1469469981265654.0
295571	26586354	measure	4.0		1469469981353206.0
295573	26450208	K_onMeasure_I:KwaiSlidingPaneLayout_T:KwaiSlidingPaneLayout	5.0		1469469981451800.0
295574	26396041	K_onMeasure_I:SlidingPaneLayout_T:KwaiSlidingPaneLayout	6.0		1469469981492425.0
295575	26318698	K_onMeasure_I:ScrollStrategyViewPager_T:ScrollStrategyViewPager	7.0		1469469981534352.0
295576	26284532	K_onMeasure_I:ViewPager_T:ScrollStrategyViewPager	8.0		1469469981555862.0
295577	25739167	K_onMeasure_I:ConstraintLayout_T:ConstraintLayout	9.0		1469469982080810.0
295583	12877187	K_onMeasure_I:KwaiGrootViewPager_T:KwaiGrootViewPager	10.0		1469469982487373.0
295584	12826615	K_onMeasure_I:VerticalViewPager_T:KwaiGrootViewPager	11.0		1469469982523414.0
295598	4406302	K_onMeasure_I:UserNameLinearLayout_T:UserNameLinearLayout	12.0		1469469983304769.0
295599	3913333	K_onMeasure_I:PriorityLinearLayout_T:UserNameLinearLayout	13.0		1469469983362321.0
295600	1194948	K_generateMeasureList_I:PriorityLinearLayout_T:UserNameLinearLayout	14.0		1469469983385081.0
295670	307136	K_getChildAt_I:PriorityLinearLayout_T:UserNameLinearLayout	14.0		1469469985760914.0
295672	1135782	K_onMeasure_I:AppCompatTextView_T:AppCompatTextView	14.0		1469469986106539.0
295775	3109688	K_onMeasure_I:UserNameLinearLayout_T:UserNameLinearLayout	12.0		1469469987828883.0
295777	2976510	K_onMeasure_I:PriorityLinearLayout_T:UserNameLinearLayout	13.0		1469469987857373.0
295779	1410052	K_generateMeasureList_I:PriorityLinearLayout_T:UserNameLinearLayout	14.0		1469469987879456.0
295900	264584	K_onMeasure_I:AppCompatTextView_T:AppCompatTextView	14.0		1469469990537060.0
295907	2243542	K_onMeasure_I:UserNameLinearLayout_T:UserNameLinearLayout	12.0		1469469991075810.0
295908	2165729	K_onMeasure_I:PriorityLinearLayout_T:UserNameLinearLayout	13.0		1469469991106487.0
295910	1231041	K_generateMeasureList_I:PriorityLinearLayout_T:UserNameLinearLayout	14.0		1469469991156019.0
295921	287812	Lock contention on thread suspend count lock (owner tid: 28394)	15.0		1469469991477894.0
296006	1940938	K_onMeasure_I:UserNameLinearLayout_T:UserNameLinearLayout	12.0		1469469993371331.0
296007	1874115	K_onMeasure_I:PriorityLinearLayout_T:UserNameLinearLayout	13.0		1469469993389091.0
296009	836562	K_generateMeasureList_I:PriorityLinearLayout_T:UserNameLinearLayout	14.0		1469469993405654.0
296110	12158437	K_onMeasure_I:KwaiGrootViewPager_T:KwaiGrootViewPager	10.0		1469469995531123.0
296111	12128958	K_onMeasure_I:VerticalViewPager_T:KwaiGrootViewPager	11.0		1469469995548102.0
296122	1906823	K_onMeasure_I:UserNameLinearLayout_T:UserNameLinearLayout	12.0		1469469995795133.0
296123	1842708	K_onMeasure_I:PriorityLinearLayout_T:UserNameLinearLayout	13.0		1469469995811904.0
296124	777968	K_generateMeasureList_I:PriorityLinearLayout_T:UserNameLinearLayout	14.0		1469469995828623.0
296217	1855677	K_onMeasure_I:UserNameLinearLayout_T:UserNameLinearLayout	12.0		1469469997741383.0
296218	1795052	K_onMeasure_I:PriorityLinearLayout_T:UserNameLinearLayout	13.0		1469469997758727.0
296220	779583	K_generateMeasureList_I:PriorityLinearLayout_T:UserNameLinearLayout	14.0		1469469997862269.0
296312	1857812	K_onMeasure_I:UserNameLinearLayout_T:UserNameLinearLayout	12.0		1469469999701227.0
296313	1783906	K_onMeasure_I:PriorityLinearLayout_T:UserNameLinearLayout	13.0		1469469999723154.0
296314	805053	K_generateMeasureList_I:PriorityLinearLayout_T:UserNameLinearLayout	14.0		1469469999739091.0
296428	5953125	K_onMeasure_I:UserNameLinearLayout_T:UserNameLinearLayout	12.0		1469470001628310.0
296429	5824636	K_onMeasure_I:PriorityLinearLayout_T:UserNameLinearLayout	13.0		1469470001648987.0
296431	4202187	K_generateMeasureList_I:PriorityLinearLayout_T:UserNameLinearLayout	14.0		1469470001663623.0
296648	258854	K_onMeasure_I:AppCompatTextView_T:AppCompatTextView	14.0		1469470007186227.0
296697	4917760	layout	4.0		1469470007959404.0
296700	4442344	K_onLayout_I:KwaiSlidingPaneLayout_T:KwaiSlidingPaneLayout	5.0		1469470008009352.0
296702	4409062	K_onLayout_I:SlidingPaneLayout_T:KwaiSlidingPaneLayout	6.0		1469470008033519.0
296705	4343906	K_onLayout_I:ViewPager_T:ScrollStrategyViewPager	7.0		1469470008076019.0
296706	4271563	K_onLayout_I:ConstraintLayout_T:ConstraintLayout	8.0		1469470008118831.0
296714	4101459	K_onLayout_I:KwaiGrootViewPager_T:KwaiGrootViewPager	9.0		1469470008231487.0
296716	4018750	K_onLayout_I:VerticalViewPager_T:KwaiGrootViewPager	10.0		1469470008257216.0
296755	245469	LoadApkAssets(/data/user/0/com.smile.gifmaker/app_DvaPlugin/commercial_night_watch/-1002726027/commercial_night_watch_-1002726027.apk)	11.0		1469470009518102.0
296772	1585782	AssetManager::SetApkAssets	11.0		1469470009857789.0
296910	895625	K_onGlobalLayout_I:InformationGroup$mOnGlobalLayoutListener$1_T:	4.0		1469470013030081.0
296914	827500	K_notifyInformationPosition_I:InformationGroupEventBus_T:InformationGroupEventBus	5.0		1469470013090706.0
296915	790990	K_accept_I:BasePage$bindInner$$inlined$dispatchRun$lambda$16_T:	6.0		1469470013119091.0
296918	756406	K_accept_I:BasePage$bindInner$$inlined$dispatchRun$lambda$16_T:	7.0		1469470013145758.0
296925	648073	K_notifyInformationPosition_I:CenterEntranceGroupEventBus_T:CenterEntranceGroupEventBus	8.0		1469470013242685.0
296926	504323	K_accept_I:LandscapeCenterEntranceElement$onBind$4_T:	9.0		1469470013268987.0
296929	476875	K_accept_I:LandscapeCenterEntranceElement$onBind$4_T:	10.0		1469470013289248.0
296931	443958	K_changeVerticalLocation_I:LandscapeCenterEntranceElement_T:LandscapeCenterEntranceElement	11.0		1469470013314821.0
296958	737812	K_onGlobalLayout_I:InformationGroup$mOnGlobalLayoutListener$1_T:	4.0		1469470014237946.0
296960	668281	K_notifyInformationPosition_I:InformationGroupEventBus_T:InformationGroupEventBus	5.0		1469470014299925.0
296961	635677	K_accept_I:BasePage$bindInner$$inlined$dispatchRun$lambda$16_T:	6.0		1469470014324769.0
296962	608698	K_accept_I:BasePage$bindInner$$inlined$dispatchRun$lambda$16_T:	7.0		1469470014344300.0
296965	492813	K_notifyInformationPosition_I:CenterEntranceGroupEventBus_T:CenterEntranceGroupEventBus	8.0		1469470014448935.0
296966	459115	K_accept_I:LandscapeCenterEntranceElement$onBind$4_T:	9.0		1469470014473779.0
296967	432135	K_accept_I:LandscapeCenterEntranceElement$onBind$4_T:	10.0		1469470014493519.0
296968	394636	K_changeVerticalLocation_I:LandscapeCenterEntranceElement_T:LandscapeCenterEntranceElement	11.0		1469470014523466.0
296992	957916	K_onGlobalLayout_I:InformationGroup$mOnGlobalLayoutListener$1_T:	4.0		1469470015203050.0
296995	880260	K_notifyInformationPosition_I:InformationGroupEventBus_T:InformationGroupEventBus	5.0		1469470015273623.0
296997	845052	K_accept_I:BasePage$bindInner$$inlined$dispatchRun$lambda$16_T:	6.0		1469470015301279.0
296998	817968	K_accept_I:BasePage$bindInner$$inlined$dispatchRun$lambda$16_T:	7.0		1469470015321071.0
297002	717083	K_notifyInformationPosition_I:CenterEntranceGroupEventBus_T:CenterEntranceGroupEventBus	8.0		1469470015414248.0
297004	684011	K_accept_I:LandscapeCenterEntranceElement$onBind$4_T:	9.0		1469470015439091.0
297005	650781	K_accept_I:LandscapeCenterEntranceElement$onBind$4_T:	10.0		1469470015465029.0
297006	618907	K_changeVerticalLocation_I:LandscapeCenterEntranceElement_T:LandscapeCenterEntranceElement	11.0		1469470015489612.0
297009	246823	K_getLandscapeEntrancePositionOpt_I:NasaExperimentUtils_T:NasaExperimentUtils	12.0		1469470015545341.0
297045	40966198	draw	4.0		1469470016405810.0
297049	21832031	Record View#draw()	5.0		1469470016451175.0
297064	19550625	scheduleTraversals	6.0		1469470016901591.0
297065	19357865	monitor contention with owner AnrAsyncSched (56) at void com.kwai.performance.stability.crash.monitor.anr.async.AsyncScheduleManager.run()(SourceFile:18) waiters=2 blocking from int android.os.MessageQueue.postSyncBarrier(long)(MessageQueue.java:483)	7.0		1469470017013258.0
297066	19329635	Lock contention on a monitor lock (owner tid: 27894)	8.0		1469470017029248.0
297810	416354	K_onSurfaceTextureUpdated_I:SurfaceTextureProxy_T:SurfaceTextureProxy	6.0		1469470036779456.0
297815	264427	K_onSurfaceTextureUpdated_I:PlayerKitContentFrame$3_T:PlayerKitContentFrame$3	7.0		1469470036923935.0
297818	212344	K_quickRunOnUiThread_I:ThreadUtils_T:ThreadUtils	8.0		1469470036968935.0
297848	386407	K_onDraw_I:KwaiImageView_T:KwaiImageView	6.0		1469470037580237.0
297849	341354	K_onDraw_I:KwaiBindableImageView_T:KwaiImageView	7.0		1469470037607581.0
309254	37111666	android.view.Choreographer$FrameHandler: android.view.Choreographer$FrameDisplayEventReceiver	0.0		1469470387882373.0
309265	37038281	Choreographer#doFrame 13432510	1.0		1469470387936904.0
309270	37008230	Choreographer#skippedFrames	2.0		1469470387952268.0
309274	768698	animation	3.0		1469470387998258.0
309285	615208	Choreographer#scheduleVsyncLocked	4.0		1469470388098883.0
309315	36119480	traversal	3.0		1469470388782893.0
309316	35976823	draw	4.0		1469470388856643.0
309317	21130417	Record View#draw()	5.0		1469470388887841.0
309379	7901458	Lock contention on Region lock (owner tid: 27818)	6.0		1469470390813623.0
309658	2254219	binder transaction	6.0		1469470402498570.0
309964	992292	K_onDraw_I:NasaFeaturedSeekBar_T:NasaFeaturedSeekBar	6.0		1469470408986018.0
312182	17686927	android.view.Choreographer$FrameHandler: android.view.Choreographer$FrameDisplayEventReceiver	0.0		1469470469000081.0
312186	6304167	Choreographer#doFrame 13432523	1.0		1469470469074612.0
312187	6244479	Choreographer#skippedFrames	2.0		1469470469126435.0
312190	1135990	animation	3.0		1469470469187216.0
312205	883750	Choreographer#scheduleVsyncLocked	4.0		1469470469385758.0
312242	4996041	traversal	3.0		1469470470339248.0
312249	4887708	draw	4.0		1469470470408987.0
312251	704270	Record View#draw()	5.0		1469470470439925.0
312262	287291	K_onSurfaceTextureUpdated_I:SurfaceTextureProxy_T:SurfaceTextureProxy	6.0		1469470470636800.0
312270	216719	K_onSurfaceTextureUpdated_I:PlayerKitContentFrame$3_T:PlayerKitContentFrame$3	7.0		1469470470697320.0
