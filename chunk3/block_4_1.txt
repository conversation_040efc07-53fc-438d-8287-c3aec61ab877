id	dur	name	depth	thread_dur	ts
195007	2087951406	android.view.Choreographer$FrameHandler: android.view.Choreographer$FrameDisplayEventReceiver	0.0		1469467698999092.0
195011	408541198	Choreographer#doFrame 13432312	1.0		1469467699044353.0
195012	408520156	Choreographer#skippedFrames	2.0		1469467699057165.0
195014	408482500	input	3.0		1469467699089509.0
195016	244479	processInputEventForCompatibility	4.0		1469467699132842.0
195038	7979010	deliverInputEvent src=0x1002 eventTimeNano=240399815183477 id=0xd91d952	4.0		1469467699437009.0
195043	7842135	ViewPostImeInputStage id=0xd91d952	5.0		1469467699502009.0
195063	7463750	K_dispatchTouchEvent_I:KwaiGrootViewPager_T:KwaiGrootViewPager	6.0		1469467699828832.0
195077	7193854	K_onTouchEvent_I:GrootTouchViewPager_T:KwaiGrootViewPager	7.0		1469467700085863.0
195080	295990	K_ignoreTouchEvent_I:GrootTouchViewPager_T:KwaiGrootViewPager	8.0		1469467700109040.0
195132	6611615	K_onTouchEvent_I:VerticalViewPager_T:KwaiGrootViewPager	8.0		1469467700661436.0
195144	290886	K_verifyScrollY_I:GrootTouchViewPager_T:KwaiGrootViewPager	9.0		1469467700779821.0
195160	200730	K_verifyScrollY_I:GrootTouchViewPager_T:KwaiGrootViewPager	9.0		1469467701167321.0
195176	307813	K_onPageScrolled_I:GrootViewPager$1_T:GrootViewPager$1	9.0		1469467701409613.0
195219	731302	K_onPageScrolled_I:NasaFeaturedAutoRefreshPresenter$2_T:	9.0		1469467702146019.0
195222	284635	K_getAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	10.0		1469467702166957.0
195246	367760	K_getRealPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	10.0		1469467702500186.0
195257	229010	K_getRealPositionInAdapter_I:PositionService_T:PositionService	11.0		1469467702633624.0
195259	200364	K_logInfo_I:GrootLogger_T:GrootLogger	12.0		1469467702655030.0
195273	399166	K_getRealPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	9.0		1469467702929301.0
195400	283021	K_getRealPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	9.0		1469467704114561.0
195462	789532	K_onPageScrolled_I:DraggingLayerPresenter$mPageChangeListener$1_T:	9.0		1469467704720446.0
195470	596042	K_getPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	10.0		1469467704788936.0
195477	469740	K_getPositionInAdapter_I:PositionService_T:PositionService	11.0		1469467704905863.0
195478	227761	K_getPositionInAdapter_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter	12.0		1469467704927946.0
195505	200104	K_logInfo_I:GrootLogger_T:GrootLogger	12.0		1469467705168363.0
195538	428072	K_onPageScrolled_I:CommentElement$7_T:	9.0		1469467705524249.0
195541	382552	K_setDanmakuTipBubbleDismiss_I:CommentViewModel_T:CommentViewModel	10.0		1469467705563519.0
195544	346667	K_setValue_I:KLiveData_T:KDispatchLiveData	11.0		1469467705592269.0
195545	329740	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	12.0		1469467705604144.0
195574	737448	K_onPageScrolled_I:DraggingLayerPresenter$mPageChangeListener$1_T:	9.0		1469467706011019.0
195578	568437	K_getPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	10.0		1469467706071176.0
195586	463541	K_getPositionInAdapter_I:PositionService_T:PositionService	11.0		1469467706171280.0
195589	297240	K_getPositionInAdapter_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter	12.0		1469467706190134.0
195626	430416	K_onPageScrolled_I:CommentElement$7_T:	9.0		1469467706765499.0
195630	383907	K_setDanmakuTipBubbleDismiss_I:CommentViewModel_T:CommentViewModel	10.0		1469467706804769.0
195631	339167	K_setValue_I:KLiveData_T:KDispatchLiveData	11.0		1469467706817113.0
195684	244583	processInputEventForCompatibility	4.0		1469467707751384.0
195695	7988698	deliverInputEvent src=0x1002 eventTimeNano=240399818207000 id=0x25d6ed18	4.0		1469467708035811.0
195698	7760885	ViewPostImeInputStage id=0x25d6ed18	5.0		1469467708097999.0
195705	7404428	K_dispatchTouchEvent_I:KwaiGrootViewPager_T:KwaiGrootViewPager	6.0		1469467708406071.0
195715	7223230	K_onTouchEvent_I:GrootTouchViewPager_T:KwaiGrootViewPager	7.0		1469467708573571.0
195716	263386	K_ignoreTouchEvent_I:GrootTouchViewPager_T:KwaiGrootViewPager	8.0		1469467708590446.0
195731	6735729	K_onTouchEvent_I:VerticalViewPager_T:KwaiGrootViewPager	8.0		1469467709046801.0
195756	390156	K_onPageScrolled_I:GrootViewPager$1_T:GrootViewPager$1	9.0		1469467709644405.0
195792	542708	K_onPageScrolled_I:NasaFeaturedAutoRefreshPresenter$2_T:	9.0		1469467710283728.0
195803	388541	K_getRealPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	10.0		1469467710429926.0
195814	257344	K_getRealPositionInAdapter_I:PositionService_T:PositionService	11.0		1469467710555967.0
195816	224063	K_logInfo_I:GrootLogger_T:GrootLogger	12.0		1469467710577061.0
195832	324739	K_getRealPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	9.0		1469467710872478.0
195898	377240	K_getRealPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	9.0		1469467711950863.0
195904	274531	K_getRealPositionInAdapter_I:PositionService_T:PositionService	10.0		1469467712047426.0
195905	252187	K_logInfo_I:GrootLogger_T:GrootLogger	11.0		1469467712063832.0
195946	1345781	K_onPageScrolled_I:DraggingLayerPresenter$mPageChangeListener$1_T:	9.0		1469467712753676.0
195951	872656	K_getPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	10.0		1469467712819978.0
195958	715209	K_getPositionInAdapter_I:PositionService_T:PositionService	11.0		1469467712963519.0
195961	396302	K_getPositionInAdapter_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter	12.0		1469467712983780.0
195980	265312	K_logInfo_I:GrootLogger_T:GrootLogger	12.0		1469467713403155.0
195997	335260	K_onPageScrolled_I:CommentElement$7_T:	9.0		1469467714117061.0
196001	268646	K_setDanmakuTipBubbleDismiss_I:CommentViewModel_T:CommentViewModel	10.0		1469467714172686.0
196002	243438	K_setValue_I:KLiveData_T:KDispatchLiveData	11.0		1469467714191696.0
196004	224635	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	12.0		1469467714205499.0
196023	798646	K_onPageScrolled_I:DraggingLayerPresenter$mPageChangeListener$1_T:	9.0		1469467714580967.0
196034	601094	K_getPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	10.0		1469467714648988.0
196042	489167	K_getPositionInAdapter_I:PositionService_T:PositionService	11.0		1469467714755394.0
196043	320677	K_getPositionInAdapter_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter	12.0		1469467714777894.0
196083	285834	K_onPageScrolled_I:CommentElement$7_T:	9.0		1469467715400967.0
196088	237709	K_setDanmakuTipBubbleDismiss_I:CommentViewModel_T:CommentViewModel	10.0		1469467715443571.0
196091	216145	K_setValue_I:KLiveData_T:KDispatchLiveData	11.0		1469467715459874.0
196148	293541	processInputEventForCompatibility	4.0		1469467716063103.0
196171	372252135	deliverInputEvent src=0x1002 eventTimeNano=240399824539000 id=0x5090462	4.0		1469467716448988.0
196177	371981198	ViewPostImeInputStage id=0x5090462	5.0		1469467716570446.0
196283	369080261	K_dispatchTouchEvent_I:KwaiGrootViewPager_T:KwaiGrootViewPager	6.0		1469467719404717.0
196300	368768125	K_onTouchEvent_I:GrootTouchViewPager_T:KwaiGrootViewPager	7.0		1469467719661592.0
196301	242031	K_ignoreTouchEvent_I:GrootTouchViewPager_T:KwaiGrootViewPager	8.0		1469467719677738.0
196321	368285156	K_onTouchEvent_I:VerticalViewPager_T:KwaiGrootViewPager	8.0		1469467720133832.0
196331	201823	K_determineTargetPage_I:GrootTouchViewPager_T:KwaiGrootViewPager	9.0		1469467720432738.0
196350	251614	K_onPageScrollStateChanged_I:GrootViewPager$1_T:GrootViewPager$1	9.0		1469467720732426.0
196383	286875	K_onPageScrollStateChanged_I:NasaBottomBarDividerPresenter$mPageChangeListener$1_T:	9.0		1469467721306540.0
196406	920365	K_onPageScrollStateChanged_I:NasaMilanoProgressPresenter$3_T:	9.0		1469467722019613.0
196409	822708	K_changeSeekBarShown_I:NasaMilanoProgressPresenter_T:NasaMilanoProgressPresenter	10.0		1469467722058832.0
196410	345730	K_getCurrentProgress_I:NasaMilanoProgressPresenter_T:NasaMilanoProgressPresenter	11.0		1469467722098519.0
196457	365204271	K_onPageSelected_I:GrootViewPager$1_T:GrootViewPager$1	9.0		1469467723208467.0
196465	8947709	K_onPageSelected_I:GrootSlideCommonLogger_T:GrootSlideCommonLogger	10.0		1469467723417842.0
196467	8922396	K_logOnPageSelected_I:GrootSlideCommonLogger_T:GrootSlideCommonLogger	11.0		1469467723437582.0
196517	344167	K_logPageLeave_I:GrootSlideCommonLogger_T:GrootSlideCommonLogger	12.0		1469467724096488.0
196529	224480	K_logLevelType_I:GrootSlideCommonLogger_T:GrootSlideCommonLogger	13.0		1469467724209144.0
196540	7362865	K_logSwitchPhoto_I:GrootSlideCommonLogger_T:GrootSlideCommonLogger	12.0		1469467724458988.0
196697	3971093	K_logEvent_I:LogManager_T:LogManager--content_package <   photo_package <     atlastype: 0     author_id: 2198326615     draw_t	13.0		1469467727839978.0
196702	765052	Lock contention on thread list lock (owner tid: 27907)	14.0		1469467727932061.0
196874	365781	K_setRefererInfo_I:GrootSlideCommonLogger_T:GrootSlideCommonLogger	12.0		1469467731854613.0
196887	244584	K_getCurrLogger_I:GrootSlideCommonLogger_T:GrootSlideCommonLogger	13.0		1469467731935915.0
196924	970520	K_onPageSelected_I:PhotoDetailLoggerPresenter$3_T:	10.0		1469467732392426.0
196929	610209	K_getPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	11.0		1469467732417217.0
196940	488750	K_getPositionInAdapter_I:PositionService_T:PositionService	12.0		1469467732532894.0
196942	278854	K_getPositionInAdapter_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter	13.0		1469467732558207.0
196967	263802	K_logInfo_I:PhotoDetailLoggerPresenter_T:PhotoDetailLoggerPresenter	11.0		1469467733088936.0
196994	1068073	K_onPageSelected_I:PhotoDetailLoggerPresenter$3_T:	10.0		1469467733382582.0
196998	785885	K_getPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	11.0		1469467733434978.0
197007	666302	K_getPositionInAdapter_I:PositionService_T:PositionService	12.0		1469467733548936.0
197008	468125	K_getPositionInAdapter_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter	13.0		1469467733566749.0
197116	344906822	K_onPageSelected_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter	10.0		1469467734536749.0
197120	851719	K_updateLastShowType_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter	11.0		1469467734561332.0
197167	342432291	K_onSelectChanged_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter	11.0		1469467735579353.0
197179	442083	K_notifyLazyFragmentActive_I:GrootFragmentPagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter	12.0		1469467735772426.0
197182	407343	K_notifyLazyFragmentActive_I:NotifyLazyFragmentActiveHelper_T:NotifyLazyFragmentActiveHelper	13.0		1469467735801124.0
197197	215209	K_logInfo_I:GrootLogger_T:GrootLogger	14.0		1469467735980290.0
197207	177001979	K_notifyDetachedItems_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter	12.0		1469467736238155.0
197221	171590469	K_performViewItemWillDisappear_I:GrootViewItem_T:GrootViewItem	13.0		1469467736621384.0
197223	171515104	K_performViewItemWillDisappear_I:GrootBaseFragment_T:NasaGrootDetailVMFragment	14.0		1469467736688988.0
197227	171299166	K_willDisappear_I:SlidePlayPhotoDetailVMFragment_T:NasaGrootDetailVMFragment	15.0		1469467736845551.0
197229	226771	K_log_I:SlidePlayMonitorLogger_T:SlidePlayMonitorLogger	16.0		1469467736905394.0
197234	317969	K_performTrackStage_I:DetailSlidePlayFragment_T:NasaGrootDetailVMFragment	16.0		1469467737358467.0
197240	35383125	K_callBecomesDetachedOnPageSelectedListener_I:SlidePlayPhotoDetailVMFragment_T:NasaGrootDetailVMFragment	16.0		1469467737830186.0
197243	35277448	K_traverseListenersBecomesDetachedOrWillDisappearByGroup_I:SlidePlayLifecycleHelper_T:SlidePlayLifecycleHelper	17.0		1469467737929353.0
197245	35188437	K_traverseListenersBecomesDetached_I:SlidePlayLifecycleHelper_T:SlidePlayLifecycleHelper	18.0		1469467737963884.0
197247	4409323	K_becomesDetachedOnPageSelected_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	19.0		1469467738022478.0
197248	827813	K_logPlayInfo_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	20.0		1469467738049613.0
197250	671094	K_getPlayModuleInfo_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	21.0		1469467738105342.0
197282	674219	K_logPlayInfo_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	20.0		1469467738905394.0
197285	506458	K_getPlayModuleInfo_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	21.0		1469467738953832.0
197312	2569427	K_savePlayPosition_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	20.0		1469467739609353.0
197313	346562	K_isSavePlayProgress_I:PlayPositionHelper_T:PlayPositionHelper	21.0		1469467739644301.0
197317	239583	K_enableContinuePlay_I:PlayPositionHelper_T:PlayPositionHelper	22.0		1469467739745707.0
197334	1880625	K_savePlayPosition_I:PlayPositionHelper_T:PlayPositionHelper	21.0		1469467740211436.0
197335	1080729	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	22.0		1469467740234249.0
197336	367917	K_logMsg_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	23.0		1469467740253884.0
197338	231771	K_getLogPrefix_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	24.0		1469467740298363.0
197350	661719	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	23.0		1469467740647321.0
197351	312396	K_logMsg_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	24.0		1469467740663832.0
197353	203073	K_getLogPrefix_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	25.0		1469467740702113.0
197368	304375	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	24.0		1469467740998988.0
197369	249011	K_logMsg_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	25.0		1469467741014769.0
197397	509010	K_put_I:PlayProgressPositionManager_T:PlayProgressPositionManager	22.0		1469467741576332.0
197436	220730	K_stop_I:PlayerResumePauseController_T:PlayerResumePauseController	20.0		1469467742203519.0
197469	3048646	K_becomesDetachedOnPageSelected_I:SlideLoadingStatePresenter$mAttachChangedListener$1_T:	19.0		1469467742879561.0
197470	2552396	K_notifyStartLoading_I:SlideLoadingStatePresenter_T:SlideLoadingStatePresenter	20.0		1469467742904769.0
197471	2522396	K_accept_I:SlidePage$onBecomesAttached$2_T:	21.0		1469467742928467.0
197472	2498437	K_accept_I:SlidePage$onBecomesAttached$2_T:	22.0		1469467742947009.0
197478	2406563	K_showLoading_I:FullScreenLayerGroupEventBus_T:FullScreenLayerGroupEventBus	23.0		1469467743033311.0
197479	1420000	K_accept_I:PlayLoadingElement$onBind$1_T:	24.0		1469467743059769.0
197482	1373125	K_accept_I:PlayLoadingElement$onBind$1_T:	25.0		1469467743097426.0
197483	1337448	K_stopLoading_I:PlayLoadingElement_T:PlayLoadingElement	26.0		1469467743119040.0
197484	1170312	K_updateLoadingVisibility_I:PlayLoadingElement_T:PlayLoadingElement	27.0		1469467743141332.0
197490	1084375	K_tryShowOrHide_I:PlayLoadingElement_T:PlayLoadingElement	28.0		1469467743220759.0
197491	1055520	K_hideAndLog_I:PlayLoadingElement_T:PlayLoadingElement	29.0		1469467743244249.0
197492	778073	K_tryHide_I:BaseElement_T:PlayLoadingElement	30.0		1469467743273728.0
197494	749844	K_setValue_I:KLiveData_T:KDispatchLiveData	31.0		1469467743292894.0
197495	712604	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	32.0		1469467743318728.0
197497	660990	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	33.0		1469467743362217.0
197499	636563	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	34.0		1469467743377738.0
197500	605937	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	35.0		1469467743397999.0
197501	585521	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	36.0		1469467743412217.0
197502	561041	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	37.0		1469467743430603.0
197504	533281	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	38.0		1469467743449249.0
197527	215573	K_logMsg_I:PlayLoadingElement_T:PlayLoadingElement	30.0		1469467744076436.0
197547	939271	K_accept_I:BottomProgressLoadingElement$onBind$2_T:	24.0		1469467744494821.0
197548	915469	K_accept_I:BottomProgressLoadingElement$onBind$2_T:	25.0		1469467744512946.0
197562	677552	K_doTryHideAndLog_I:BottomProgressLoadingElement_T:BottomProgressLoadingElement	26.0		1469467744744874.0
197563	655625	K_tryHide_I:BaseElement_T:BottomProgressLoadingElement	27.0		1469467744760186.0
197564	635313	K_setValue_I:KLiveData_T:KDispatchLiveData	28.0		1469467744774144.0
197565	610365	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	29.0		1469467744793571.0
197568	567344	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	30.0		1469467744830915.0
197570	541718	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	31.0		1469467744851228.0
197571	516771	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	32.0		1469467744870811.0
197574	494375	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	33.0		1469467744887946.0
197575	471042	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	34.0		1469467744905238.0
197576	447656	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	35.0		1469467744920655.0
197591	434739	K_releasePlayerLoadingHelper_I:SlideLoadingStatePresenter_T:SlideLoadingStatePresenter	20.0		1469467745487478.0
197609	1083593	K_becomesDetachedOnPageSelected_I:SlidePlayScreenVMPresenter$1_T:	19.0		1469467745983103.0
197619	424062	K_getCurrentItemRealPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	20.0		1469467746274249.0
197624	294062	K_getCurrentRealPositionInAdapter_I:PositionService_T:PositionService	21.0		1469467746398832.0
197644	230834	K_becomesDetachedOnPageSelected_I:SlidePlayPhotoPreloadOptPresenter$1_T:	19.0		1469467747092321.0
197660	310677	K_becomesDetachedOnPageSelected_I:SlidePlayVoteStickerPresenter$1_T:	19.0		1469467747354926.0
197661	255052	K_removeOnPageChangeListener_I:SlidePlayViewModel_T:SlidePlayViewModel	20.0		1469467747401905.0
197733	1460834	K_becomesDetachedOnPageSelected_I:NasaMilanoProgressPresenter$1_T:	19.0		1469467748142321.0
197755	378750	K_removeOnViewPagerTranslateYListener_I:SlidePlayViewModel_T:SlidePlayViewModel	20.0		1469467748372478.0
197781	534896	K_becomesDetached_I:NasaMilanoProgress_T:NasaMilanoProgress	20.0		1469467748778571.0
197784	410416	K_resetProgress_I:NasaFeaturedSeekBar_T:NasaFeaturedSeekBar	21.0		1469467748843676.0
197859	13814531	K_becomesDetachedOnPageSelected_I:NasaScaleCleanControllerEventPresenter$1_T:	19.0		1469467749684509.0
197861	288906	K_getCurrentPhoto_I:SlidePlayViewModel_T:SlidePlayViewModel	20.0		1469467749708103.0
197922	13186250	K_lambda$onBind$2_I:NasaScaleCleanControllerEventPresenter_T:NasaScaleCleanControllerEventPresenter	20.0		1469467750282738.0
197926	7843333	K_logSpeedChange_I:NasaScaleCleanControllerEventPresenter_T:NasaScaleCleanControllerEventPresenter	21.0		1469467750327582.0
197941	1098177	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	22.0		1469467750557009.0
197944	367291	K_logMsg_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	23.0		1469467750577478.0
197948	247812	K_getLogPrefix_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	24.0		1469467750620707.0
197990	677500	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	23.0		1469467750972374.0
197992	302812	K_logMsg_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	24.0		1469467750988780.0
198009	326666	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	24.0		1469467751313728.0
198010	267604	K_logMsg_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	25.0		1469467751330082.0
198048	1063698	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	22.0		1469467751703519.0
198049	341980	K_logMsg_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	23.0		1469467751718571.0
198053	241302	K_getLogPrefix_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	24.0		1469467751763519.0
198072	674323	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	23.0		1469467752077269.0
198074	305469	K_logMsg_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	24.0		1469467752105290.0
198076	211042	K_getLogPrefix_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	25.0		1469467752141332.0
198096	317604	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	24.0		1469467752428311.0
198097	260834	K_logMsg_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	25.0		1469467752448571.0
198184	2991718	K_logEvent_I:LogManager_T:LogManager--content_package <   photo_package <     atlastype: 0     author_id: 2198326615     draw_t	22.0		1469467755172374.0
198192	1332864	Lock contention on thread list lock (owner tid: 27914)	23.0		1469467755909926.0
198226	2889740	K_updateSpeed_I:SlidePlayerPanelViewModelImpl_T:SlidePlayerPanelViewModelImpl	21.0		1469467758296696.0
198227	1148334	K_accept_I:PlayerPanelElementController$bind$1_T:PlayerPanelElementController$bind$1	22.0		1469467758328467.0
198228	1120364	K_accept_I:PlayerPanelElementController$bind$1_T:PlayerPanelElementController$bind$1	23.0		1469467758351332.0
198230	475469	K_isSpeedEnable_I:SlidePlayerPanelPhotoConfig_T:SlidePlayerPanelPhotoConfig	24.0		1469467758383780.0
198246	220833	K_isSpeedEnable_I:SlidePlayerPanelConfig_T:SlidePlayerPanelConfig	25.0		1469467758633311.0
198263	525937	K_setSpeed_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	24.0		1469467758940603.0
198265	501302	K_setSpeed_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	25.0		1469467758960394.0
198266	481614	K_setSpeed_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	26.0		1469467758974926.0
198275	341771	K_setSpeed_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	27.0		1469467759109144.0
198296	1685521	K_accept_I:PlayerPanelElementController$bind$1_T:PlayerPanelElementController$bind$1	22.0		1469467759493988.0
198297	1659011	K_accept_I:PlayerPanelElementController$bind$1_T:PlayerPanelElementController$bind$1	23.0		1469467759509717.0
198298	1037187	K_isSpeedEnable_I:SlidePlayerPanelPhotoConfig_T:SlidePlayerPanelPhotoConfig	24.0		1469467759528155.0
198305	554583	K_isSupportPlayerPanelFeatureType_I:SlidePlayerPanelPhotoConfig_T:SlidePlayerPanelPhotoConfig	25.0		1469467759744509.0
198306	532604	K_isSupportPlayerPanelFeatureType_I:NasaDetailUtil_T:NasaDetailUtil	26.0		1469467759758103.0
198308	510573	K_get_I:VideoFeedAccessor$12_T:	27.0		1469467759768936.0
198310	445989	K_get_I:VideoFeedAccessor$12_T:	28.0		1469467759782582.0
198327	237136	K_isSpeedEnable_I:SlidePlayerPanelConfig_T:SlidePlayerPanelConfig	25.0		1469467760322269.0
198344	547135	K_setSpeed_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	24.0		1469467760615655.0
198345	503802	K_setSpeed_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	25.0		1469467760650186.0
198346	484375	K_setSpeed_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	26.0		1469467760663988.0
198347	461562	K_setSpeed_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	27.0		1469467760681124.0
198377	1052864	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	21.0		1469467761259457.0
198379	314427	K_logMsg_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	22.0		1469467761274665.0
198382	213489	K_getLogPrefix_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	23.0		1469467761315499.0
198397	695989	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	22.0		1469467761604353.0
198398	363177	K_logMsg_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	23.0		1469467761622009.0
198402	259270	K_getLogPrefix_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	24.0		1469467761662999.0
198417	293282	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	23.0		1469467762002321.0
198419	241354	K_logMsg_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	24.0		1469467762022478.0
198443	1084323	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	21.0		1469467762348571.0
198445	515260	K_logMsg_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	22.0		1469467762373051.0
198448	225417	K_getLogPrefix_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	23.0		1469467762405238.0
198472	526511	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	22.0		1469467762901644.0
198474	246250	K_logMsg_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	23.0		1469467762915551.0
198492	248490	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	23.0		1469467763174717.0
198494	205105	K_logMsg_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	24.0		1469467763188571.0
198510	392344	K_becomesDetachedOnPageSelected_I:SlidePlaySwitchNextGuidePresenter$mAttachChangeListener$1_T:	19.0		1469467763597269.0
198513	317864	K_immediatelyDismiss_I:SlidePlaySwitchNextGuidePopup_T:SlidePlaySwitchNextGuidePopup	20.0		1469467763664249.0
198523	228541	K_becomesDetachedOnPageSelected_I:PhotoPlayCommonSceneDetectorPresenter$mAttachChangeListener$1_T:	19.0		1469467764019978.0
198595	522552	K_becomesDetachedOnPageSelected_I:NasaDetailBottomBarVMPresenter$2_T:	19.0		1469467764972061.0
198599	464584	K_resetBottomBar_I:NasaDetailBottomBarVMPresenter_T:NasaDetailBottomBarVMPresenter	20.0		1469467765021071.0
198601	299792	K_showOrHideBottomBar_I:NasaDetailBottomBarVMPresenter_T:NasaDetailBottomBarVMPresenter	21.0		1469467765040863.0
198637	359479	K_becomesDetachedOnPageSelected_I:FeatureLeftSlideGuidePresenter$2_T:	19.0		1469467765719665.0
198667	542864	K_becomesDetachedOnPageSelected_I:NasaFeatureLikeGuidePresenter$mAttachChangedListener$1_T:	19.0		1469467766103780.0
198669	305937	K_cancelLikeGuide_I:NasaFeatureLikeGuidePresenter_T:NasaFeatureLikeGuidePresenter	20.0		1469467766132634.0
198749	1017135	K_becomesDetachedOnPageSelected_I:WelcomeBackLoginPresenter$1_T:	19.0		1469467767119926.0
198754	862240	K_handleQPhotoNotLike_I:WelcomeBackHelper_T:WelcomeBackHelper	20.0		1469467767166696.0
198767	579114	K_hasWelcomeBackLoginDialogShowed_I:WelcomeBackHelper_T:WelcomeBackHelper	21.0		1469467767367374.0
198811	938334	K_becomesDetachedOnPageSelected_I:ActionTriggerVMPresenter$4_T:	19.0		1469467768204040.0
198812	873907	K_clearStatusListener_I:ActionTriggerVMPresenter_T:ActionTriggerVMPresenter	20.0		1469467768228467.0
198832	362864	K_release_I:PlayerProgressUpdater_T:PlayerProgressUpdater	21.0		1469467768676905.0
198859	272239	K_becomesDetachedOnPageSelected_I:RedesignPlayProgressEventPresenter$1_T:	19.0		1469467769255655.0
198884	278125	K_isAdSearchBrandVideoPhoto_I:BaseAdSearchUtil_T:BaseAdSearchUtil	19.0		1469467769691071.0
198886	257604	K_getSearchBrandAdType_I:BaseAdSearchUtil_T:BaseAdSearchUtil	20.0		1469467769706228.0
198887	230105	K_getAdSearchBrandInfo_I:BaseAdSearchUtil_T:BaseAdSearchUtil	21.0		1469467769728571.0
198908	1378229	K_unregisterVideoListeners_I:VideoPlayedReporter_T:VideoPlayedReporter	19.0		1469467770257061.0
198914	484896	K_i_I:KCLog_T:KCLog	20.0		1469467770409613.0
198915	455677	K_i_I:LocalLogHelperImpl_T:LocalLogHelperImpl	21.0		1469467770427738.0
198917	356042	Contending for pthread mutex	22.0		1469467770503467.0
198926	317031	K_stop_I:VideoPlayedReporter_T:VideoPlayedReporter	20.0		1469467770931853.0
198927	284063	K_stop_I:AdPlayedLogReporter_T:AdPlayedLogReporter	21.0		1469467770957894.0
198949	273907	K_stop_I:VideoPlayedReporter_T:VideoPlayedReporter	19.0		1469467771656592.0
198950	239740	K_stop_I:AdPlayedLogReporter_T:AdPlayedLogReporter	20.0		1469467771684821.0
198961	818385	K_reset_I:VideoPlayedReporter_T:VideoPlayedReporter	19.0		1469467771952582.0
198963	762396	K_reset_I:VideoPlayedReporter_T:VideoPlayedReporter	20.0		1469467772002321.0
198964	727187	K_reset_I:AdPlayedLogReporter_T:AdPlayedLogReporter	21.0		1469467772031176.0
199032	93856927	K_becomesDetached_I:BasePage_T:SlidePage	16.0		1469467773251592.0
199041	93668646	K_becomesDetachInner_I:BasePage_T:SlidePage	17.0		1469467773428103.0
199049	2825261	K_becomesDetached_I:BaseGroup_T:SideProgressGroup	18.0		1469467773668415.0
199050	2780416	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:SidebarProgressElement	19.0		1469467773696280.0
199054	202917	K_isEnableBindAttachCheck_I:BaseElement_T:SidebarProgressElement	20.0		1469467773883259.0
199066	2282604	K_onBecomesDetached_I:SidebarProgressElement$mPhotoDetailAttachChangedListener$1_T:	20.0		1469467774176540.0
199089	593385	K_unregisterProxyListener_I:SidebarProgressElement_T:SidebarProgressElement	21.0		1469467774428780.0
199092	458281	K_useNativeCache_I:QPhotoContentUtil_T:QPhotoContentUtil	22.0		1469467774477738.0
199144	897969	K_tryHide_I:BaseElement_T:SidebarProgressElement	21.0		1469467775542894.0
199145	864167	K_setValue_I:KLiveData_T:KDispatchLiveData	22.0		1469467775567217.0
199146	823854	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	23.0		1469467775594926.0
199148	753125	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	24.0		1469467775659978.0
199149	731562	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	25.0		1469467775675134.0
199150	707552	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	26.0		1469467775690238.0
199151	685260	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	27.0		1469467775706436.0
199152	643750	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	28.0		1469467775741176.0
199153	619322	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	29.0		1469467775756749.0
199169	9179636	K_becomesDetached_I:BaseGroup_T:InformationGroup	18.0		1469467776587217.0
199188	201875	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:RecoReasonElement	19.0		1469467776988936.0
199198	1177917	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:SingleWeakStyleElement	19.0		1469467777207321.0
199207	993020	K_onBecomesDetached_I:BasePlcWeakStyleElement$2_T:	20.0		1469467777384249.0
199213	444896	K_becomesDetachedOnPageSelected_I:PLCLogHelper$4_T:	21.0		1469467777565290.0
199217	303021	K_release_I:KwaiPlayerPlayOrNotHelper_T:KwaiPlayerPlayOrNotHelper	22.0		1469467777688571.0
199226	322760	K_onPageAttached_I:SingleWeakStyleElement_T:SingleWeakStyleElement	21.0		1469467778048936.0
199229	237396	K_onPageAttached_I:SingleWeakViewModel_T:SingleWeakViewModel	22.0		1469467778128936.0
199230	205157	K_setValue_I:KLiveData_T:KDispatchLiveData	23.0		1469467778155342.0
199240	220364	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:RewardInfoLabelElement	19.0		1469467778404978.0
199249	238646	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:MerchantCommentElement	19.0		1469467778640915.0
199262	422396	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:UserNameElement	19.0		1469467778896332.0
199271	206875	K_onBecomesDetached_I:PageAttachChangedListener_T:	20.0		1469467779106332.0
199280	285104	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:CoCreatorAuthorsElement	19.0		1469467779345186.0
199293	288021	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:CaptionElement	19.0		1469467779647009.0
199315	246615	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:CreateTimeElement	19.0		1469467779970342.0
199323	1007864	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:MusicLabelElement	19.0		1469467780242582.0
199345	813177	K_onBecomesDetached_I:MusicLabelElement$1_T:	20.0		1469467780430290.0
199347	271614	K_setDisableShowBubble_I:MusicLabelViewModel_T:MusicLabelViewModel	21.0		1469467780480082.0
199348	245885	K_setValue_I:KLiveData_T:KDispatchLiveData	22.0		1469467780499874.0
199351	218229	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	23.0		1469467780521749.0
199371	454844	K_stopMarquee_I:MusicLabelElement_T:MusicLabelElement	21.0		1469467780781436.0
199381	401615	K_setMarqueeState_I:MusicLabelViewModel_T:MusicLabelViewModel	22.0		1469467780827686.0
199384	378802	K_setValue_I:KLiveData_T:KDispatchLiveData	23.0		1469467780844926.0
199387	356093	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	24.0		1469467780861801.0
199389	303802	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	25.0		1469467780908259.0
199390	280937	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	26.0		1469467780925603.0
199391	258959	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	27.0		1469467780942217.0
199392	225209	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	28.0		1469467780962217.0
199437	699687	K_becomesDetached$slide_play_detail_framework_release_I:BaseElement_T:PushNegativeFeedbackEntryElement	19.0		1469467781468259.0
199453	444688	K_log_I:SlidePlayMonitorLogger_T:SlidePlayMonitorLogger	20.0		1469467781629092.0
199663	211927	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:QuickCommentElement	19.0		1469467783714457.0
199678	608438	K_becomesDetached$slide_play_detail_framework_release_I:BaseElement_T:ShootWithHimElement	19.0		1469467783943363.0
199696	379427	K_log_I:SlidePlayMonitorLogger_T:SlidePlayMonitorLogger	20.0		1469467784079405.0
199710	214531	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:QuickShareElement	19.0		1469467784577113.0
199717	946302	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:SendDanmakuLowPriorityNewElement	19.0		1469467784810030.0
199724	608594	K_onBecomesDetached_I:BaseSendDanmakuNewElement$1_T:	20.0		1469467785141280.0
199726	413229	K_setDisableShowBubble_I:SendDanmakuViewModel_T:SendDanmakuViewModel	21.0		1469467785331228.0
199727	371927	K_setValue_I:KLiveData_T:KDispatchLiveData	22.0		1469467785366384.0
199728	344115	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	23.0		1469467785388936.0
199730	220312	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	24.0		1469467785507426.0
199744	263229	K_becomesDetached_I:BaseGroup_T:MiddleGroup	18.0		1469467785897738.0
199745	234792	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:AtlasPlaySegmentedProgressElement	19.0		1469467785918884.0
199772	232708	K_becomesDetached_I:BaseGroup_T:CenterEntranceGroup	18.0		1469467786232686.0
199794	1581355	K_becomesDetached_I:BaseGroup_T:BottomGroup	18.0		1469467786537321.0
199850	216146	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:PhotoStatusElement	19.0		1469467786996905.0
199902	242709	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:UserStatusBottomElement	19.0		1469467787817269.0
199920	7256146	K_becomesDetached_I:BaseGroup_T:RightActionBarGroup	18.0		1469467788282842.0
199922	256771	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:VoteIconElement	19.0		1469467788352478.0
199931	1875156	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:AvatarAndFollowElement	19.0		1469467788624978.0
199938	1623906	K_onBecomesDetached_I:AvatarAndFollowBaseElement$4_T:	20.0		1469467788869926.0
199953	250573	K_dismissBubble_I:ThanosFollowGuideHelper_T:ThanosFollowGuideHelper	21.0		1469467789326436.0
199977	882032	K_hideLiveWindow_I:AvatarAndFollowBaseElement_T:AvatarAndFollowElement	21.0		1469467789605967.0
199981	794271	K_setShowLiveWindow_I:AvatarAndFollowViewModel_T:AvatarAndFollowViewModel	22.0		1469467789687269.0
199983	729635	K_setValue_I:KLiveData_T:KDispatchLiveData	23.0		1469467789745134.0
199984	705000	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	24.0		1469467789761280.0
199987	571614	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	25.0		1469467789888676.0
199989	548229	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	26.0		1469467789906280.0
199991	520886	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	27.0		1469467789927321.0
199993	500365	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	28.0		1469467789942009.0
199997	462292	K_lambda$onBindData$4_I:AvatarAndFollowElementView_T:AvatarAndFollowElementView	29.0		1469467789973571.0
199998	368437	K_hideLiveWindow_I:AvatarAndFollowElementView_T:AvatarAndFollowElementView	30.0		1469467790057530.0
200019	258021	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:RewardElement	19.0		1469467790520446.0
200035	1249687	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:LikeElement	19.0		1469467790798207.0
200045	1051667	K_onBecomesDetached_I:DefaultPageAttachChangedListener_T:	20.0		1469467790985759.0
200047	1025521	K_onBecomesDetached_I:LikeElement$2_T:	21.0		1469467791006332.0
200060	662968	K_setLikeAnimation_I:LikeViewModel_T:LikeViewModel	22.0		1469467791362478.0
200068	496563	K_setValue_I:KLiveData_T:KDispatchLiveData	23.0		1469467791522165.0
200069	475052	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	24.0		1469467791538051.0
200072	412240	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	25.0		1469467791594665.0
200074	390000	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	26.0		1469467791610915.0
200075	366094	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	27.0		1469467791625811.0
200077	346666	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	28.0		1469467791639301.0
200078	310000	K_lambda$onBindData$4_I:LikeElementView_T:LikeElementView	29.0		1469467791670446.0
200093	539948	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:CommentElement	19.0		1469467792074665.0
200109	288854	K_onBecomesDetached_I:CommentElement$8_T:	20.0		1469467792318884.0
200112	224114	K_setDanmakuTipBubbleDismiss_I:CommentViewModel_T:CommentViewModel	21.0		1469467792369926.0
200131	1336354	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:CollectElement	19.0		1469467792672217.0
200144	1107344	K_onBecomesDetached_I:DefaultPageAttachChangedListener_T:	20.0		1469467792894509.0
200148	1079792	K_onBecomesDetached_I:CollectElement$onBind$4_T:	21.0		1469467792915342.0
200167	557864	K_setCollectAnimation_I:CollectViewModel_T:CollectViewModel	22.0		1469467792996124.0
200179	388802	K_setValue_I:KLiveData_T:KDispatchLiveData	23.0		1469467793159457.0
200183	341302	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	24.0		1469467793201436.0
200187	295104	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	25.0		1469467793242217.0
200188	269532	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	26.0		1469467793258467.0
200195	226093	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	27.0		1469467793296124.0
200196	205052	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	28.0		1469467793311436.0
200217	383073	K_showOrHideCollectBeatLottie_I:CollectViewModel_T:CollectViewModel	22.0		1469467793603103.0
200218	358229	K_setValue_I:KLiveData_T:KDispatchLiveData	23.0		1469467793621436.0
200219	336771	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	24.0		1469467793636696.0
200224	281928	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	25.0		1469467793686071.0
200225	257761	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	26.0		1469467793704717.0
200226	236979	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	27.0		1469467793719717.0
200240	269844	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:ShareElement	19.0		1469467794026436.0
200261	1217969	K_becomesDetached$slide_play_detail_framework_release_I:BaseElement_T:MusicWheelElement	19.0		1469467794314144.0
200274	762812	K_onBecomesDetached_I:MusicWheelElement$mAttachChangedListener$1_T:	20.0		1469467794653103.0
200281	565416	K_setCoverUrls_I:MusicWheelViewModel_T:MusicWheelViewModel	21.0		1469467794843676.0
200282	523593	K_setValue_I:KLiveData_T:KLiveData	22.0		1469467794878676.0
200283	497240	K_notifyValueSet_I:KLiveData_T:KLiveData	23.0		1469467794899040.0
200284	477968	K_notifyValueSet_I:KLiveData_T:KLiveData	24.0		1469467794912374.0
200285	446719	K_onChanged_I:MusicWheelElementView$onBindData$1_T:MusicWheelElementView$onBindData$1	25.0		1469467794937009.0
200286	409375	K_onChanged_I:MusicWheelElementView$onBindData$1_T:MusicWheelElementView$onBindData$1	26.0		1469467794966436.0
200302	3092865	K_becomesDetached_I:BaseGroup_T:PlayControllerGroup	18.0		1469467795638363.0
200303	3068698	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:ProgressPreviewElement	19.0		1469467795656957.0
200310	2645156	K_onBecomesDetached_I:ProgressPreviewElement$mAttachChangedListener$2$1_T:	20.0		1469467796073884.0
200311	2576771	K_onElementDetached_I:ProgressPreviewElement_T:ProgressPreviewElement	21.0		1469467796137269.0
200314	387656	K_get_I:Singleton_T:Singleton-33563823	22.0		1469467796498780.0
200333	245521	K_hideLoading_I:ProgressPreviewElement_T:ProgressPreviewElement	22.0		1469467797162582.0
200356	1054062	K_tryHide_I:BaseElement_T:ProgressPreviewElement	22.0		1469467797651905.0
200358	1031615	K_setValue_I:KLiveData_T:KDispatchLiveData	23.0		1469467797668936.0
200360	1008958	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	24.0		1469467797683103.0
200363	963802	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	25.0		1469467797721280.0
200365	934480	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	26.0		1469467797745446.0
200367	910156	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	27.0		1469467797761384.0
200369	821875	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	28.0		1469467797844561.0
200372	792865	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	29.0		1469467797867686.0
200378	765417	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	30.0		1469467797888936.0
200393	281250	Lock contention on thread suspend count lock (owner tid: 27818)	31.0		1469467797984874.0
200459	10583438	K_becomesDetached_I:BaseSingleElementGroup_T:NegativeFeedbackGroup	18.0		1469467798815811.0
200463	10552656	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:NegativeFeedbackElement	19.0		1469467798839040.0
200485	10377760	K_onBecomesDetached_I:NegativeFeedbackElement$2_T:	20.0		1469467799004561.0
200496	10135104	K_onBecomesDetached_I:PlayerPanelElementController_T:PlayerPanelElementController	21.0		1469467799233311.0
200552	2034062	K_resetSpeed_I:SlidePlayerPanelViewModelImpl_T:SlidePlayerPanelViewModelImpl	22.0		1469467799851332.0
200556	876146	K_accept_I:PlayerPanelElementController$bind$1_T:PlayerPanelElementController$bind$1	23.0		1469467799878155.0
200560	856927	K_accept_I:PlayerPanelElementController$bind$1_T:PlayerPanelElementController$bind$1	24.0		1469467799892530.0
200564	404010	K_isSpeedEnable_I:SlidePlayerPanelPhotoConfig_T:SlidePlayerPanelPhotoConfig	25.0		1469467799908728.0
200601	384479	K_setSpeed_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	25.0		1469467800359405.0
200602	354166	K_setSpeed_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	26.0		1469467800384249.0
200603	334375	K_setSpeed_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	27.0		1469467800398988.0
200604	311823	K_setSpeed_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	28.0		1469467800415915.0
200626	1109167	K_accept_I:PlayerPanelElementController$bind$1_T:PlayerPanelElementController$bind$1	23.0		1469467800770394.0
200627	1082500	K_accept_I:PlayerPanelElementController$bind$1_T:PlayerPanelElementController$bind$1	24.0		1469467800790499.0
200629	380781	K_isSpeedEnable_I:SlidePlayerPanelPhotoConfig_T:SlidePlayerPanelPhotoConfig	25.0		1469467800806488.0
200647	640729	K_setSpeed_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	25.0		1469467801227530.0
200648	620365	K_setSpeed_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	26.0		1469467801242738.0
200649	582032	K_setSpeed_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	27.0		1469467801274769.0
200650	560886	K_setSpeed_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	28.0		1469467801290342.0
200658	7203125	K_getPlayerPanelStatusLogParams_I:SlidePlayerPanelManager_T:SlidePlayerPanelManager	22.0		1469467801929092.0
200659	383542	K_isMirrorEnable_I:SlidePlayerPanelPhotoConfig_T:SlidePlayerPanelPhotoConfig	23.0		1469467801954613.0
200685	2009375	FullSuspendCheck	23.0		1469467802353363.0
200734	292188	K_getPlayerSpeedOutsideShow_I:NasaExperimentUtils_T:NasaExperimentUtils	23.0		1469467804406696.0
200741	516614	K_isSaveTrafficEnable_I:SlidePlayerPanelPhotoConfig_T:SlidePlayerPanelPhotoConfig	23.0		1469467804746124.0
200746	343594	K_isSaveTrafficEnable_I:SlidePlayerPanelConfig_T:SlidePlayerPanelConfig	24.0		1469467804905811.0
200755	712395	K_isSaveTrafficSelected_I:SlidePlayerPanelManager_T:SlidePlayerPanelManager	23.0		1469467805289874.0
200756	403489	K_isSaveTrafficEnable_I:SlidePlayerPanelPhotoConfig_T:SlidePlayerPanelPhotoConfig	24.0		1469467805319405.0
200761	250625	K_isSaveTrafficEnable_I:SlidePlayerPanelConfig_T:SlidePlayerPanelConfig	25.0		1469467805460499.0
200768	240416	K_isSaveTrafficModeOn_I:PlayerPanelHelper_T:PlayerPanelHelper	24.0		1469467805752530.0
200769	209635	K_getEnablePlayerSpeedAndDataSaveModeOptimize_I:NasaExperimentUtils_T:NasaExperimentUtils	25.0		1469467805773624.0
200773	556250	K_isQualityEnable_I:SlidePlayerPanelPhotoConfig_T:SlidePlayerPanelPhotoConfig	23.0		1469467806088155.0
200786	209271	K_isQualityEnable_I:SlidePlayerPanelConfig_T:SlidePlayerPanelConfig	24.0		1469467806418780.0
200796	752969	K_accessibilitySelectedTitle_I:SlidePlayerPanelManager_T:SlidePlayerPanelManager	23.0		1469467806695134.0
200798	477135	K_isAccessibilityEnable_I:SlidePlayerPanelPhotoConfig_T:SlidePlayerPanelPhotoConfig	24.0		1469467806721228.0
200810	209635	K_isAccessibilityEnable_I:SlidePlayerPanelConfig_T:SlidePlayerPanelConfig	25.0		1469467806981332.0
200829	680886	K_isSoundEffectEnable_I:SlidePlayerPanelPhotoConfig_T:SlidePlayerPanelPhotoConfig	23.0		1469467807496696.0
200845	219479	K_isSoundEffectEnable_I:SlidePlayerPanelConfig_T:SlidePlayerPanelConfig	24.0		1469467807811384.0
200864	250260	K_isCollectDislikeEnable_I:SlidePlayerPanelPhotoConfig_T:SlidePlayerPanelPhotoConfig	23.0		1469467808336853.0
200879	508959	K_isSmallWindowEnable_I:SlidePlayerPanelPhotoConfig_T:SlidePlayerPanelPhotoConfig	23.0		1469467808610446.0
200888	351615	K_isSmallWindowEnable_I:SlidePlayerPanelConfig_T:SlidePlayerPanelConfig	24.0		1469467808758311.0
200912	604792	K_becomesDetached_I:BaseSingleElementGroup_T:EmptyPhotoGroup	18.0		1469467809500134.0
200914	557188	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:EmptyPhotoNoteElement	19.0		1469467809535394.0
200924	319323	K_onBecomesDetached_I:PageAttachChangedListener_T:	20.0		1469467809765655.0
200925	202969	K_onBecomesDetached_I:EmptyPhotoNoteElement$2_T:	21.0		1469467809871019.0
200938	254583	K_becomesDetached_I:BaseSingleElementGroup_T:SerialPayPhotoGroup	18.0		1469467810200863.0
200939	222708	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:SerialPausePayElement	19.0		1469467810223884.0
200952	6984114	K_becomesDetached_I:BaseGroup_T:FullScreenLayerGroup	18.0		1469467810549978.0
200953	5104791	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:PlayFailedRetryElement	19.0		1469467810576905.0
200962	4888125	K_onBecomesDetached_I:PlayFailedRetryElement$3_T:	20.0		1469467810784040.0
200964	4622865	K_reset_I:PlayFailedRetryElement_T:PlayFailedRetryElement	21.0		1469467810823884.0
200965	1525886	K_hideRetry_I:PlayFailedRetryElement_T:PlayFailedRetryElement	22.0		1469467810846696.0
200969	989375	K_tryHide_I:BaseElement_T:PlayFailedRetryElement	23.0		1469467810959978.0
200970	961041	K_setValue_I:KLiveData_T:KDispatchLiveData	24.0		1469467810981853.0
200972	938021	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	25.0		1469467810997946.0
200974	872292	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	26.0		1469467811055238.0
200976	831770	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	27.0		1469467811084926.0
200977	804740	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	28.0		1469467811104196.0
200978	777657	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	29.0		1469467811124144.0
200979	747188	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	30.0		1469467811146071.0
200980	715573	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	31.0		1469467811166071.0
201003	221980	K_accept_I:SerializedRelay_T:SerializedRelay	23.0		1469467812142321.0
201007	3029480	K_showLoading_I:PlayFailedRetryElement_T:PlayFailedRetryElement	22.0		1469467812404821.0
201008	2991511	K_accept_I:SlidePage$onBecomesAttached$2_T:	23.0		1469467812434613.0
201010	2964687	K_accept_I:SlidePage$onBecomesAttached$2_T:	24.0		1469467812454457.0
201014	2875209	K_showLoading_I:FullScreenLayerGroupEventBus_T:FullScreenLayerGroupEventBus	25.0		1469467812536644.0
201015	1624270	K_accept_I:PlayLoadingElement$onBind$1_T:	26.0		1469467812564249.0
201016	1594791	K_accept_I:PlayLoadingElement$onBind$1_T:	27.0		1469467812585655.0
201018	1507708	K_stopLoading_I:PlayLoadingElement_T:PlayLoadingElement	28.0		1469467812664874.0
201019	1469322	K_updateLoadingVisibility_I:PlayLoadingElement_T:PlayLoadingElement	29.0		1469467812687999.0
201023	1371458	K_tryShowOrHide_I:PlayLoadingElement_T:PlayLoadingElement	30.0		1469467812775499.0
201024	1341980	K_hideAndLog_I:PlayLoadingElement_T:PlayLoadingElement	31.0		1469467812797894.0
201026	922760	K_tryHide_I:BaseElement_T:PlayLoadingElement	32.0		1469467812815707.0
201027	897396	K_setValue_I:KLiveData_T:KDispatchLiveData	33.0		1469467812832842.0
201028	869062	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	34.0		1469467812850707.0
201030	810313	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	35.0		1469467812902842.0
201031	784323	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	36.0		1469467812920446.0
201032	756771	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	37.0		1469467812940290.0
201034	725260	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	38.0		1469467812962999.0
201035	690938	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	39.0		1469467812990394.0
201036	658698	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	40.0		1469467813011019.0
201049	367500	K_logMsg_I:PlayLoadingElement_T:PlayLoadingElement	32.0		1469467813758571.0
201061	1191562	K_accept_I:BottomProgressLoadingElement$onBind$2_T:	26.0		1469467814211280.0
201064	1163645	K_accept_I:BottomProgressLoadingElement$onBind$2_T:	27.0		1469467814230499.0
201068	216302	K_setShowLoadingAnim_I:BottomProgressLoadingViewModel_T:BottomProgressLoadingViewModel	28.0		1469467814281176.0
201093	861823	K_doTryHideAndLog_I:BottomProgressLoadingElement_T:BottomProgressLoadingElement	28.0		1469467814523259.0
201096	834062	K_tryHide_I:BaseElement_T:BottomProgressLoadingElement	29.0		1469467814543103.0
201098	809636	K_setValue_I:KLiveData_T:KDispatchLiveData	30.0		1469467814560238.0
201100	780573	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	31.0		1469467814582269.0
201102	709948	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	32.0		1469467814644978.0
201103	680781	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	33.0		1469467814666280.0
201107	654895	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	34.0		1469467814684249.0
201109	627968	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	35.0		1469467814703676.0
201111	598907	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	36.0		1469467814722894.0
201112	561146	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	37.0		1469467814749717.0
201155	599010	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:PlayPauseCenterElement	19.0		1469467815705186.0
201176	299479	K_onBecomesDetached_I:BasePlayPauseElement$mAttachChangedListener$2$1_T:	20.0		1469467815995915.0
201194	206146	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:PlayLoadingElement	19.0		1469467816324457.0
201204	204531	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:BottomProgressLoadingElement	19.0		1469467816558884.0
201226	487239	K_becomesDetached$slide_play_detail_framework_release_I:BaseElement_T:PayCourseTrailFinishElement	19.0		1469467816784405.0
201271	230156	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:CoronaPlayPayPauseElement	19.0		1469467817292374.0
201293	300521	K_becomesDetached_I:BaseSingleElementGroup_T:DisclaimerGroup	18.0		1469467817638988.0
201296	261093	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:DisclaimerElement	19.0		1469467817668051.0
201309	46760834	K_becomesDetached_I:BaseGroup_T:DanmakuGroup	18.0		1469467818059821.0
201310	2794166	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:DanmakuCloseGuideElement	19.0		1469467818085030.0
201317	2574948	K_onBecomesDetached_I:DefaultPageAttachChangedListener_T:	20.0		1469467818294561.0
201318	2539687	K_onBecomesDetached_I:BaseOpenCloseGuideElement$mAttachChangedListener$1_T:	21.0		1469467818321749.0
201319	2498959	K_onBecomesDetached_I:DanmakuCloseGuideElement_T:DanmakuCloseGuideElement	22.0		1469467818355342.0
201320	1723385	K_onBecomesDetached_I:BaseOpenCloseGuideElement_T:DanmakuCloseGuideElement	23.0		1469467818389874.0
201326	248698	K_release_I:DanmakuOpenCloseGuideViewModel_T:DanmakuOpenCloseGuideViewModel	24.0		1469467818647217.0
201335	1188593	K_tryHide_I:BaseElement_T:DanmakuCloseGuideElement	24.0		1469467818914874.0
201336	1158854	K_setValue_I:KLiveData_T:KDispatchLiveData	25.0		1469467818934561.0
201337	1125260	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	26.0		1469467818957582.0
201340	1062084	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	27.0		1469467819009665.0
201341	1033750	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	28.0		1469467819028415.0
201342	1010104	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	29.0		1469467819045238.0
201343	974739	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	30.0		1469467819073051.0
201344	946510	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	31.0		1469467819093884.0
201345	913958	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	32.0		1469467819114926.0
201367	655521	K_setDanmakuTranslationYDp_I:DanmakuGroupEventBus_T:DanmakuGroupEventBus	23.0		1469467820186905.0
201369	607187	K_accept_I:DanmakuElement$doInBind$1_T:DanmakuElement$doInBind$1	24.0		1469467820227634.0
201371	572969	K_accept_I:DanmakuElement$doInBind$1_T:DanmakuElement$doInBind$1	25.0		1469467820254196.0
201373	487032	K_setDanmakuTranslationYDp_I:DanmakuViewModel_T:DanmakuViewModel	26.0		1469467820332321.0
201374	450781	K_setValue_I:KLiveData_T:KDispatchLiveData	27.0		1469467820360030.0
201375	417604	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	28.0		1469467820381176.0
201377	347240	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	29.0		1469467820444769.0
201378	317708	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	30.0		1469467820467686.0
201379	289531	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	31.0		1469467820486540.0
201381	262448	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	32.0		1469467820505967.0
201382	206875	K_onChanged_I:DanmakuElementView$onBindData$2_T:DanmakuElementView$onBindData$2	33.0		1469467820543884.0
201386	43911250	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:DanmakuElement	19.0		1469467820902009.0
201393	43658282	K_onBecomesDetached_I:DanmakuElement$mAttachChangedListener$1_T:	20.0		1469467821145342.0
201394	43625260	K_onDanmakuDetached_I:DanmakuElement_T:DanmakuElement	21.0		1469467821171436.0
201395	2119583	K_appendVideoStateParams_I:NasaDetailDanmakuUtil_T:NasaDetailDanmakuUtil	22.0		1469467821207634.0
201398	2042709	K_appendVideoStateParams_I:NasaDetailDanmakuUtil_T:NasaDetailDanmakuUtil	23.0		1469467821276644.0
201400	838281	K_isHasDanmaku_I:DanmakuSwitchUtil_T:DanmakuSwitchUtil	24.0		1469467821313884.0
201405	696145	K_isSupportDanmaku_I:DanmakuSwitchUtil_T:DanmakuSwitchUtil	25.0		1469467821441124.0
201416	346406	K_isThirdPartAdGroup_I:CommercialFeedExt_T:CommercialFeedExt	26.0		1469467821633728.0
201430	570886	K_isSupportDanmakuCompat_I:NasaDetailUtil_T:NasaDetailUtil	24.0		1469467822243519.0
201431	531875	K_isSupportDanmaku_I:DanmakuSwitchUtil_T:DanmakuSwitchUtil	25.0		1469467822269978.0
201439	250417	K_isThirdPartAdGroup_I:CommercialFeedExt_T:CommercialFeedExt	26.0		1469467822443259.0
201464	41216459	K_releaseAndReset_I:DanmakuElement_T:DanmakuElement	22.0		1469467823571071.0
201465	212344	K_log_I:DanmakuElement_T:DanmakuElement	23.0		1469467823597269.0
201478	8846354	K_getMTranslateYToDownRunnable_I:DanmakuElement_T:DanmakuElement	23.0		1469467824066592.0
201479	8796511	K_invoke_I:DanmakuElement$mTranslateYToDownRunnable$2_T:	24.0		1469467824105446.0
201480	8754844	K_invoke_I:DanmakuElement$mTranslateYToDownRunnable$2_T:	25.0		1469467824137582.0
201481	8547083	Lock contention on Class loader classes (owner tid: 18446744073709551615)	26.0		1469467824181488.0
201726	31599792	K_detach_I:DanmakuElement_T:DanmakuElement	23.0		1469467833131644.0
201748	568802	K_setState$detail_release$default_I:DanmakuElement$State_T:State	24.0		1469467833555082.0
201751	526667	K_setState$detail_release_I:DanmakuElement$State_T:State	25.0		1469************.0
201761	275052	K_apply_I:DanmakuElement$sam$androidx_arch_core_util_Function$0_T:DanmakuElement$sam$androidx_arch_core_util_Function$0	26.0		1469467833661488.0
201762	240156	K_invoke_I:DanmakuElement$mState$1_T:	27.0		1469467833687686.0
201765	206303	K_invoke_I:DanmakuElement$mState$1_T:	28.0		1469467833712321.0
201793	2562240	VerifyClass com.yxcorp.gifshow.danmaku.danmakulib.DanmakuLottieLoadHelper	24.0		1469467834482946.0
201980	874584	VerifyClass com.yxcorp.gifshow.danmaku.util.DanmakuSendGuideUtils	24.0		1469467839625915.0
201987	526198	K_getDanmakuSendGuideConfig_I:DanmakuExperimentUtils_T:DanmakuExperimentUtils	24.0		1469467840541384.0
201988	466354	K_invoke_I:DanmakuExperimentUtils$danmakuSendGuideConfig$2_T:	25.0		1469467840581280.0
201989	419167	K_invoke_I:DanmakuExperimentUtils$danmakuSendGuideConfig$2_T:	26.0		1469467840621332.0
202000	7913594	VerifyClass com.yxcorp.gifshow.danmaku.helper.BarrageSettingHelper	24.0		1469467841227790.0
202237	317031	r/a5/ed8.webp	24.0		1469467849380134.0
202280	3858177	VerifyClass com.yxcorp.gifshow.danmaku.helper.BarragePresetHelper	24.0		1469467850165342.0
202481	1249583	Lock contention on ClassLinker classes lock (owner tid: 27818)	24.0		1469467863053676.0
202577	681980	K_becomesDetached_I:BaseGroup_T:TopRightGroup	18.0		1469467864902946.0
202609	450156	K_becomesDetached$slide_play_detail_framework_release_I:BaseElement_T:SaveTrafficReminderElement	19.0		1469467865127113.0
202648	490000	K_becomesDetached_I:BaseGroup_T:PlcStrongGroup	18.0		1469467865691176.0
202649	463021	K_becomesDetached$slide_play_detail_framework_release_I:DispatchBaseElement_T:SingleStrongStyleElement	19.0		1469467865712061.0
202692	429479	K_becomesDetached_I:BaseGroup_T:BottomPanelGroup	18.0		1469467866262113.0
202730	340052	K_onBecomesDetached_I:SlidePage_T:SlidePage	18.0		1469467866747165.0
202731	210312	K_onBecomesDetached_I:BasePage_T:SlidePage	19.0		1469467866782374.0
202758	29655990	K_logStatEventAndReleaseAsync_I:SlidePlayPhotoDetailVMFragment_T:NasaGrootDetailVMFragment	16.0		1469467867437634.0
202761	298489	K_getUrl_I:SlidePlayPhotoDetailVMFragment_T:NasaGrootDetailVMFragment	17.0		1469467867589353.0
202817	28457761	K_finishLogAndReleasePlayer_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	17.0		1469467868601592.0
202819	714218	K_logPlayInfo_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	18.0		1469467868623624.0
202823	532552	K_getPlayModuleInfo_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	19.0		1469467868670655.0
202857	27653907	K_finishWithPlayerKit_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	18.0		1469467869364717.0
202861	5938854	K_finishLogWhenUsePlayerKit_I:PhotoDetailPlayLoggerHelper_T:PhotoDetailPlayLoggerHelper	19.0		1469467869476957.0
202865	1092552	K_setupLogInfo_I:PhotoDetailPlayLoggerHelper_T:PhotoDetailPlayLoggerHelper	20.0		1469467869742530.0
202866	435781	K_logPlaySourceInfo_I:PhotoDetailPlayLoggerHelper_T:PhotoDetailPlayLoggerHelper	21.0		1469467869762686.0
202867	202344	K_getCurrentPlaySource_I:QPhotoMediaPlayerImplV3_T:QPhotoMediaPlayerImplV3	22.0		1469467869895082.0
202894	269062	K_usePlayerKitReportLog_I:PhotoDetailPlayLoggerHelper_T:PhotoDetailPlayLoggerHelper	20.0		1469467871039509.0
202895	220938	K_logMsg_I:PhotoDetailPlayLoggerHelper_T:PhotoDetailPlayLoggerHelper	21.0		1469467871077894.0
203047	330104	K_run_I:EveManagerWrapper$post$1_T:	20.0		1469467874673155.0
203069	221094	K_run_I:EveManagerWrapper$post$2_T:	20.0		1469467875186280.0
203079	455261	Lock contention on InternTable lock (owner tid: 28268)	19.0		1469467875682738.0
203139	457500	K_onReportGothamPlayEvent_I:GothamInfoReportCallbackImpl_T:GothamInfoReportCallbackImpl	19.0		1469467878295759.0
203207	5364479	K_lambda$new$0_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	19.0		1469467880866384.0
203214	5293021	K_notifyPlayerStateChange_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	20.0		1469467880927478.0
203216	200625	K_logMsg_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	21.0		1469467880973415.0
203232	647969	K_lambda$new$0_I:KwaiPlayerPlayOrNotHelper_T:KwaiPlayerPlayOrNotHelper	21.0		1469467881247321.0
203234	597969	K_moveToChange_I:KwaiPlayerPlayOrNotHelper_T:KwaiPlayerPlayOrNotHelper	22.0		1469467881288207.0
203245	443958	K_notifyPlayingChanged_I:KwaiPlayerPlayOrNotHelper_T:KwaiPlayerPlayOrNotHelper	23.0		1469467881435655.0
203246	409479	K_lambda$new$0_I:PhotoDetailPlayLoggerHelper_T:PhotoDetailPlayLoggerHelper	24.0		1469467881459874.0
203248	262604	K_addPlayTime_I:PhotoDetailPlayLoggerHelper_T:PhotoDetailPlayLoggerHelper	25.0		1469467881498467.0
203278	1705521	K_lambda$new$0_I:KwaiPlayerPlayOrNotHelper_T:KwaiPlayerPlayOrNotHelper	21.0		1469467881971905.0
203281	1675156	K_moveToChange_I:KwaiPlayerPlayOrNotHelper_T:KwaiPlayerPlayOrNotHelper	22.0		1469467881992999.0
203290	1504531	K_notifyPlayingChanged_I:KwaiPlayerPlayOrNotHelper_T:KwaiPlayerPlayOrNotHelper	23.0		1469467882155759.0
203291	1463802	K_lambda$onBind$0_I:DetailCountDownPresenter_T:DetailCountDownPresenter	24.0		1469467882185551.0
203303	479791	K_get_I:Singleton_T:Singleton-627110530	25.0		1469467882512426.0
203388	1166250	K_lambda$new$0_I:KwaiPlayerPlayOrNotHelper_T:KwaiPlayerPlayOrNotHelper	21.0		1469467883707113.0
203390	1137292	K_moveToChange_I:KwaiPlayerPlayOrNotHelper_T:KwaiPlayerPlayOrNotHelper	22.0		1469467883727009.0
203396	935730	K_notifyPlayingChanged_I:KwaiPlayerPlayOrNotHelper_T:KwaiPlayerPlayOrNotHelper	23.0		1469467883920446.0
203399	890312	K_lambda$onBind$0_I:GrowthKwaiWatchVideoPresenter_T:GrowthKwaiWatchVideoPresenter	24.0		1469467883958884.0
203402	424114	K_setVideoPlaying_I:GrowthKwaiWatchVideoManager_T:GrowthKwaiWatchVideoManager	25.0		1469467884041749.0
203419	295417	K_setVideoPlaying_I:GrowthRedDotProtectionManager_T:GrowthRedDotProtectionManager	25.0		1469467884544613.0
203420	251823	K_isFeatureEnabled_I:GrowthRedDotProtectionManager_T:GrowthRedDotProtectionManager	26.0		1469467884579353.0
203436	449740	K_onPlayerStateChanged_I:MilanoItemPlayerPresenter$3_T:	21.0		1469467884949821.0
203437	225989	K_getCurrentPhoto_I:SlidePlayViewModel_T:SlidePlayViewModel	22.0		1469467884984301.0
203469	557448	K_lambda$new$0_I:KwaiPlayerPlayOrNotAysncHelper_T:KwaiPlayerPlayOrNotAysncHelper	21.0		1469467885541696.0
203472	501354	K_moveToChange_I:KwaiPlayerPlayOrNotAysncHelper_T:KwaiPlayerPlayOrNotAysncHelper	22.0		1469467885588936.0
203477	352135	K_notifyPlayingChanged_I:KwaiPlayerPlayOrNotAysncHelper_T:KwaiPlayerPlayOrNotAysncHelper	23.0		1469467885729874.0
203480	304635	K_lambda$bindShowCommentAnimListenerOnAttach$12_I:CommentElement_T:CommentElement	24.0		1469467885769874.0
203505	330937	K_releaseAsync_I:KwaiMediaPlayer_T:KwaiMediaPlayer	19.0		1469467886491280.0
203545	1487708	K_accept_I:DefaultFrameUiModule$createBinder$1$bindViewToViewModel$1_T:	19.0		1469467887813780.0
203546	1440156	K_setPlayerInterface_I:PlayerKitContentFrame_T:PlayerKitContentFrame	20.0		1469467887852738.0
203556	482240	K_releaseTextureForReuse_I:PlayerKitContentFrame_T:PlayerKitContentFrame	21.0		1469467888120915.0
203566	219583	K_internalReleaseSurfaceTexture_I:PlayerKitContentFrame_T:PlayerKitContentFrame	22.0		1469467888376488.0
203577	663907	K_setPlayerWithoutCleanSurface_I:PlayerKitContentFrame_T:PlayerKitContentFrame	21.0		1469467888621019.0
203582	528906	K_setupNewPlayer_I:PlayerKitContentFrame_T:PlayerKitContentFrame	22.0		1469467888748363.0
203583	214896	K_updateFrameShader_I:PlayerKitContentFrame_T:PlayerKitContentFrame	23.0		1469467888772217.0
203591	252812	K_bindSurfaceToPlayer_I:PlayerKitContentFrame_T:PlayerKitContentFrame	23.0		1469467889013624.0
203602	527500	K_logMsg_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	19.0		1469467889337738.0
203605	299479	K_getLogPrefix_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	20.0		1469467889381801.0
203623	4392604	K_detachKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	19.0		1469467889898988.0
203624	362188	K_logMsg_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	20.0		1469467889917842.0
203626	258542	K_getLogPrefix_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	21.0		1469467889962321.0
203641	3387344	K_innerDetach_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	20.0		1469467890302321.0
203644	3109271	K_unRegisterPlayerListeners_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	21.0		1469467890412321.0
203811	550677	K_<init>_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	20.0		1469467893704665.0
203842	245000	K_logMsg_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	19.0		1469467894643519.0
203853	1925104	K_detachKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	19.0		1469467894920134.0
203854	235989	K_logMsg_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	20.0		1469467894937374.0
203866	1211718	K_innerDetach_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	20.0		1469467895192374.0
203873	1084843	K_unRegisterPlayerListeners_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	21.0		1469467895311853.0
203935	396718	K_<init>_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	20.0		1469467896420603.0
204032	2027240	K_accept_I:SerializedRelay_T:SerializedRelay	16.0		1469467897959561.0
204033	2001406	K_accept_I:PublishRelay_T:PublishRelay	17.0		1469467897974249.0
204107	1018333	K_run_I:ObservableObserveOn$ObserveOnObserver_T:ObserveOnObserver	18.0		1469467898924249.0
204110	970938	K_lambda$init$0_I:SearchCustomSignalManager_T:SearchCustomSignalManager	19.0		1469467898960446.0
204238	1771719	K_accept_I:SerializedRelay_T:SerializedRelay	16.0		1469467900378832.0
204239	1751250	K_accept_I:PublishRelay_T:PublishRelay	17.0		1469467900392686.0
204278	864427	K_run_I:ObservableObserveOn$ObserveOnObserver_T:ObserveOnObserver	18.0		1469467901249717.0
204279	834167	K_lambda$init$0_I:SearchCustomSignalManager_T:SearchCustomSignalManager	19.0		1469467901271592.0
204304	508854	K_isLoadFromCache_I:PhotoMetaExt_T:PhotoMetaExt	20.0		1469467901580551.0
204310	435052	K_lambda$isLoadFromCache$34_I:PhotoMetaExt_T:PhotoMetaExt	21.0		1469467901646228.0
204314	335937	Lock contention on thread suspend count lock (owner tid: 27900)	22.0		1469467901684926.0
204336	1567188	K_setPhotoDetailLogger_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	16.0		1469467902397894.0
204338	588854	K_release_I:PhotoDetailPlayLoggerHelper_T:PhotoDetailPlayLoggerHelper	17.0		1469467902427009.0
204380	765000	K_<init>_I:PhotoDetailPlayLoggerHelper_T:PhotoDetailPlayLoggerHelper	17.0		1469467903116696.0
204385	372083	K_<init>_I:KwaiPlayerPlayOrNotHelper_T:KwaiPlayerPlayOrNotHelper	18.0		1469467903231384.0
204417	3697395	K_initLogger_I:SlidePlayPhotoDetailVMFragment_T:NasaGrootDetailVMFragment	16.0		1469467903988624.0
204449	450104	K_getPage2_I:NasaGrootDetailVMFragment_T:NasaGrootDetailVMFragment	17.0		1469467904580603.0
204450	370782	K_getPage2_I:BaseFragment_T:NasaGrootDetailVMFragment	18.0		1469467904598519.0
204580	246198	K_performTrackStage_I:DetailSlidePlayFragment_T:NasaGrootDetailVMFragment	16.0		1469467907731957.0
204602	3190729	K_notifyDetached_I:StickyMilanoAttachChangedListenerManager_T:StickyMilanoAttachChangedListenerManager	13.0		1469467908419405.0
204616	1664948	K_becomesDetached_I:MilanoProfileSidePresenter$4_T:	14.0		1469467908645342.0
204622	203020	K_logInfo_I:GrootLogger_T:GrootLogger	15.0		1469467908704301.0
204656	445104	K_setDetailProfilePageListLoading_I:SlidePlayViewModel_T:SlidePlayViewModel	15.0		1469467909221801.0
204683	319427	K_getDetailProfileFeedPageList_I:SlidePlayViewModel_T:SlidePlayViewModel	15.0		1469467909703571.0
204708	254531	K_getDetailProfileFeedPageList_I:SlidePlayViewModel_T:SlidePlayViewModel	15.0		1469467910038311.0
204727	330364	K_becomesDetached_I:MilanoProfileSideOptVMPresenter$mAttachListener$1_T:	14.0		1469467910348780.0
204729	272865	K_unregisterOptProfileFeedPageListLoadListener_I:SlidePlayViewModel_T:SlidePlayViewModel	15.0		1469467910392686.0
204822	1303698	K_willDisappear_I:SlideUserProcessLoggerPresenter$1_T:	13.0		1469467911816853.0
204830	549480	K_leave_I:SlidePhotoInfo_T:SlidePhotoInfo	14.0		1469467911956748.0
204832	361510	Lock contention on thread suspend count lock (owner tid: 27818)	15.0		1469467912003884.0
204854	572396	K_updateSlidePhotos_I:SlideUserProcessLoggerPresenter_T:SlideUserProcessLoggerPresenter	14.0		1469467912537894.0
204858	525260	K_printLog_I:SlideUserProcessLoggerPresenter_T:SlideUserProcessLoggerPresenter	15.0		1469467912577530.0
204888	164711562	K_notifyAttachedItem_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter	12.0		1469467913264353.0
204905	164531302	K_performViewItemWillAppear_I:GrootViewItem_T:GrootViewItem	13.0		1469467913439457.0
204908	151409167	K_performViewItemWillAppear_I:DetailSlidePlayFragment_T:NasaGrootDetailVMFragment	14.0		1469467913470394.0
204974	322135	K_getSceneExtraParams_I:DetailSlidePlayFragment_T:NasaGrootDetailVMFragment	15.0		1469467914727113.0
205035	149052552	K_performTrackPageStage_I:DetailSlidePlayFragment_T:NasaGrootDetailVMFragment	15.0		1469467915816384.0
205056	148559375	K_performViewItemWillAppear_I:GrootBaseFragment_T:NasaGrootDetailVMFragment	16.0		1469467916026696.0
205072	145454688	K_willAppear_I:SlidePlayPhotoDetailVMFragment_T:NasaGrootDetailVMFragment	17.0		1469467916189717.0
205076	225365	K_log_I:SlidePlayMonitorLogger_T:SlidePlayMonitorLogger	18.0		1469467916214769.0
205085	238958	K_performTrackStage_I:DetailSlidePlayFragment_T:NasaGrootDetailVMFragment	18.0		1469467916461540.0
205102	81164896	K_callBecomesAttachedOnPageSelectedListener_I:SlidePlayPhotoDetailVMFragment_T:NasaGrootDetailVMFragment	18.0		1469467916735915.0
205107	81073489	K_traverseListenersBecomesAttachedOrWillAppearByGroup_I:SlidePlayLifecycleHelper_T:SlidePlayLifecycleHelper	19.0		1469467916815759.0
205109	80903646	K_traverseListenersBecomesAttached_I:SlidePlayLifecycleHelper_T:SlidePlayLifecycleHelper	20.0		1469467916850863.0
205112	18767396	K_becomesAttachedOnPageSelected_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	21.0		1469467916884717.0
205114	835521	K_logPlayInfo_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	22.0		1469467916913415.0
205117	656615	K_getPlayModuleInfo_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	23.0		1469467916971592.0
205152	753854	K_logPlayInfo_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	22.0		1469467917837790.0
205155	594948	K_getPlayModuleInfo_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	23.0		1469467917888415.0
205189	213438	K_getInitPauseFlags_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	22.0		1469467918634040.0
205198	491146	K_start_I:PlayerResumePauseController_T:PlayerResumePauseController	22.0		1469467918878259.0
205214	11396979	K_handlePlayLogicWhileFragmentAttached_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	22.0		1469467919391957.0
205226	11056615	K_firstTimeToPlay_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	23.0		1469467919684144.0
205227	769115	K_logPlayInfo_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	24.0		1469467919707842.0
205230	599479	K_getPlayModuleInfo_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	25.0		1469467919759978.0
205268	805052	K_logPlayInfo_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	24.0		1469467920756644.0
205270	617448	K_getPlayModuleInfo_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	25.0		1469467920802530.0
205317	9129791	K_startPlay_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	24.0		1469467921595030.0
205319	8833802	K_requestPlay_I:PlayerResumePauseController_T:PlayerResumePauseController	25.0		1469467921620707.0
205320	483698	K_getSurface_I:DetailPlayModuleImplPlayerKit$1_T:DetailPlayModuleImplPlayerKit$1	26.0		1469467921657061.0
205322	438438	K_getSurface_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	27.0		1469467921688571.0
205324	407760	K_getSurface_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	28.0		1469467921711176.0
205325	384063	K_getSurface_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	29.0		1469467921727790.0
205327	347188	K_getSurface_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	30.0		1469467921754196.0
205329	241198	K_logMsg_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	31.0		1469467921848311.0
205362	8021146	K_resume_I:PlayerResumePauseController_T:PlayerResumePauseController	26.0		1469467922423884.0
205392	7530937	K_start_I:DetailPlayModuleImplPlayerKit$1_T:DetailPlayModuleImplPlayerKit$1	27.0		1469467922865603.0
205395	7496562	K_start_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	28.0		1469467922890603.0
205398	7461302	K_start_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	29.0		1469467922917634.0
205400	7426094	K_start_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	30.0		1469467922942738.0
205401	7373542	K_start_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	31.0		1469467922968571.0
205505	3717031	K_lambda$new$0_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	32.0		1469467924694926.0
205511	3630573	K_notifyPlayerStateChange_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3	33.0		1469467924773467.0
205555	485781	K_onPlayerStateChanged_I:MilanoItemPlayerPresenter$3_T:	34.0		1469467925404301.0
205557	241459	K_getCurrentPhoto_I:SlidePlayViewModel_T:SlidePlayViewModel	35.0		1469467925430967.0
205576	250573	K_lambda$new$1_I:SlidePlayAutoPlayNextPresenter_T:SlidePlayAutoPlayNextPresenter	34.0		1469467925917998.0
205581	200886	K_isSupportedAutoPlay_I:SlidePlayAutoPlayNextPresenter_T:SlidePlayAutoPlayNextPresenter	35.0		1469467925944873.0
205601	277448	K_onPlayerStateChanged_I:SidebarProgressElement$mPlayerStateChangedListener$2$1_T:	34.0		1469467926213988.0
205605	210312	K_setPlayState_I:SidebarProgressViewModel_T:SidebarProgressViewModel	35.0		1469467926274353.0
205627	1649375	K_onPlayerStateChanged_I:BasePlayPauseElement$addPlayerStateListener$1_T:BasePlayPauseElement$addPlayerStateListener$1	34.0		1469467926513623.0
205629	1614062	K_setPlayStatus_I:BasePlayPauseElement_T:PlayPauseCenterElement	35.0		1469467926537634.0
205633	240364	K_setPlayStatus_I:PlayPauseViewModel_T:PlayPauseViewModel	36.0		1469467926585030.0
205636	210521	K_setValue_I:KLiveData_T:KDispatchLiveData	37.0		1469467926607582.0
205648	1287864	K_onPlayStatusChanged_I:PlayPauseCenterElement_T:PlayPauseCenterElement	36.0		1469467926853207.0
205653	1032344	K_hideElement_I:PlayPauseCenterElement_T:PlayPauseCenterElement	37.0		1469467926991071.0
205655	915573	K_tryHide_I:BaseElement_T:PlayPauseCenterElement	38.0		1469467927008988.0
205656	891094	K_setValue_I:KLiveData_T:KDispatchLiveData	39.0		1469467927024613.0
205658	867760	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	40.0		1469467927040811.0
205661	808489	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	41.0		1469467927093103.0
205664	782865	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	42.0		1469467927111123.0
205666	756354	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	43.0		1469467927130394.0
205667	729323	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	44.0		1469467927146853.0
205668	698750	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	45.0		1469467927167894.0
205669	628125	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	46.0		1469467927224248.0
205859	3741875	K_startLog_I:PhotoDetailPlayLoggerHelper_T:PhotoDetailPlayLoggerHelper	22.0		1469467930823884.0
205888	436667	K_logApmFirstFrameStart_I:PhotoDetailPlayLoggerHelper_T:PhotoDetailPlayLoggerHelper	23.0		1469467931207634.0
205975	430209	K_logPreparedPlayInfo_I:PhotoDetailPlayLoggerHelper_T:PhotoDetailPlayLoggerHelper	23.0		1469467932171748.0
206155	218177	K_setPage_I:PlayerLoggerWrapper_T:PlayerLoggerWrapper	23.0		1469467933692582.0
206204	1038333	K_initPlayerKitLog_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit	22.0		1469467934600238.0
206212	207188	K_usePlayerKitReportLog_I:PhotoDetailPlayLoggerHelper_T:PhotoDetailPlayLoggerHelper	23.0		1469467934946748.0
206221	296354	K_bindPlayerKitView_I:PhotoDetailPlayLoggerHelper_T:PhotoDetailPlayLoggerHelper	23.0		1469467935184405.0
206237	1403906	K_becomesAttachedOnPageSelected_I:SlidePlayerAttachLoggerPresenter$1_T:	21.0		1469467935900551.0
206239	590573	K_getCurrentItemRealPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	22.0		1469467935947946.0
206245	439635	K_getCurrentRealPositionInAdapter_I:PositionService_T:PositionService	23.0		1469467936091176.0
206246	237448	K_getCurrentItemPositionInViewPager_I:PositionService_T:PositionService	24.0		1469467936110915.0
206247	206302	K_logInfo_I:GrootLogger_T:GrootLogger	25.0		1469467936133519.0
206267	357239	K_getAdapterLastValidItemPosition_I:SlidePlayViewModel_T:SlidePlayViewModel	22.0		1469467936572478.0
206273	207239	K_getLastValidItemPosition_I:PositionService_T:PositionService	23.0		1469467936715134.0
206281	322292	K_getCurrentShowIndex_I:SlidePlayViewModel_T:SlidePlayViewModel	22.0		1469467936950394.0
206308	3164011	K_becomesAttachedOnPageSelected_I:SlidePlayEnterActionLogPresenter$1_T:	21.0		1469467937337842.0
206311	3071511	K_getItemEnterType_I:SlidePlayViewModel_T:SlidePlayViewModel	22.0		1469467937363623.0
206315	2928489	K_getGrootService_I:SlidePlayViewModel_T:SlidePlayViewModel	23.0		1469467937402009.0
206317	2899531	K_getService_I:GrootController_T:KwaiGrootController	24.0		1469467937422530.0
206319	2823907	FullSuspendCheck	25.0		1469467937431071.0
206320	911771	Lock contention on thread suspend count lock (owner tid: 27818)	26.0		1469467937441332.0
206402	13782031	K_becomesAttachedOnPageSelected_I:SlidePlayRealShowPresenter$1_T:	21.0		1469467940528259.0
206426	308385	K_getCurrentShowIndex_I:SlidePlayViewModel_T:SlidePlayViewModel	22.0		1469467940858780.0
206440	274323	K_getCurrentShowIndex_I:SlidePlayViewModel_T:SlidePlayViewModel	22.0		1469467941187998.0
206458	4014479	K_lambda$onBind$0_I:NasaEveRankPresenter_T:NasaEveRankPresenter	22.0		1469467941598519.0
206664	270729	K_getCardId_I:SlidePlayRealShowPresenter_T:SlidePlayRealShowPresenter	22.0		1469467946088676.0
206666	240313	K_getPageList_I:SlidePlayRealShowPresenter_T:SlidePlayRealShowPresenter	23.0		1469467946106019.0
206668	206823	K_getPageList_I:SlidePlayViewModel_T:SlidePlayViewModel	24.0		1469467946133103.0
206717	1236354	K_addNormalRealShowModel_I:SlidePlayRealShowPresenter_T:SlidePlayRealShowPresenter	22.0		1469467946883311.0
206730	870886	K_get_I:Singleton_T:Singleton-1261527171	23.0		1469467947094196.0
206732	824687	Result:class com.yxcorp.gifshow.log.LogManager	24.0		1469467947104301.0
206759	361042	K_getCurrentShowIndex_I:SlidePlayRealShowPresenter_T:SlidePlayRealShowPresenter	22.0		1469467948139717.0
206761	335312	K_getCurrentShowIndex_I:SlidePlayViewModel_T:SlidePlayViewModel	23.0		1469467948158780.0
206768	204531	K_getCurrentItemPositionInViewPager_I:PositionService_T:PositionService	24.0		1469467948283259.0
206774	1545520	K_logPhotoShow_I:SlidePlayRealShowPresenter_T:SlidePlayRealShowPresenter	22.0		1469467948518103.0
206780	1393854	K_logShowEvent_I:DetailPhotoLogPresenter$2_T:	23.0		1469467948658207.0
206813	737396	K_expandContentPackage_I:SlidePlayRealShowPresenter$2_T:SlidePlayRealShowPresenter$2	24.0		1469467949200082.0
206954	1830938	K_accept_I:SerializedRelay_T:SerializedRelay	22.0		1469467950919040.0
206955	1811042	K_accept_I:PublishRelay_T:PublishRelay	23.0		1469467950932842.0
206958	508229	K_run_I:ObservableObserveOn$ObserveOnObserver_T:ObserveOnObserver	24.0		1469467950978103.0
207034	434323	K_run_I:ObservableObserveOn$ObserveOnObserver_T:ObserveOnObserver	24.0		1469467951870759.0
207118	1292396	K_accept_I:SerializedRelay_T:SerializedRelay	22.0		1469467953010759.0
207120	1272239	K_accept_I:PublishRelay_T:PublishRelay	23.0		1469467953024353.0
207121	576823	K_run_I:ObservableObserveOn$ObserveOnObserver_T:ObserveOnObserver	24.0		1469467953050342.0
207122	550156	K_accept_I:KgiRedPacketManager$sam$io_reactivex_functions_Consumer$0_T:KgiRedPacketManager$sam$io_reactivex_functions_Consumer$	25.0		1469467953069926.0
207124	512553	K_invoke_I:KgiRedPacketManager$init$1_T:KgiRedPacketManager$init$1	26.0		1469467953100498.0
207126	487188	K_invoke_I:KgiRedPacketManager$init$1_T:KgiRedPacketManager$init$1	27.0		1469467953117894.0
207131	453594	K_onPhotoPLayEvent_I:KgiRedPacketManager_T:KgiRedPacketManager	28.0		1469467953145238.0
207150	307083	K_onVideoWatch_I:KgiRedPacketManager_T:KgiRedPacketManager	29.0		1469467953286540.0
207193	290729	K_run_I:ObservableObserveOn$ObserveOnObserver_T:ObserveOnObserver	24.0		1469467953654144.0
207232	281666	K_run_I:ObservableObserveOn$ObserveOnObserver_T:ObserveOnObserver	24.0		1469467953989405.0
207234	247864	K_accept_I:PrtpWatcher$sam$io_reactivex_functions_Consumer$0_T:PrtpWatcher$sam$io_reactivex_functions_Consumer$0	25.0		1469467954016332.0
207290	1036614	K_becomesAttachedOnPageSelected_I:NasaTextureAdapterLoggerPresenter$1_T:	21.0		1469467954558259.0
207303	808750	K_initLoggerParams_I:NasaTextureAdapterLoggerPresenter_T:NasaTextureAdapterLoggerPresenter	22.0		1469467954778884.0
207394	1621511	K_becomesAttachedOnPageSelected_I:SlideLoadingStatePresenter$mAttachChangedListener$1_T:	21.0		1469467955619821.0
207396	804948	K_createPlayerLoadingHelperIfNeed_I:SlideLoadingStatePresenter_T:SlideLoadingStatePresenter	22.0		1469467955639717.0
207520	1214687	K_becomesAttachedOnPageSelected_I:SlidePlayScreenVMPresenter$1_T:	21.0		1469467957291436.0
207557	801927	K_showScreenCleanType_I:SlidePlayScreenVMPresenter_T:NasaSlidePlayScreenVMPresenter	22.0		1469467957694821.0
207561	687812	K_showOrClearScreen_I:SlideV2Utils_T:SlideV2Utils	23.0		1469467957770551.0
207586	369583	K_becomesAttachedOnPageSelected_I:SlidePlayPhotoPreloadOptPresenter$1_T:	21.0		1469467958544040.0
207616	208021	K_becomesAttachedOnPageSelected_I:SlidePlayVoteStickerPresenter$1_T:	21.0		1469467958947165.0
207644	246666	K_becomesAttachedOnPageSelected_I:SlidePlayPostRecommendPresenter$1_T:	21.0		1469467959427478.0
207688	6454271	K_becomesAttachedOnPageSelected_I:NasaMilanoProgressPresenter$1_T:	21.0		1469467959992061.0
207700	250469	K_becomesAttached_I:NasaMilanoProgress_T:NasaMilanoProgress	22.0		1469467960100238.0
207731	1316093	K_getSpeed_I:ProgressUtil_T:ProgressUtil	22.0		1469467960459301.0
207732	1092500	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	23.0		1469467960484301.0
207735	357708	K_logMsg_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	24.0		1469467960521853.0
207741	239948	K_getLogPrefix_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	25.0		1469467960564717.0
207771	664687	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	24.0		1469467960902478.0
207775	302240	K_logMsg_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	25.0		1469467960922686.0
207814	317604	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	25.0		1469467961243363.0
207815	258541	K_logMsg_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	26.0		1469467961261176.0
207875	418646	K_getUpdateInterval_I:ProgressUtil_T:ProgressUtil	22.0		1469467961881436.0
207915	1074739	K_getAnimatorUpdateThreshold_I:ProgressUtil_T:ProgressUtil	22.0		1469467962339301.0
207957	350520	K_enableSmoothProgress_I:NasaMilanoProgress_T:NasaMilanoProgress	22.0		1469467963443676.0
207964	220417	K_resetProgress_I:NasaFeaturedSeekBar_T:NasaFeaturedSeekBar	23.0		1469467963566696.0
208011	574843	K_lambda$becomesAttachedOnPageSelected$1_I:NasaMilanoProgressPresenter$1_T:	22.0		1469467964392478.0
208013	537343	K_showProgressBar_I:NasaMilanoProgressPresenter_T:NasaMilanoProgressPresenter	23.0		1469467964418103.0
208015	511354	K_showProgressBar_I:NasaMilanoProgressPresenter_T:NasaMilanoProgressPresenter	24.0		1469467964438311.0
208024	399844	K_accept_I:NasaBottomBarDividerPresenter$onBind$1_T:	25.0		1469467964543623.0
208027	370469	K_accept_I:NasaBottomBarDividerPresenter$onBind$1_T:	26.0		1469467964566592.0
208119	345937	K_showProgressBar_I:NasaMilanoProgressPresenter_T:NasaMilanoProgressPresenter	22.0		1469467966094509.0
208120	318646	K_showProgressBar_I:NasaMilanoProgressPresenter_T:NasaMilanoProgressPresenter	23.0		1469467966116123.0
208122	264375	K_accept_I:NasaBottomBarDividerPresenter$onBind$1_T:	24.0		1469467966163780.0
208123	230885	K_accept_I:NasaBottomBarDividerPresenter$onBind$1_T:	25.0		1469467966190811.0
208132	478645	K_becomesAttachedOnPageSelected_I:NasaTimeManagementPresenter$mAttachChangedListener$1_T:	21.0		1469467966536801.0
208133	446407	K_doNoticeCheck_I:NasaTimeManagementPresenter_T:NasaTimeManagementPresenter	22.0		1469467966560446.0
208162	331615	K_becomesAttachedOnPageSelected_I:PhotoPlayCommonSceneDetectorPresenter$mAttachChangeListener$1_T:	21.0		1469467967122113.0
208189	1234479	K_becomesAttachedOnPageSelected_I:MilanoItemProfileSidePresenter$2_T:	21.0		1469467967665186.0
208193	737448	K_updateLoggerCustomKsOrderList_I:MilanoItemProfileSidePresenter_T:MilanoItemProfileSidePresenter	22.0		1469467967733259.0
208200	646771	K_setCustomKsOrderList_I:MilanoItemProfileSidePresenter_T:MilanoItemProfileSidePresenter	23.0		1469467967818467.0
208241	387031	K_updateOperationBarVisibility_I:MilanoItemProfileSidePresenter_T:MilanoItemProfileSidePresenter	22.0		1469467968507113.0
208275	1097032	K_becomesAttachedOnPageSelected_I:BaseFeatureFollowPresenter$mAttachChangedListener$1_T:	21.0		1469467968929821.0
208277	774635	K_initFollowStatus_I:BaseFeatureFollowPresenter_T:SlidePlayGlobalFollowPresenter	22.0		1469467968953988.0
208280	558958	K_isFollowStatus_I:BaseFeatureFollowPresenter_T:SlidePlayGlobalFollowPresenter	23.0		1469467968989405.0
208330	724687	K_becomesAttachedOnPageSelected_I:SlidePlayGlobalUserInfoPresenter$1_T:	21.0		1469467970095030.0
208331	310156	K_getUserName_I:SlidePlayGlobalUserInfoPresenter_T:NasaUserInfoPresenter	22.0		1469467970120603.0
208346	287500	K_initActivityUserIcon_I:SlidePlayGlobalUserInfoPresenter_T:NasaUserInfoPresenter	22.0		1469467970526436.0
208359	237136	K_becomesAttachedOnPageSelected_I:SlidePlayAutoPlayNextPresenter$1_T:	21.0		1469467970859040.0
208371	257917	K_becomesAttachedOnPageSelected_I:NasaPageSelectPresenter$1_T:	21.0		1469467971183623.0
208401	543020	K_becomesAttachedOnPageSelected_I:ClickPostWorkInfoShareGuidePresenter$1_T:ClickPostWorkInfoShareGuidePresenter$1	21.0		1469467972171176.0
208404	465625	K_showSharePanel_I:ClickPostWorkInfoShareGuidePresenter_T:ClickPostWorkInfoShareGuidePresenter	22.0		1469467972243051.0
208437	380833	K_becomesAttachedOnPageSelected_I:WelcomeBackLoginPresenter$1_T:	21.0		1469467973081905.0
208439	243438	K_checkWelcomeBackDialogShow_I:WelcomeBackLoginPresenter_T:WelcomeBackLoginPresenter	22.0		1469467973213623.0
208447	940937	K_becomesAttachedOnPageSelected_I:SlideFeedPresenter$mAttachChangedListener$1_T:	21.0		1469467973497061.0
208453	324011	K_updateFloatWidgetTypeByCurrQPhoto_I:FloatWidgetPluginImpl_T:FloatWidgetPluginImpl	22.0		1469467973656748.0
208490	1790000	K_becomesAttachedOnPageSelected_I:ActionTriggerVMPresenter$4_T:	21.0		1469467974468051.0
208492	1446303	K_startListenerStatus_I:ActionTriggerVMPresenter_T:ActionTriggerVMPresenter	22.0		1469467974540498.0
208515	694167	K_setPlayer_I:PlayerProgressUpdater_T:PlayerProgressUpdater	23.0		1469467974861123.0
208602	239688	K_updateCurrentStatus_I:ActionTriggerVMPresenter_T:ActionTriggerVMPresenter	22.0		1469467976012321.0
208624	664063	K_becomesAttachedOnPageSelected_I:SlidePlayPictureTagPresenter$mAttachChangedListener$1_T:	21.0		1469467976325915.0
208626	217188	K_handlePicTagExp_I:SlidePlayPictureTagPresenter_T:SlidePlayPictureTagPresenter	22.0		1469467976366696.0
208651	337083	K_becomesAttachedOnPageSelected_I:WolverinePhotoDetailLoggerPresenter$mAttachChangedListener$1_T:	21.0		1469467977029926.0
208665	11668542	K_becomesAttachedOnPageSelected_I:RedesignPlayProgressEventPresenter$1_T:	21.0		1469467977408415.0
208667	11358594	K_initProgressUpdateHandler_I:RedesignPlayProgressEventPresenter_T:RedesignPlayProgressEventPresenter	22.0		1469467977444717.0
208671	11308125	K_getScheduleHandlerIntervalMs_I:RedesignPlayProgressEventPresenter_T:RedesignPlayProgressEventPresenter	23.0		1469467977481957.0
208676	10955521	K_getSpeed_I:ProgressUtil_T:ProgressUtil	24.0		1469467977531332.0
208677	1332500	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	25.0		1469467977567634.0
208678	483698	K_logMsg_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	26.0		1469467977588207.0
208680	367395	K_getLogPrefix_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	27.0		1469467977630603.0
208703	797760	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	26.0		1469467978094301.0
208705	361042	K_logMsg_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	27.0		1469467978110967.0
208717	388281	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	27.0		1469467978498311.0
208718	302136	K_logMsg_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	28.0		1469467978517269.0
208728	9459427	K_lambda$setKsMediaPlayerInitConfig$4_I:KsMediaPlayerInitModule_T:KsMediaPlayerInitModule	25.0		1469467978996540.0
208730	9280521	Lock contention on Region lock (owner tid: 27818)	26.0		1469467979092842.0
208890	270469	K_getUpdateInterval_I:ProgressUtil_T:ProgressUtil	24.0		1469467988514509.0
208971	7970208	K_becomesAttachedOnPageSelected_I:OppoPreventBurnInHelper$attachChangedListener$1_T:	21.0		1469467989502530.0
208992	7787032	monitor contention with owner AnrAsyncSched (56) at void com.kwai.performance.stability.crash.monitor.anr.async.AsyncScheduleManager.run()(SourceFile:18) waiters=0 blocking from boolean android.os.MessageQueue.enqueueMessage(android.os.Message		22.0	
208994	7758594	Lock contention on a monitor lock (owner tid: 27894)	23.0		1469467989686123.0
209478	62707969	K_becomesAttached_I:BasePage_T:SlidePage	18.0		1469467997929509.0
209482	62607604	K_becomesAttachInner_I:BasePage_T:SlidePage	19.0		1469467998023155.0
209487	3576250	K_becomesAttached_I:BaseGroup_T:SideProgressGroup	20.0		1469467998110863.0
209489	3546197	K_becomesAttached$slide_play_detail_framework_release_I:DispatchBaseElement_T:SidebarProgressElement	21.0		1469467998134926.0
209500	3391980	K_onBecomesAttached_I:SidebarProgressElement$mPhotoDetailAttachChangedListener$1_T:	22.0		1469467998282321.0
209504	267968	K_setDuration_I:SidebarProgressViewModel_T:SidebarProgressViewModel	23.0		1469467998327426.0
209591	1305521	K_checkCleanState_I:SidebarProgressElement_T:SidebarProgressElement	23.0		1469467999686332.0
209593	377396	K_showProgress_I:SidebarProgressElement_T:SidebarProgressElement	24.0		1469467999726332.0
209595	315312	K_isPaused_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	25.0		1469467999781436.0
209596	294115	K_isPaused_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	26.0		1469467999796488.0
209597	271615	K_isPaused_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	27.0		1469467999810290.0
209608	646562	K_tryHide_I:BaseElement_T:SidebarProgressElement	24.0		1469468000120030.0
209609	624114	K_setValue_I:KLiveData_T:KDispatchLiveData	25.0		1469468000136957.0
209610	602865	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	26.0		1469468000152686.0
209613	549375	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	27.0		1469468000200446.0
209614	528125	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	28.0		1469468000216644.0
209615	509271	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	29.0		1469468000230446.0
209616	488489	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	30.0		1469468000245759.0
209617	456406	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	31.0		1469468000271436.0
209618	433698	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	32.0		1469468000286905.0
209657	207239	K_getCurrProgress_I:SwipeToProfileFeedMovement_T:SwipeToProfileFeedMovement	23.0		1469468001069301.0
209695	12445729	K_becomesAttached_I:BaseGroup_T:InformationGroup	20.0		1469468002132530.0
209720	2537864	K_becomesAttached$slide_play_detail_framework_release_I:DispatchBaseElement_T:RecoReasonElement	21.0		1469468002418155.0
209726	2424531	K_onBecomesAttached_I:RecoReasonElement$1_T:	22.0		1469468002524978.0
209755	686666	K_hideIfNeed_I:RecoReasonElement_T:RecoReasonElement	23.0		1469468002870707.0
209769	516770	K_tryHide_I:BaseElement_T:RecoReasonElement	24.0		1469468003035551.0
209770	499791	K_setValue_I:KLiveData_T:KDispatchLiveData	25.0		1469468003048207.0
209772	478386	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	26.0		1469468003064821.0
209774	443802	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	27.0		1469468003094665.0
209775	426875	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	28.0		1469468003106905.0
209777	409427	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	29.0		1469468003119301.0
209778	388541	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	30.0		1469468003135082.0
209779	368906	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	31.0		1469468003150030.0
209781	348854	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	32.0		1469468003163676.0
209803	266719	K_initRecoContent_I:RecoReasonElement_T:RecoReasonElement	23.0		1469468003600186.0
209823	951563	K_hideRecoTag_I:RecoReasonElement_T:RecoReasonElement	23.0		1469468003992998.0
209838	750104	K_hideIfNeed_I:RecoReasonElement_T:RecoReasonElement	24.0		1469468004189353.0
209852	583177	K_tryHide_I:BaseElement_T:RecoReasonElement	25.0		1469468004350915.0
209853	566667	K_setValue_I:KLiveData_T:KDispatchLiveData	26.0		1469468004362738.0
209854	550312	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	27.0		1469468004374561.0
209857	511146	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	28.0		1469468004408936.0
209858	491146	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	29.0		1469468004421644.0
209860	475052	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	30.0		1469468004433051.0
209862	455938	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	31.0		1469468004445290.0
209863	428646	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	32.0		1469468004467113.0
209864	404583	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	33.0		1469468004479926.0
209925	316510	K_becomesAttached$slide_play_detail_framework_release_I:DispatchBaseElement_T:CoCreatorAuthorsElement	21.0		1469468005514926.0
209935	200573	K_onBecomesAttached_I:PageAttachChangedListener_T:	22.0		1469468005625342.0
209958	6177604	K_becomesAttached$slide_play_detail_framework_release_I:DispatchBaseElement_T:MusicLabelElement	21.0		1469468006143676.0
209963	6066146	K_onBecomesAttached_I:MusicLabelElement$1_T:	22.0		1469468006246332.0
209972	5389947	K_initMusicLabel_I:MusicLabelElement_T:MusicLabelElement	23.0		1469468006439301.0
209974	1060157	K_enableStartMarquee_I:MusicLabelElement_T:MusicLabelElement	24.0		1469468006476071.0
210003	1222396	K_setEnableMarquee_I:MusicLabelViewModel_T:MusicLabelViewModel	24.0		1469468007712113.0
210005	1188334	K_setValue_I:KLiveData_T:KDispatchLiveData	25.0		1469468007740394.0
210007	1165625	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	26.0		1469468007757790.0
210009	1118750	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	27.0		1469468007799092.0
210011	1092761	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	28.0		1469468007819196.0
210012	1071928	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	29.0		1469468007834248.0
210013	1052031	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	30.0		1469468007847790.0
210014	1020625	K_onChanged_I:MusicLabelElementView$onBindDataWithMusic$2_T:MusicLabelElementView$onBindDataWithMusic$2	31.0		1469468007873415.0
210015	985260	K_onChanged_I:MusicLabelElementView$onBindDataWithMusic$2_T:MusicLabelElementView$onBindDataWithMusic$2	32.0		1469468007903311.0
210021	689427	K_updateLayout_I:MusicLabelElementView_T:MusicLabelElementView	33.0		1469468007996436.0
210056	2655260	K_setMusicLabel_I:MusicLabelElement_T:MusicLabelElement	24.0		1469468008959509.0
210058	622708	K_formatKtvScoreInfoText_I:SlideKtvHelper_T:SlideKtvHelper	25.0		1469468008982530.0
210059	241980	K_isKtvLabel_I:SlideKtvHelper_T:SlideKtvHelper	26.0		1469468009008623.0
210084	314375	K_isChorusLabel_I:SlideKtvHelper_T:SlideKtvHelper	26.0		1469468009283207.0
210112	296927	K_isKtvLabel_I:SlideKtvHelper_T:SlideKtvHelper	25.0		1469468009630030.0
210134	261354	K_isChorusLabel_I:SlideKtvHelper_T:SlideKtvHelper	25.0		1469468009966905.0
210162	1080104	K_setTitle_I:MusicLabelViewModel_T:MusicLabelViewModel	25.0		1469468010529040.0
210163	1040677	K_setValue_I:KLiveData_T:KDispatchLiveData	26.0		1469468010562842.0
210164	1015989	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	27.0		1469468010578676.0
210167	963959	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	28.0		1469468010625394.0
210168	802448	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	29.0		1469468010781905.0
210174	712187	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	30.0		1469468010867686.0
210175	694219	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	31.0		1469468010880967.0
210176	667344	K_onChanged_I:MusicLabelElementView$onBindDataWithMusic$3_T:MusicLabelElementView$onBindDataWithMusic$3	32.0		1469468010903363.0
210177	641719	K_onChanged_I:MusicLabelElementView$onBindDataWithMusic$3_T:MusicLabelElementView$onBindDataWithMusic$3	33.0		1469468010924821.0
210188	468282	K_updateLayout_I:MusicLabelElementView_T:MusicLabelElementView	34.0		1469468011092998.0
210234	346250	K_startOrPauseMarquee_I:MusicLabelElement_T:MusicLabelElement	23.0		1469468011961123.0
210235	324114	K_startMarquee_I:MusicLabelElement_T:MusicLabelElement	24.0		1469468011978728.0
210237	277447	K_setMarqueeState_I:MusicLabelViewModel_T:MusicLabelViewModel	25.0		1469468012020551.0
210238	259010	K_setValue_I:KLiveData_T:KDispatchLiveData	26.0		1469468012034301.0
210239	241927	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	27.0		1469468012046853.0
210271	258594	K_becomesAttached$slide_play_detail_framework_release_I:BaseElement_T:PushNegativeFeedbackEntryElement	21.0		1469468012472998.0
210316	216562	K_becomesAttached$slide_play_detail_framework_release_I:DispatchBaseElement_T:QuestionnaireElement	21.0		1469468013117530.0
210397	265625	K_becomesAttached$slide_play_detail_framework_release_I:BaseElement_T:ShootWithHimElement	21.0		1469468014017634.0
210474	776302	K_becomesAttached_I:BaseGroup_T:BottomGroup	20.0		1469468015007634.0
210537	7441823	K_becomesAttached_I:BaseGroup_T:RightActionBarGroup	20.0		1469468015856488.0
210547	1041093	K_becomesAttached$slide_play_detail_framework_release_I:DispatchBaseElement_T:AvatarAndFollowElement	21.0		1469468015985551.0
210554	925261	K_onBecomesAttached_I:AvatarAndFollowBaseElement$4_T:	22.0		1469468016092373.0
210578	418698	K_cantShowGuide_I:AvatarAndFollowBaseElement_T:AvatarAndFollowElement	23.0		1469468016411696.0
210629	800104	K_becomesAttached$slide_play_detail_framework_release_I:DispatchBaseElement_T:LikeElement	21.0		1469468017197269.0
210631	651510	K_isDispatchEnable_I:DispatchBaseElement_T:LikeElement	22.0		1469468017211905.0
210633	628333	K_allowAsync_I:DispatchBaseElement_T:LikeElement	23.0		1469468017225915.0
210634	414219	Lock contention on thread suspend count lock (owner tid: 27898)	24.0		1469468017231644.0
210695	952187	K_becomesAttached$slide_play_detail_framework_release_I:DispatchBaseElement_T:CommentElement	21.0		1469468018022426.0
210707	687500	K_onBecomesAttached_I:CommentElement$8_T:	22.0		1469468018278676.0
210710	313073	K_tryToStartGodCommentTipView_I:CommentElement_T:CommentElement	23.0		1469468018327738.0
210748	272448	K_setDanmakuTipBubbleDismiss_I:CommentViewModel_T:CommentViewModel	23.0		1469468018684144.0
210749	239010	K_setValue_I:KLiveData_T:KDispatchLiveData	24.0		1469468018703832.0
210750	207552	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	25.0		1469468018723415.0
210828	3905521	K_becomesAttached$slide_play_detail_framework_release_I:BaseElement_T:MusicWheelElement	21.0		1469468019385811.0
210851	3556875	K_onBecomesAttached_I:MusicWheelElement$mAttachChangedListener$1_T:	22.0		1469468019645863.0
210854	1114479	K_allowRotate_I:MusicWheelElement_T:MusicWheelElement	23.0		1469468019674613.0
210919	2377761	K_updateCover_I:MusicWheelElement_T:MusicWheelElement	23.0		1469468020817790.0
210946	2046459	K_setCoverUrls_I:MusicWheelViewModel_T:MusicWheelViewModel	24.0		1469468021142946.0
210948	2013646	K_setValue_I:KLiveData_T:KLiveData	25.0		1469468021169613.0
210950	1960000	K_notifyValueSet_I:KLiveData_T:KLiveData	26.0		1469468021209926.0
210952	1919010	K_notifyValueSet_I:KLiveData_T:KLiveData	27.0		1469468021242634.0
210953	1885052	K_onChanged_I:MusicWheelElementView$onBindData$1_T:MusicWheelElementView$onBindData$1	28.0		1469468021267426.0
210956	1850989	K_onChanged_I:MusicWheelElementView$onBindData$1_T:MusicWheelElementView$onBindData$1	29.0		1469468021293207.0
210969	349687	AbstractDraweeControllerBuilder#buildController	30.0		1469468021578884.0
210972	318593	PipelineDraweeControllerBuilder#obtainController	31.0		1469468021595655.0
210993	1157291	AbstractDraweeController#onAttach	30.0		1469468021973728.0
210995	1134323	AbstractDraweeController#submitRequest	31.0		1469468021986696.0
211004	306458	ImagePipeline#submitFetchRequest	32.0		1469468022091801.0
211005	275208	CloseableProducerToDataSourceAdapter#create	33.0		1469468022116436.0
211006	255990	AbstractProducerToDataSourceAdapter()	34.0		1469468022127061.0
211028	622760	K_run_I:AbstractDataSource$1_T:	32.0		1469468022436228.0
211052	329844	K_run_I:AbstractDataSource$1_T:	33.0		1469468022717061.0
211060	251562	ImagePipeline#submitFetchRequest	34.0		1469468022785811.0
211062	229323	CloseableProducerToDataSourceAdapter#create	35.0		1469468022800967.0
211063	212917	AbstractProducerToDataSourceAdapter()	36.0		1469468022808519.0
211121	1865625	K_becomesAttached_I:BaseGroup_T:PlayControllerGroup	20.0		1469468023387790.0
211124	1841771	K_becomesAttached$slide_play_detail_framework_release_I:DispatchBaseElement_T:ProgressPreviewElement	21.0		1469468023405759.0
211144	1678542	K_onBecomesAttached_I:ProgressPreviewElement$mAttachChangedListener$2$1_T:	22.0		1469468023562165.0
211147	1648021	K_onElementAttached_I:ProgressPreviewElement_T:ProgressPreviewElement	23.0		1469468023587113.0
211173	203125	K_setTotalDuration_I:ProgressPreviewViewModel_T:ProgressPreviewViewModel	24.0		1469468023808623.0
211255	755000	K_tryHide_I:BaseElement_T:ProgressPreviewElement	24.0		1469468024472217.0
211259	730990	K_setValue_I:KLiveData_T:KDispatchLiveData	25.0		1469468024490915.0
211262	707813	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	26.0		1469468024506123.0
211271	633750	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	27.0		1469468024571071.0
211275	612136	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	28.0		1469468024586696.0
211279	578958	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	29.0		1469468024613676.0
211283	555781	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	30.0		1469468024628988.0
211286	520521	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	31.0		1469468024658207.0
211288	495573	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	32.0		1469468024675603.0
211346	353177	K_becomesAttached_I:BaseSingleElementGroup_T:NegativeFeedbackGroup	20.0		1469468025341801.0
211349	325989	K_becomesAttached$slide_play_detail_framework_release_I:DispatchBaseElement_T:NegativeFeedbackElement	21.0		1469468025363207.0
211376	320885	K_becomesAttached_I:BaseSingleElementGroup_T:EmptyPhotoGroup	20.0		1469468025860655.0
211378	287656	K_becomesAttached$slide_play_detail_framework_release_I:DispatchBaseElement_T:EmptyPhotoNoteElement	21.0		1469468025887790.0
211410	200365	K_becomesAttached_I:BaseSingleElementGroup_T:SerialPayPhotoGroup	20.0		1469468026271071.0
211444	4270261	K_becomesAttached_I:BaseGroup_T:FullScreenLayerGroup	20.0		1469468026550446.0
211448	381927	K_becomesAttached$slide_play_detail_framework_release_I:DispatchBaseElement_T:PlayFailedRetryElement	21.0		1469468026569717.0
211467	230260	K_onBecomesAttached_I:PlayFailedRetryElement$3_T:	22.0		1469468026715603.0
211496	2956875	K_becomesAttached$slide_play_detail_framework_release_I:DispatchBaseElement_T:PlayPauseCenterElement	21.0		1469468026978155.0
211516	2775468	K_onBecomesAttached_I:BasePlayPauseElement$mAttachChangedListener$2$1_T:	22.0		1469468027151801.0
211524	2090469	K_reset_I:PlayPauseCenterElement_T:PlayPauseCenterElement	23.0		1469468027200967.0
211526	2025781	K_reset_I:BasePlayPauseElement_T:PlayPauseCenterElement	24.0		1469468027227113.0
211556	1698177	K_setPlayStatus_I:BasePlayPauseElement_T:PlayPauseCenterElement	25.0		1469468027543103.0
211560	204375	K_setPlayStatus_I:PlayPauseViewModel_T:PlayPauseViewModel	26.0		1469468027593311.0
211581	1412292	K_onPlayStatusChanged_I:PlayPauseCenterElement_T:PlayPauseCenterElement	26.0		1469468027820498.0
211592	1159844	K_hideElement_I:PlayPauseCenterElement_T:PlayPauseCenterElement	27.0		1469468027925863.0
211596	762344	K_tryHide_I:BaseElement_T:PlayPauseCenterElement	28.0		1469468027948363.0
211598	738073	K_setValue_I:KLiveData_T:KDispatchLiveData	29.0		1469468027967998.0
211599	718750	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	30.0		1469468027982582.0
211604	658177	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	31.0		1469468028038467.0
211606	628906	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	32.0		1469468028063207.0
211607	604167	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	33.0		1469468028077790.0
211610	576458	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	34.0		1469468028094509.0
211612	551823	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	35.0		1469468028113728.0
211615	523750	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	36.0		1469468028133363.0
211664	295521	K_lambda$becomesAttachedOnPageSelected$6_I:NasaMilanoProgressPresenter$1_T:	28.0		1469468028757373.0
211665	259739	K_changeSeekBarStatus_I:NasaMilanoProgressPresenter_T:NasaMilanoProgressPresenter	29.0		1469468028787009.0
211666	231094	K_setSelected_I:IMilanoProgress_T:NasaMilanoProgress	30.0		1469468028809873.0
211667	209427	K_onSelectedChanged_I:NasaMilanoProgress_T:NasaMilanoProgress	31.0		1469468028825082.0
211693	608333	K_onElementAttached_I:PlayPauseCenterElement_T:PlayPauseCenterElement	23.0		1469468029312426.0
211719	324271	K_apply_I:PlayPauseCenterElement$onElementAttached$1_T:	24.0		1469468029587582.0
211723	280364	K_apply_I:PlayPauseCenterElement$onElementAttached$1_T:	25.0		1469468029625082.0
211765	439010	K_becomesAttached$slide_play_detail_framework_release_I:BaseElement_T:PayCourseTrailFinishElement	21.0		1469468030233051.0
211777	201979	K_getCurrentElementName_I:BaseElement_T:PayCourseTrailFinishElement	22.0		1469468030437061.0
211800	3136197	Lock contention on InternTable lock (owner tid: 27898)	20.0		1469468030832426.0
211906	3965156	K_becomesAttached_I:BaseGroup_T:DanmakuGroup	20.0		1469468034276905.0
211908	433385	K_becomesAttached$slide_play_detail_framework_release_I:DispatchBaseElement_T:DanmakuCloseGuideElement	21.0		1469468034295655.0
211920	308802	K_onBecomesAttached_I:DefaultPageAttachChangedListener_T:	22.0		1469468034414769.0
211922	284063	K_onBecomesAttached_I:BaseOpenCloseGuideElement$mAttachChangedListener$1_T:	23.0		1469468034434873.0
211923	256511	K_onBecomesAttached_I:DanmakuCloseGuideElement_T:DanmakuCloseGuideElement	24.0		1469468034457321.0
211945	3493542	K_becomesAttached$slide_play_detail_framework_release_I:DispatchBaseElement_T:DanmakuElement	21.0		1469468034743259.0
211952	3386041	K_onBecomesAttached_I:DanmakuElement$mAttachChangedListener$1_T:	22.0		1469468034844301.0
211957	3323438	K_onDanmakuAttached_I:DanmakuElement_T:DanmakuElement	23.0		1469468034900446.0
211960	403334	K_isSupportDanmakuCompat_I:NasaDetailUtil_T:NasaDetailUtil	24.0		1469468034919717.0
211961	382813	K_isSupportDanmaku_I:DanmakuSwitchUtil_T:DanmakuSwitchUtil	25.0		1469468034932894.0
211967	202031	K_isThirdPartAdGroup_I:CommercialFeedExt_T:CommercialFeedExt	26.0		1469468035057686.0
211999	2673385	K_subscribeEvent_I:DanmakuElement_T:DanmakuElement	24.0		1469468035488988.0
212012	2045313	K_subscribeViewCreated_I:DanmakuEventBus_T:DanmakuEventBus	25.0		1469468035687373.0
212016	1963542	K_accept_I:DanmakuElement$subscribeEvent$1_T:DanmakuElement$subscribeEvent$1	26.0		1469468035760238.0
212017	1931302	K_accept_I:DanmakuElement$subscribeEvent$1_T:DanmakuElement$subscribeEvent$1	27.0		1469468035784561.0
212018	670989	K_onBind_I:DanmakuKitCreateHelper_T:DanmakuKitCreateHelper	28.0		1469468035806905.0
212025	507708	K_<init>_I:NasaDanmakuParams_T:NasaDanmakuParams	29.0		1469468035866540.0
212076	1183438	K_initKit_I:DanmakuElement_T:DanmakuElement	28.0		1469468036515394.0
212091	967188	K_ifNull_I:NasaDetailDanmakuUtil_T:NasaDetailDanmakuUtil	29.0		1469468036726748.0
212093	940989	K_run_I:DanmakuElement$initKit$2_T:	30.0		1469468036747478.0
212105	515104	K_apply_I:DanmakuElement$initKit$2$1_T:	31.0		1469468036888467.0
212107	493177	K_apply_I:DanmakuElement$initKit$2$1_T:	32.0		1469468036905238.0
212109	368750	K_getDanmakuKitObservable_I:BarrageKitWrapper_T:BarrageKitWrapper	33.0		1469468036957321.0
212110	301771	K_createDanmakuKit_I:BarrageKitWrapper_T:BarrageKitWrapper	34.0		1469468037019248.0
212144	261510	K_notifyDanmakuInputVisible_I:DanmakuElement_T:DanmakuElement	31.0		1469468037421801.0
212148	210365	K_apply_I:DanmakuElement$notifyDanmakuInputVisible$1_T:	32.0		1469468037466592.0
212198	421042	K_becomesAttached_I:BaseGroup_T:TopRightGroup	20.0		1469468038310967.0
212206	270573	K_becomesAttached$slide_play_detail_framework_release_I:BaseElement_T:SaveTrafficReminderElement	21.0		1469468038456436.0
212232	390938	K_becomesAttached_I:BaseGroup_T:PlcStrongGroup	20.0		1469468038812269.0
212233	365989	K_becomesAttached$slide_play_detail_framework_release_I:DispatchBaseElement_T:SingleStrongStyleElement	21.0		1469468038832478.0
212241	236511	K_onBecomesAttached_I:BasePlcStrongStyleElement$1_T:	22.0		1469468038956592.0
212264	1400990	K_becomesAttached_I:BaseGroup_T:BottomPanelGroup	20.0		1469468039271488.0
212265	1250625	K_becomesAttached$slide_play_detail_framework_release_I:DispatchBaseElement_T:SurveyElement	21.0		1469468039289613.0
212273	1139115	K_onBecomesAttached_I:PageAttachChangedListener_T:	22.0		1469468039390290.0
212274	1113385	K_onBecomesAttached_I:SurveyElement$1_T:	23.0		1469468039407634.0
212276	232187	K_setSurveyType_I:SurveyElementHelper_T:SurveyElementHelper	24.0		1469468039433936.0
212315	667969	K_checkNthAction_I:SurveyElement_T:SurveyElement	24.0		1469468039848363.0
212317	617187	K_getInstance_I:SurveyElementHelper_T:SurveyElementHelper	25.0		1469468039871853.0
212366	19911667	K_onBecomesAttached_I:SlidePage_T:SlidePage	20.0		1469468040712842.0
212386	8016875	K_setValue_I:KLiveData_T:KLiveData	21.0		1469468041099978.0
212388	7994166	K_notifyValueSet_I:KLiveData_T:KLiveData	22.0		1469468041117478.0
212389	7975000	K_notifyValueSet_I:KLiveData_T:KLiveData	23.0		1469468041130134.0
212390	7949739	K_onChanged_I:BasePage$observeSwipe2ProfilePhotoListProgress$1_T:	24.0		1469468041149457.0
212392	7930104	K_onChanged_I:BasePage$observeSwipe2ProfilePhotoListProgress$1_T:	25.0		1469468041164613.0
212426	352761	K_pageSetBottomMargin_I:FullScreenLayerGroupEventBus_T:FullScreenLayerGroupEventBus	26.0		1469468041914665.0
212429	246198	K_accept_I:BottomProgressLoadingElement$onBind$3_T:	27.0		1469468042013728.0
212430	223594	K_accept_I:BottomProgressLoadingElement$onBind$3_T:	28.0		1469468042031488.0
212448	317135	K_notifySwipeScreenProgress_I:TopRightGroupEventBus_T:TopRightGroupEventBus	26.0		1469468042326332.0
212450	276719	K_changeViewWithDisclaimer_I:HdrWatermarkElement_T:HdrWatermarkElement	27.0		1469468042361592.0
212485	2299115	K_notifySwipeProgress_I:FullScreenLayerGroupEventBus_T:FullScreenLayerGroupEventBus	26.0		1469468043004248.0
212488	827552	K_accept_I:PlayPauseCenterElement$observeGroupEvent$1_T:	27.0		1469468043042113.0
212490	806511	K_accept_I:PlayPauseCenterElement$observeGroupEvent$1_T:	28.0		1469468043057842.0
212492	776615	K_swipeToShowElement_I:PlayPauseCenterElement_T:PlayPauseCenterElement	29.0		1469468043082113.0
212497	706198	K_tryHide_I:BaseElement_T:PlayPauseCenterElement	30.0		1469468043147009.0
212499	686458	K_setValue_I:KLiveData_T:KDispatchLiveData	31.0		1469468043161280.0
212500	668958	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	32.0		1469468043173728.0
212504	613594	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	33.0		1469468043224196.0
212505	594531	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	34.0		1469468043238103.0
212507	577552	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	35.0		1469468043249821.0
212508	559844	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	36.0		1469468043261957.0
212510	531094	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	37.0		1469468043282321.0
212511	504270	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	38.0		1469468043299301.0
212547	978385	K_accept_I:PlayLoadingElement$onBind$2_T:	27.0		1469468043891436.0
212548	941823	K_accept_I:PlayLoadingElement$onBind$2_T:	28.0		1469468043919353.0
212550	901771	K_updateLoadingVisibility_I:PlayLoadingElement_T:PlayLoadingElement	29.0		1469468043950863.0
212555	829584	K_tryShowOrHide_I:PlayLoadingElement_T:PlayLoadingElement	30.0		1469468044017217.0
212558	806354	K_hideAndLog_I:PlayLoadingElement_T:PlayLoadingElement	31.0		1469468044035290.0
212559	572448	K_tryHide_I:BaseElement_T:PlayLoadingElement	32.0		1469468044048884.0
212560	554166	K_setValue_I:KLiveData_T:KDispatchLiveData	33.0		1469468044061957.0
212561	535625	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	34.0		1469468044073780.0
212566	480625	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	35.0		1469468044123311.0
212567	458646	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	36.0		1469468044137321.0
212569	441042	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	37.0		1469468044150342.0
212571	423594	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	38.0		1469468044162842.0
212572	400260	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	39.0		1469468044180551.0
212573	381146	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	40.0		1469468044193519.0
212597	200989	K_logMsg_I:PlayLoadingElement_T:PlayLoadingElement	32.0		1469468044634926.0
212618	408073	K_accept_I:BottomProgressLoadingElement$onBind$1_T:	27.0		1469468044888467.0
212619	385364	K_accept_I:BottomProgressLoadingElement$onBind$1_T:	28.0		1469468044904405.0
212660	1843750	K_notifySwipeScreenProgress_I:PlayControllerGroupEventBus_T:PlayControllerGroupEventBus	26.0		1469468045368051.0
212661	1813333	K_accept_I:SidebarProgressElement$mPhotoDetailAttachChangedListener$1$onBecomesAttached$3_T:	27.0		1469468045392009.0
212663	1773281	K_accept_I:SidebarProgressElement$mPhotoDetailAttachChangedListener$1$onBecomesAttached$3_T:	28.0		1469468045426905.0
212678	1592136	K_tryHide_I:BaseElement_T:SidebarProgressElement	29.0		1469468045601696.0
212680	1570104	K_setValue_I:KLiveData_T:KDispatchLiveData	30.0		1469468045615186.0
212684	1541771	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	31.0		1469468045637582.0
212688	1501459	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	32.0		1469468045669717.0
212689	1484219	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	33.0		1469468045682217.0
212690	1466354	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	34.0		1469468045695082.0
212691	1444635	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	35.0		1469468045711280.0
212693	1420677	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	36.0		1469468045729561.0
212694	1397865	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	37.0		1469468045743311.0
212697	867187	Contending for pthread mutex	38.0		1469468045863207.0
212820	441302	K_setRightActionBarSwipeProgress_I:BasePage_T:SlidePage	26.0		1469468047925446.0
212833	308802	K_setViewSwipeProgress_I:BasePage_T:SlidePage	27.0		1469468048026905.0
212918	693490	K_setValue_I:KLiveData_T:KLiveData	21.0		1469468049207217.0
212920	670469	K_notifyValueSet_I:KLiveData_T:KLiveData	22.0		1469468049225238.0
212922	654063	K_notifyValueSet_I:KLiveData_T:KLiveData	23.0		1469468049236592.0
212926	630000	K_onChanged_I:BasePage$observePlcStrongShow$1_T:	24.0		1469468049255498.0
212928	601250	K_onChanged_I:BasePage$observePlcStrongShow$1_T:	25.0		1469468049275186.0
212987	215729	Lock contention on InternTable lock (owner tid: 0)	21.0		1469468050024144.0
213003	9466719	K_accept_I:SlidePage$onBecomesAttached$2_T:	21.0		1469468050423571.0
213004	9447605	K_accept_I:SlidePage$onBecomesAttached$2_T:	22.0		1469468050436748.0
213007	9368646	K_showLoading_I:FullScreenLayerGroupEventBus_T:FullScreenLayerGroupEventBus	23.0		1469468050509613.0
213008	2134636	K_accept_I:PlayLoadingElement$onBind$1_T:	24.0		1469468050535342.0
213011	2106406	K_accept_I:PlayLoadingElement$onBind$1_T:	25.0		1469468050558415.0
213014	2068177	K_startLoading_I:PlayLoadingElement_T:PlayLoadingElement	26.0		1469468050591228.0
213021	1635417	K_updateLoadingVisibility_I:PlayLoadingElement_T:PlayLoadingElement	27.0		1469468050766436.0
213025	1518229	K_tryShowOrHide_I:PlayLoadingElement_T:PlayLoadingElement	28.0		1469468050877061.0
213026	1495677	K_hideAndLog_I:PlayLoadingElement_T:PlayLoadingElement	29.0		1469468050893728.0
213027	1183125	K_tryHide_I:BaseElement_T:PlayLoadingElement	30.0		1469468050913051.0
213028	1161041	K_setValue_I:KLiveData_T:KDispatchLiveData	31.0		1469468050929457.0
213029	1140677	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	32.0		1469468050944092.0
213031	1096562	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	33.0		1469468050982061.0
213032	398959	Lock contention on thread suspend count lock (owner tid: 27898)	34.0		1469468050989144.0
213039	614479	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	34.0		1469468051458311.0
213040	584896	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	35.0		1469468051478988.0
213041	561198	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	36.0		1469468051494978.0
213042	537240	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	37.0		1469468051512842.0
213043	509791	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	38.0		1469468051528780.0
213059	266719	K_logMsg_I:PlayLoadingElement_T:PlayLoadingElement	30.0		1469468052115290.0
213066	211666	K_lambda$becomesAttachedOnPageSelected$1_I:NasaMilanoProgressPresenter$1_T:	27.0		1469468052441176.0
213070	7176562	K_accept_I:BottomProgressLoadingElement$onBind$2_T:	24.0		1469468052693936.0
213071	7155938	K_accept_I:BottomProgressLoadingElement$onBind$2_T:	25.0		1469468052708467.0
213085	6888646	K_doTryShowAndLog_I:BottomProgressLoadingElement_T:BottomProgressLoadingElement	26.0		1469468052970186.0
213087	6270000	K_tryShow_I:BaseElement_T:BottomProgressLoadingElement	27.0		1469468053003155.0
213101	5993334	K_setValue_I:KLiveData_T:KDispatchLiveData	28.0		1469468053274144.0
213102	5972865	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	29.0		1469468053288571.0
213105	5919427	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	30.0		1469468053336332.0
213107	5896979	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	31.0		1469468053353207.0
213108	5877969	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	32.0		1469468053366332.0
213109	5857344	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	33.0		1469468053380394.0
213110	5827239	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	34.0		1469468053403884.0
213112	5802917	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	35.0		1469468053419613.0
213130	541562	K_calculateElementRealShow_I:BaseGroup_T:FullScreenLayerGroup	36.0		1469468053898051.0
213196	2014792	K_create_I:BaseElementView_T:BottomProgressLoadingElementView	36.0		1469468055385498.0
213198	1937188	K_createView_I:BottomProgressLoadingElementView_T:BottomProgressLoadingElementView	37.0		1469468055411488.0
213204	1727864	inflate	38.0		1469468055608259.0
213205	1611615	com.yxcorp.gifshow.detail.view.SlidePlayVideoLoadingProgressBar	39.0		1469468055686123.0
213215	931042	res/drawable/progress_indeterminate_horizontal.xml	40.0		1469468056105342.0
213217	229010	res/drawable-xhdpi-v4/progressbar_indeterminate1.png	41.0		1469468056255707.0
213220	213125	res/drawable-xhdpi-v4/progressbar_indeterminate2.png	41.0		1469468056544873.0
213243	768125	K_bindData_I:BaseElementView_T:BottomProgressLoadingElementView	36.0		1469468057595082.0
213245	729948	K_onBindData_I:BottomProgressLoadingElementView_T:BottomProgressLoadingElementView	37.0		1469468057628155.0
213246	698698	K_onBindData_I:BottomProgressLoadingElementView_T:BottomProgressLoadingElementView	38.0		1469468057653780.0
213276	334895	K_layout_I:FrameGroupLayout_T:FrameGroupLayout	36.0		1469468058645551.0
213310	316563	K_logViewState_I:BottomProgressLoadingElement_T:BottomProgressLoadingElement	27.0		1469468059536019.0
213358	290990	K_handleExpTag_I:SlidePlayPhotoDetailVMFragment_T:NasaGrootDetailVMFragment	18.0		1469468060674040.0
213379	307500	K_performTrackStage_I:DetailSlidePlayFragment_T:NasaGrootDetailVMFragment	18.0		1469468061196853.0
213389	2911979	K_updateKsOrderList_I:GrootBaseFragment_T:NasaGrootDetailVMFragment	17.0		1469468061668155.0
213391	245781	K_logInfo_I:GrootLogger_T:GrootLogger	18.0		1469468061739665.0
213511	4613489	K_notifyBecomeAttached_I:StickyMilanoAttachChangedListenerManager_T:StickyMilanoAttachChangedListenerManager	14.0		1469468065659509.0
213522	264896	K_accept_I:SerializedRelay_T:SerializedRelay	15.0		1469468065990811.0
213524	240677	K_accept_I:PublishRelay_T:PublishRelay	16.0		1469468066007738.0
213549	460625	K_becomesAttached_I:MilanoLiveDisableProfilePresenter$1_T:	15.0		1469468066426592.0
213554	348750	K_setEnableProfileSideOnCurrentPhoto_I:HomeMilanoBaseContainerFragment_T:HomeFeaturedMilanoContainerFragment	16.0		1469468066532686.0
213556	313229	K_lambda$onBind$0_I:MilanoProfileSideSwipePresenter_T:MilanoProfileSideSwipePresenter	17.0		1469468066561280.0
213573	211615	K_becomesAttached_I:FeaturedPoorNetworkVideoPlayOptPresenter$2_T:	15.0		1469468066915290.0
213589	284062	K_becomesAttached_I:FeaturedSlideVideoNumPresenter$1_T:	15.0		1469468067145551.0
213607	1227240	K_becomesAttached_I:MilanoProfileSidePresenter$4_T:	15.0		1469468067450342.0
213625	252604	K_setDetailProfileInsertPhotoIfExclusive_I:SlidePlayViewModel_T:SlidePlayViewModel	16.0		1469468067890551.0
213635	254375	K_setDetailProfileNeedRemoveDuplicate_I:SlidePlayViewModel_T:SlidePlayViewModel	16.0		1469468068171071.0
213665	233959	K_becomesAttached_I:MilanoProfileSideOptVMPresenter$mAttachListener$1_T:	15.0		1469468068699248.0
213678	346510	K_becomesAttached_I:MilanoProfileFeedLoadMorePresenter$2_T:	15.0		1469468068953155.0
213679	296667	K_createOnScrollListener_I:MilanoProfileFeedLoadMoreOptPresenter_T:MilanoProfileFeedLoadMoreOptPresenter	16.0		1469468068990342.0
213681	209948	K_getDetailProfileFeedPageList_I:SlidePlayViewModel_T:SlidePlayViewModel	17.0		1469468069030967.0
213728	1399584	K_notifyWillAppear_I:StickyViewItemAppearChangedListenerController_T:StickyViewItemAppearChangedListenerController	14.0		1469468070335446.0
213729	1361094	K_willAppear_I:NasaSlideMonitorIndexPresenter$mViewItemAppearanceChangedListener$1_T:	15.0		1469468070362738.0
213732	1324270	K_whetherToCheckPosition_I:NasaSlideMonitorIndexPresenter_T:NasaSlideMonitorIndexPresenter	16.0		1469468070393051.0
213739	1146875	K_checkPosition_I:NasaSlideMonitorIndexPresenter_T:NasaSlideMonitorIndexPresenter	17.0		1469468070564665.0
213761	213698	K_getOriginPhotoList_I:SlidePlayViewModel_T:SlidePlayViewModel	18.0		1469468070867113.0
213785	297240	K_nextIsUnShow_I:NasaSlideMonitorIndexPresenter_T:NasaSlideMonitorIndexPresenter	18.0		1469468071269821.0
213829	5907136	K_willAppear_I:SlideUserProcessLoggerPresenter$1_T:	14.0		1469468071757998.0
213859	269896	K_getCurrentShowIndex_I:SlidePlayViewModel_T:SlidePlayViewModel	15.0		1469468072235446.0
213885	5005417	K_initSlidePhoto_I:SlideUserProcessLoggerPresenter_T:SlideUserProcessLoggerPresenter	15.0		1469468072633519.0
213909	650834	K_getMediaType_I:QPhotoMediaType_T:QPhotoMediaType	16.0		1469468072955394.0
213961	590573	K_getCurrentItemRealPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	16.0		1469468073724040.0
213962	205834	K_getGrootService_I:SlidePlayViewModel_T:SlidePlayViewModel	17.0		1469468073741019.0
213968	345000	K_getCurrentRealPositionInAdapter_I:PositionService_T:PositionService	17.0		1469468073963051.0
214003	582708	K_hasNextPhoto_I:SlidePlayViewModel_T:SlidePlayViewModel	16.0		1469468074522478.0
214011	474218	K_hasNextPhoto_I:KwaiPlayService_T:KwaiPlayService	17.0		1469468074622426.0
214013	427500	K_getPositionInAdapter_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter	18.0		1469468074643207.0
214016	230573	K_contains_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter	19.0		1469468074670603.0
214017	204740	K_contains_I:GrootUtils_T:GrootUtils	20.0		1469468074690290.0
214056	2428594	K_getPhotoCacheSize_I:PlayerCacheSizeUtil_T:PlayerCacheSizeUtil	16.0		1469468075160186.0
214059	690729	K_getManifestString_I:PlayerCacheSizeUtil_T:PlayerCacheSizeUtil	17.0		1469468075181176.0
214061	653541	K_toJsonString_I:ManifestInterface_T:KwaiManifest	18.0		1469468075211801.0
214063	575781	K_write_I:KwaiManifestTypeAdapter_T:KwaiManifestTypeAdapter	19.0		1469468075237061.0
214066	480625	K_write_I:KwaiManifestTypeAdapter_T:KwaiManifestTypeAdapter	20.0		1469468075254926.0
214069	392239	K_writeAdaption_I:KwaiManifestTypeAdapter_T:KwaiManifestTypeAdapter	21.0		1469468075284353.0
214241	248646	K_willAppear_I:NasaLivePhotoDuplicatedPresenter$mViewItemLifecycleListener$1_T:	14.0		1469468077716592.0
214258	1026771	K_onPageSelected_I:KwaiGrootFooterLoadingManager_T:KwaiGrootFooterLoadingManager	11.0		1469468078261280.0
214259	991771	K_updateFootLoading_I:KwaiGrootFooterLoadingManager_T:KwaiGrootFooterLoadingManager	12.0		1469468078290967.0
214260	300938	K_getCurrentDataSource_I:KwaiGrootDataSourceManager_T:KwaiGrootDataSourceManager	13.0		1469468078318467.0
214261	256146	K_logInfo_I:GrootLogger_T:GrootLogger	14.0		1469468078350238.0
214266	232813	K_getLastIndexInCurrentDataSource_I:KwaiGrootFooterLoadingManager_T:KwaiGrootFooterLoadingManager	13.0		1469468078689769.0
214274	242812	K_getLastIndexInCurrentDataSource_I:KwaiGrootFooterLoadingManager_T:KwaiGrootFooterLoadingManager	13.0		1469468078993259.0
214307	260313	K_setPollUpPrefetchThreshold_I:HomeFeaturedMilanoContainerFragment$1_T:HomeFeaturedMilanoContainerFragment$1	10.0		1469468080242269.0
214308	211302	K_setPollUpPrefetchThreshold_I:SlidePlayViewModel_T:SlidePlayViewModel	11.0		1469468080285134.0
214333	2956615	K_notifyPageSelected_I:StickyPageChangedListenerController_T:StickyPageChangedListenerController	10.0		1469468080771436.0
214345	1092396	K_onPageSelected_I:MilanoProfileSideSwipePresenter$2_T:	11.0		1469468081033936.0
214346	242917	K_getCurrentShowIndex_I:SlidePlayViewModel_T:SlidePlayViewModel	12.0		1469468081050134.0
214355	594844	K_getViewItemType_I:SlidePlayViewModel_T:SlidePlayViewModel	12.0		1469468081316644.0
214364	481614	K_getViewItemType_I:ViewItemService_T:ViewItemService	13.0		1469468081425134.0
214366	298073	K_getViewItemType_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter	14.0		1469468081442582.0
214371	227865	K_getViewItemType_I:NasaGrootItemCreator_T:NasaGrootItemCreator	15.0		1469468081507946.0
214399	987708	K_onPageSelected_I:PostEntranceNotifyCurrentFeedPresenter$1_T:	11.0		1469468082173780.0
214401	959479	K_onFeedSelected_I:PostEntranceNotifyCurrentFeedPresenter_T:PostEntranceNotifyCurrentFeedPresenter	12.0		1469468082197009.0
214405	890052	K_doCurrentMainDispatch_I:SlidePlayLifecycleHelper_T:SlidePlayLifecycleHelper	13.0		1469468082261228.0
214407	859479	K_lambda$onFeedSelected$1_I:PostEntranceNotifyCurrentFeedPresenter_T:PostEntranceNotifyCurrentFeedPresenter	14.0		1469468082282582.0
214408	263698	K_getCurrentShowIndex_I:SlidePlayViewModel_T:SlidePlayViewModel	15.0		1469468082296071.0
214425	207292	K_getRealPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	15.0		1469468082576696.0
214481	319635	K_onPageSelected_I:NasaFeaturedFPSMonitorPresenter$1_T:	11.0		1469468083402738.0
214484	217239	K_onSlideStart_I:NasaFeaturedFPSMonitorPresenter_T:NasaFeaturedFPSMonitorPresenter	12.0		1469468083499978.0
214494	1377968	K_onPageSelected_I:NasaBaseSpecialCameraButtonPresenter$2_T:	10.0		1469468083749978.0
214503	1195625	K_onFeedSelected_I:NasaBaseSpecialCameraButtonPresenter_T:NasaSpecialCameraButtonPresenter	11.0		1469468083926644.0
214506	1143959	K_doCurrentMainDispatch_I:SlidePlayLifecycleHelper_T:SlidePlayLifecycleHelper	12.0		1469468083972998.0
214507	1119479	K_lambda$onFeedSelected$1_I:NasaBaseSpecialCameraButtonPresenter_T:NasaSpecialCameraButtonPresenter	13.0		1469468083991280.0
214508	217604	K_getCurrentShowIndex_I:SlidePlayViewModel_T:SlidePlayViewModel	14.0		1469468084005394.0
214523	231250	K_getRealPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	14.0		1469468084235967.0
214548	472396	K_handlePostShowInfo_I:NasaSpecialCameraButtonPresenter_T:NasaSpecialCameraButtonPresenter	14.0		1469468084633415.0
214560	287760	K_hideSpecialButton_I:NasaSpecialCameraButtonPresenter_T:NasaSpecialCameraButtonPresenter	15.0		1469468084809457.0
214574	447344	K_onPageSelected_I:NasaKgiFeaturePresenter$mPhotoChangeListener$1_T:	10.0		1469468085151957.0
214588	239062	K_obtainFeature_I:NasaKgiFeaturePresenter_T:NasaKgiFeaturePresenter	11.0		1469468085346332.0
214604	207395	K_getCurrentPhoto_I:SlidePlayViewModel_T:SlidePlayViewModel	10.0		1469468085642426.0
214616	354166	K_getCurrentItemRealPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	10.0		1469468085897582.0
214622	261770	K_getCurrentRealPositionInAdapter_I:PositionService_T:PositionService	11.0		1469468085984926.0
214655	214011	K_getRealPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	10.0		1469468086602373.0
214740	18743959	traversal	4.0		1469468088798571.0
214741	7726458	measure	5.0		1469468088845551.0
214743	7660990	K_onMeasure_I:KwaiSlidingPaneLayout_T:KwaiSlidingPaneLayout	6.0		1469468088891540.0
214744	7636458	K_onMeasure_I:SlidingPaneLayout_T:KwaiSlidingPaneLayout	7.0		1469468088907009.0
214745	7203385	K_onMeasure_I:ScrollStrategyViewPager_T:ScrollStrategyViewPager	8.0		1469468088926801.0
214747	7185677	K_onMeasure_I:ViewPager_T:ScrollStrategyViewPager	9.0		1469468088938571.0
214749	7094739	K_onMeasure_I:ConstraintLayout_T:ConstraintLayout	10.0		1469468089020707.0
214754	4858906	K_onMeasure_I:KwaiGrootViewPager_T:KwaiGrootViewPager	11.0		1469468089197738.0
214757	4838177	K_onMeasure_I:VerticalViewPager_T:KwaiGrootViewPager	12.0		1469468089212530.0
214777	790990	K_onMeasure_I:MusicLabelLayout_T:MusicLabelLayout	13.0		1469468089459873.0
214778	771302	K_onMeasure_I:PriorityLinearLayout_T:MusicLabelLayout	14.0		1469468089472165.0
214855	1677448	K_onMeasure_I:UserNameLinearLayout_T:UserNameLinearLayout	13.0		1469468090323988.0
214859	1586198	K_onMeasure_I:PriorityLinearLayout_T:UserNameLinearLayout	14.0		1469468090361644.0
214861	722240	K_generateMeasureList_I:PriorityLinearLayout_T:UserNameLinearLayout	15.0		1469468090375863.0
214988	388750	K_onMeasure_I:ConstraintLayout_T:ConstraintLayout	13.0		1469468092019978.0
214989	258178	K_onMeasure_I:CaptionTextView_T:CaptionTextView	14.0		1469468092068623.0
214990	229947	K_onMeasure_I:AppCompatTextView_T:CaptionTextView	15.0		1469468092086176.0
215000	420156	K_onMeasure_I:MusicLabelLayout_T:MusicLabelLayout	13.0		1469468092427738.0
215002	401458	K_onMeasure_I:PriorityLinearLayout_T:MusicLabelLayout	14.0		1469468092441384.0
215040	409948	K_onMeasure_I:MusicLabelLayout_T:MusicLabelLayout	13.0		1469468093039040.0
215042	380677	K_onMeasure_I:PriorityLinearLayout_T:MusicLabelLayout	14.0		1469468093063259.0
215078	537917	K_onMeasure_I:MusicLabelLayout_T:MusicLabelLayout	13.0		1469468093484769.0
215079	519844	K_onMeasure_I:PriorityLinearLayout_T:MusicLabelLayout	14.0		1469468093498363.0
215115	1865104	K_onMeasure_I:KwaiGrootViewPager_T:KwaiGrootViewPager	11.0		1469468094206280.0
215116	1846667	K_onMeasure_I:VerticalViewPager_T:KwaiGrootViewPager	12.0		1469468094219665.0
215119	413646	K_onMeasure_I:MusicLabelLayout_T:MusicLabelLayout	13.0		1469468094281123.0
215120	394791	K_onMeasure_I:PriorityLinearLayout_T:MusicLabelLayout	14.0		1469468094294405.0
215149	398281	K_onMeasure_I:MusicLabelLayout_T:MusicLabelLayout	13.0		1469468094729301.0
215150	379635	K_onMeasure_I:PriorityLinearLayout_T:MusicLabelLayout	14.0		1469468094741332.0
215188	420573	K_onMeasure_I:MusicLabelLayout_T:MusicLabelLayout	13.0		1469468095167321.0
215190	398230	K_onMeasure_I:PriorityLinearLayout_T:MusicLabelLayout	14.0		1469468095184873.0
215226	417032	K_onMeasure_I:MusicLabelLayout_T:MusicLabelLayout	13.0		1469468095621748.0
215227	398333	K_onMeasure_I:PriorityLinearLayout_T:MusicLabelLayout	14.0		1469468095635863.0
215265	288334	K_onMeasure_I:RefreshLayout_T:CustomRefreshLayout	8.0		1469468096165446.0
215268	201406	K_onMeasure_I:CustomRecyclerView_T:CustomRecyclerView	9.0		1469468096209926.0
215286	5581459	layout	5.0		1469468096693519.0
215290	5330782	K_onLayout_I:KwaiSlidingPaneLayout_T:KwaiSlidingPaneLayout	6.0		1469468096726019.0
215291	5307292	K_onLayout_I:SlidingPaneLayout_T:KwaiSlidingPaneLayout	7.0		1469468096740290.0
215294	3885886	K_onLayout_I:ViewPager_T:ScrollStrategyViewPager	8.0		1469468096760498.0
215296	3848177	K_onLayout_I:ConstraintLayout_T:ConstraintLayout	9.0		1469468096784040.0
215301	3762448	K_onLayout_I:KwaiGrootViewPager_T:KwaiGrootViewPager	10.0		1469468096831540.0
215302	3719843	K_onLayout_I:VerticalViewPager_T:KwaiGrootViewPager	11.0		1469468096846280.0
215339	408542	K_onLayout_I:SlidePlayVideoLoadingProgressBar_T:SlidePlayVideoLoadingProgressBar	12.0		1469468097492009.0
215342	350260	K_showAnim_I:SlidePlayVideoLoadingProgressBar_T:SlidePlayVideoLoadingProgressBar	13.0		1469468097545186.0
215357	1427916	K_onMeasure_I:UserNameLinearLayout_T:UserNameLinearLayout	12.0		1469468097954405.0
215359	1345521	K_onMeasure_I:PriorityLinearLayout_T:UserNameLinearLayout	13.0		1469468097971592.0
215361	684635	K_generateMeasureList_I:PriorityLinearLayout_T:UserNameLinearLayout	14.0		1469468097992061.0
215514	218125	K_onLayout_I:AvatarWithFollowLiving_T:AvatarWithFollowLiving	12.0		1469468100224144.0
215540	1355573	K_onLayout_I:RefreshLayout_T:CustomRefreshLayout	8.0		1469468100680707.0
215547	1166406	K_onLayout_I:RecyclerView_T:CustomRecyclerView	9.0		1469468100833311.0
215548	1134792	RV OnLayout	10.0		1469468100851488.0
215598	432344	K_onGlobalLayout_I:InformationGroup$mOnGlobalLayoutListener$1_T:	5.0		1469468102342582.0
215600	387291	K_notifyInformationPosition_I:InformationGroupEventBus_T:InformationGroupEventBus	6.0		1469468102383103.0
215602	362396	K_accept_I:BasePage$bindInner$$inlined$dispatchRun$lambda$16_T:	7.0		1469468102399196.0
215603	345989	K_accept_I:BasePage$bindInner$$inlined$dispatchRun$lambda$16_T:	8.0		1469468102410759.0
215607	285625	K_notifyInformationPosition_I:CenterEntranceGroupEventBus_T:CenterEntranceGroupEventBus	9.0		1469468102466280.0
215608	264167	K_accept_I:LandscapeCenterEntranceElement$onBind$4_T:	10.0		1469468102482738.0
215609	239323	K_accept_I:LandscapeCenterEntranceElement$onBind$4_T:	11.0		1469468102502998.0
215610	219271	K_changeVerticalLocation_I:LandscapeCenterEntranceElement_T:LandscapeCenterEntranceElement	12.0		1469468102518311.0
215631	404427	K_onGlobalLayout_I:InformationGroup$mOnGlobalLayoutListener$1_T:	5.0		1469468102908207.0
215634	359844	K_notifyInformationPosition_I:InformationGroupEventBus_T:InformationGroupEventBus	6.0		1469468102944717.0
215636	335260	K_accept_I:BasePage$bindInner$$inlined$dispatchRun$lambda$16_T:	7.0		1469468102962530.0
215637	318073	K_accept_I:BasePage$bindInner$$inlined$dispatchRun$lambda$16_T:	8.0		1469468102974509.0
215640	256303	K_notifyInformationPosition_I:CenterEntranceGroupEventBus_T:CenterEntranceGroupEventBus	9.0		1469468103028623.0
215641	237864	K_accept_I:LandscapeCenterEntranceElement$onBind$4_T:	10.0		1469468103041957.0
215642	220208	K_accept_I:LandscapeCenterEntranceElement$onBind$4_T:	11.0		1469468103054509.0
215643	200886	K_changeVerticalLocation_I:LandscapeCenterEntranceElement_T:LandscapeCenterEntranceElement	12.0		1469468103068623.0
215662	4086563	draw	5.0		1469468103442738.0
215663	3502761	Record View#draw()	6.0		1469468103466592.0
215841	22777187	com.kwai.video.aemonplayer.AemonMediaPlayer$EventHandler: #200	1.0		1469468108098936.0
215852	22565989	K_lambda$attachListeners$8_I:_2_AbstractPlayerListenerDelegate_T:WaynePlayer	2.0		1469468108298051.0
215931	4854166	K_lambda$new$0_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	3.0		1469468109746853.0
215935	4731927	K_lambda$new$0_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	4.0		1469468109861384.0
215937	4686667	K_lambda$new$0_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	5.0		1469468109899040.0
215942	4627448	K_lambda$new$0_I:PlayerLoadingHelper_T:PlayerLoadingHelper	6.0		1469468109950707.0
215962	4369323	K_shouldHideLoading_I:SlideLoadingStatePresenter$createPlayerLoadingHelperIfNeed$1_T:SlideLoadingStatePresenter$createPlayerLoa	7.0		1469468110202894.0
215964	4338229	K_notifyStartLoading_I:SlideLoadingStatePresenter_T:SlideLoadingStatePresenter	8.0		1469468110221957.0
215966	4306250	K_accept_I:SlidePage$onBecomesAttached$2_T:	9.0		1469468110247946.0
215968	4285625	K_accept_I:SlidePage$onBecomesAttached$2_T:	10.0		1469468110263415.0
215972	4223281	K_showLoading_I:FullScreenLayerGroupEventBus_T:FullScreenLayerGroupEventBus	11.0		1469468110320707.0
215974	1589114	K_accept_I:PlayLoadingElement$onBind$1_T:	12.0		1469468110350655.0
215975	1568073	K_accept_I:PlayLoadingElement$onBind$1_T:	13.0		1469468110362686.0
215977	1533385	K_stopLoading_I:PlayLoadingElement_T:PlayLoadingElement	14.0		1469468110381801.0
215984	883907	K_updateLoadingVisibility_I:PlayLoadingElement_T:PlayLoadingElement	15.0		1469468110524248.0
215990	807187	K_tryShowOrHide_I:PlayLoadingElement_T:PlayLoadingElement	16.0		1469468110595811.0
215994	788385	K_hideAndLog_I:PlayLoadingElement_T:PlayLoadingElement	17.0		1469468110610134.0
215995	587239	K_tryHide_I:BaseElement_T:PlayLoadingElement	18.0		1469468110623676.0
215997	561823	K_setValue_I:KLiveData_T:KDispatchLiveData	19.0		1469468110644248.0
215998	542916	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	20.0		1469468110658155.0
216003	499740	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	21.0		1469468110692790.0
216005	477969	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	22.0		1469468110709561.0
216006	447656	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	23.0		1469468110735030.0
216008	428541	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	24.0		1469468110749457.0
216010	404792	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	25.0		1469468110768467.0
216012	377083	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	26.0		1469468110788884.0
216049	479427	K_lambda$becomesAttachedOnPageSelected$1_I:NasaMilanoProgressPresenter$1_T:	15.0		1469468111427634.0
216050	449063	K_showProgressBar_I:NasaMilanoProgressPresenter_T:NasaMilanoProgressPresenter	16.0		1469468111452269.0
216051	423906	K_showProgressBar_I:NasaMilanoProgressPresenter_T:NasaMilanoProgressPresenter	17.0		1469468111472426.0
216054	315469	K_accept_I:NasaBottomBarDividerPresenter$onBind$1_T:	18.0		1469468111572061.0
216055	298906	K_accept_I:NasaBottomBarDividerPresenter$onBind$1_T:	19.0		1469468111583155.0
216076	2573854	K_accept_I:BottomProgressLoadingElement$onBind$2_T:	12.0		1469468111961644.0
216077	2541406	K_accept_I:BottomProgressLoadingElement$onBind$2_T:	13.0		1469468111989561.0
216079	411458	K_setShowLoadingAnim_I:BottomProgressLoadingViewModel_T:BottomProgressLoadingViewModel	14.0		1469468112025915.0
216080	392812	K_setValue_I:KLiveData_T:KDispatchLiveData	15.0		1469468112039561.0
216081	368906	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	16.0		1469468112056436.0
216085	323802	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	17.0		1469468112093103.0
216086	306042	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	18.0		1469468112105238.0
216087	272448	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	19.0		1469468112131644.0
216088	230677	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	20.0		1469468112164561.0
216104	2065521	K_doTryHideAndLog_I:BottomProgressLoadingElement_T:BottomProgressLoadingElement	14.0		1469468112460134.0
216106	535886	K_logMsg_I:BottomProgressLoadingElement_T:BottomProgressLoadingElement	15.0		1469468112482790.0
216119	1474114	K_tryHide_I:BaseElement_T:BottomProgressLoadingElement	15.0		1469468113038051.0
216120	1451979	K_setValue_I:KLiveData_T:KDispatchLiveData	16.0		1469468113055238.0
216123	1433802	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	17.0		1469468113068207.0
216125	1350521	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	18.0		1469468113143415.0
216127	1331979	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	19.0		1469468113156280.0
216128	1315260	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	20.0		1469468113168259.0
216129	1297604	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	21.0		1469468113181019.0
216131	1269271	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	22.0		1469468113200915.0
216132	1245625	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	23.0		1469468113215238.0
216155	518386	K_calculateElementRealShow_I:BaseGroup_T:FullScreenLayerGroup	24.0		1469468113569665.0
216268	509219	K_onInfo_I:PhotoDetailLoggerPresenter$1_T:	3.0		1469468115450290.0
216271	473594	K_uploadFirstFrameDurationFromActionUp_I:PhotoDetailLoggerPresenter_T:PhotoDetailLoggerPresenter	4.0		1469468115479821.0
216362	1359323	K_lambda$new$1_I:KwaiPlayerPlayOrNotHelper_T:KwaiPlayerPlayOrNotHelper	3.0		1469468116283155.0
216365	1340104	K_moveToChange_I:KwaiPlayerPlayOrNotHelper_T:KwaiPlayerPlayOrNotHelper	4.0		1469468116297113.0
216379	1220157	K_notifyPlayingChanged_I:KwaiPlayerPlayOrNotHelper_T:KwaiPlayerPlayOrNotHelper	5.0		1469468116406696.0
216382	1191302	K_lambda$onBind$0_I:DetailCountDownPresenter_T:DetailCountDownPresenter	6.0		1469468116424457.0
216528	3952292	K_lambda$new$1_I:KwaiPlayerPlayOrNotHelper_T:KwaiPlayerPlayOrNotHelper	3.0		1469468117709717.0
216530	3924323	K_moveToChange_I:KwaiPlayerPlayOrNotHelper_T:KwaiPlayerPlayOrNotHelper	4.0		1469468117731592.0
216532	2927448	K_changeLog_I:KwaiPlayerPlayOrNotHelper_T:KwaiPlayerPlayOrNotHelper	5.0		1469468117746019.0
216662	950781	K_notifyPlayingChanged_I:KwaiPlayerPlayOrNotHelper_T:KwaiPlayerPlayOrNotHelper	5.0		1469468120698988.0
216663	907291	K_lambda$onBind$0_I:GrowthKwaiWatchVideoPresenter_T:GrowthKwaiWatchVideoPresenter	6.0		1469468120733103.0
216670	484011	K_setVideoPlaying_I:GrowthKwaiWatchVideoManager_T:GrowthKwaiWatchVideoManager	7.0		1469468120851123.0
216671	228385	K_logCustomEventByNewUser_I:GrowthLoggerHelper_T:GrowthLoggerHelper	8.0		1469468120882582.0
216762	5414583	K_lambda$new$0_I:PlayFailedRetryElement_T:PlayFailedRetryElement	3.0		1469468122233780.0
216764	222604	K_log_I:PlayFailedRetryElement_T:PlayFailedRetryElement	4.0		1469468122253728.0
216782	4754271	K_reset_I:PlayFailedRetryElement_T:PlayFailedRetryElement	4.0		1469468122498519.0
216785	1716771	K_hideRetry_I:PlayFailedRetryElement_T:PlayFailedRetryElement	5.0		1469468122522321.0
216794	695781	K_tryHide_I:BaseElement_T:PlayFailedRetryElement	6.0		1469468122618780.0
216796	668333	K_setValue_I:KLiveData_T:KDispatchLiveData	7.0		1469468122639561.0
216798	636771	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	8.0		1469468122660394.0
216804	577760	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	9.0		1469468122709301.0
216807	555885	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	10.0		1469468122725238.0
216811	534062	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	11.0		1469468122741176.0
216813	513489	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	12.0		1469468122755603.0
216815	490209	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	13.0		1469468122772217.0
216819	463802	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	14.0		1469468122791384.0
216861	764896	K_accept_I:SerializedRelay_T:SerializedRelay	6.0		1469468123465446.0
216862	712187	K_accept_I:PublishRelay_T:PublishRelay	7.0		1469468123485551.0
216874	2975625	K_showLoading_I:PlayFailedRetryElement_T:PlayFailedRetryElement	5.0		1469468124267373.0
216876	2940989	K_accept_I:SlidePage$onBecomesAttached$2_T:	6.0		1469468124295082.0
216877	2918593	K_accept_I:SlidePage$onBecomesAttached$2_T:	7.0		1469468124311332.0
216882	2833124	K_showLoading_I:FullScreenLayerGroupEventBus_T:FullScreenLayerGroupEventBus	8.0		1469468124391176.0
216885	1753854	K_accept_I:PlayLoadingElement$onBind$1_T:	9.0		1469468124411071.0
216887	1724166	K_accept_I:PlayLoadingElement$onBind$1_T:	10.0		1469468124430603.0
216891	1687187	K_stopLoading_I:PlayLoadingElement_T:PlayLoadingElement	11.0		1469468124460030.0
216893	1141823	K_updateLoadingVisibility_I:PlayLoadingElement_T:PlayLoadingElement	12.0		1469468124478936.0
216900	1063958	K_tryShowOrHide_I:PlayLoadingElement_T:PlayLoadingElement	13.0		1469468124548884.0
216901	1042812	K_hideAndLog_I:PlayLoadingElement_T:PlayLoadingElement	14.0		1469468124564457.0
216903	789687	K_tryHide_I:BaseElement_T:PlayLoadingElement	15.0		1469468124578936.0
216904	768386	K_setValue_I:KLiveData_T:KDispatchLiveData	16.0		1469468124594248.0
216905	747709	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	17.0		1469468124608467.0
216907	691302	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	18.0		1469468124658155.0
216908	668802	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	19.0		1469468124674613.0
216909	646823	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	20.0		1469468124690186.0
216910	619375	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	21.0		1469468124710967.0
216911	596510	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	22.0		1469468124727686.0
216912	571719	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	23.0		1469468124744873.0
216942	213802	K_logMsg_I:PlayLoadingElement_T:PlayLoadingElement	15.0		1469468125385342.0
216957	490260	K_lambda$becomesAttachedOnPageSelected$1_I:NasaMilanoProgressPresenter$1_T:	12.0		1469468125649926.0
216960	458958	K_showProgressBar_I:NasaMilanoProgressPresenter_T:NasaMilanoProgressPresenter	13.0		1469468125672582.0
216964	424791	K_showProgressBar_I:NasaMilanoProgressPresenter_T:NasaMilanoProgressPresenter	14.0		1469468125699405.0
216970	355104	K_accept_I:NasaBottomBarDividerPresenter$onBind$1_T:	15.0		1469468125760759.0
216972	286042	K_accept_I:NasaBottomBarDividerPresenter$onBind$1_T:	16.0		1469468125823363.0
216987	1036198	K_accept_I:BottomProgressLoadingElement$onBind$2_T:	9.0		1469468126181644.0
216988	1015260	K_accept_I:BottomProgressLoadingElement$onBind$2_T:	10.0		1469468126196280.0
216991	259167	K_setShowLoadingAnim_I:BottomProgressLoadingViewModel_T:BottomProgressLoadingViewModel	11.0		1469468126239665.0
216992	236146	K_setValue_I:KLiveData_T:KDispatchLiveData	12.0		1469468126256488.0
216994	214896	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	13.0		1469468126272165.0
217012	679479	K_doTryHideAndLog_I:BottomProgressLoadingElement_T:BottomProgressLoadingElement	11.0		1469468126525811.0
217013	654636	K_tryHide_I:BaseElement_T:BottomProgressLoadingElement	12.0		1469468126543623.0
217014	632135	K_setValue_I:KLiveData_T:KDispatchLiveData	13.0		1469468126559978.0
217016	611250	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	14.0		1469468126574978.0
217019	564687	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	15.0		1469468126615030.0
217020	542968	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	16.0		1469468126630655.0
217021	521875	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	17.0		1469468126645550.0
217022	501510	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	18.0		1469468126659665.0
217023	479479	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	19.0		1469468126675446.0
217025	451302	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	20.0		1469468126695603.0
217060	2999896	K_lambda$startLog$3_I:PhotoDetailPlayLoggerHelper_T:PhotoDetailPlayLoggerHelper	3.0		1469468127720134.0
217067	2815313	K_logApmFirstFrameSuccess_I:PhotoDetailPlayLoggerHelper_T:PhotoDetailPlayLoggerHelper	4.0		1469468127898571.0
217087	323020	K_isCurrentFragmentOnScreen_I:DetailSlidePlayFragment_T:NasaGrootDetailVMFragment	5.0		1469468128164353.0
217089	295833	K_getCurrentShowIndex_I:SlidePlayViewModel_T:SlidePlayViewModel	6.0		1469468128184457.0
217131	1112604	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	5.0		1469468128853103.0
217132	356822	K_logMsg_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	6.0		1469468128874978.0
217134	256927	K_getLogPrefix_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	7.0		1469468128914092.0
217149	705312	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	6.0		1469468129254457.0
217150	294895	K_logMsg_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	7.0		1469468129271853.0
217180	363854	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	7.0		1469468129584717.0
217181	286250	K_logMsg_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	8.0		1469468129607425.0
217236	245729	K_onFirstFrameReady_I:TabApmTracker_T:TabApmTracker	5.0		1469468130461644.0
217339	4467500	android.os.Handler: cb	1.0		1469468131867894.0
217366	280417	binder transaction	2.0		1469468132270446.0
217388	2820208	K_lambda$registerReceiver$1_I:App_T:App	2.0		1469468132609978.0
217398	2688281	binder transaction	3.0		1469468132710603.0
217626	12471094	android.view.ViewRootImpl$ViewRootHandler: com.kwai.library.groot.slide.logger.GrootSlideCommonLogger$$ExternalSyntheticLambda0	1.0		1469468138057009.0
217629	12400573	K_lambda$postLogPageShow$0_I:GrootSlideCommonLogger_T:GrootSlideCommonLogger	2.0		1469468138120759.0
217647	11801146	K_doLogPageShow_I:GrootSlideCommonLogger_T:GrootSlideCommonLogger	3.0		1469468138290082.0
217675	4312604	K_getPageParams_I:NasaGrootDetailVMFragment_T:NasaGrootDetailVMFragment	4.0		1469468138602321.0
217680	4094583	K_getPageParams_I:SlidePlayPhotoDetailVMFragment_T:NasaGrootDetailVMFragment	5.0		1469468138632634.0
217690	4007136	K_getGrootPageParams_I:PhotoPageParamsUtils_T:PhotoPageParamsUtils	6.0		1469468138714300.0
217693	2804219	K_getCommonBuilder_I:PhotoPageParamsUtils_T:PhotoPageParamsUtils	7.0		1469468138730863.0
217878	403125	K_isFullScreen_I:PhotoPageParamsUtils_T:PhotoPageParamsUtils	8.0		1469468140471436.0
217997	882552	K_build_I:DetailPageParam$Builder_T:Builder	7.0		1469468141653728.0
217998	821979	K_<init>_I:DetailPageParam_T:DetailPageParam	8.0		1469468141670394.0
218154	757865	K_getContentPackage_I:SlidePlayPhotoDetailVMFragment_T:NasaGrootDetailVMFragment	4.0		1469468142960863.0
218251	671771	K_getContentPackageOnLeave_I:SlidePlayPhotoDetailVMFragment_T:NasaGrootDetailVMFragment	4.0		1469468143748415.0
218438	356250	K_onPageChanged_I:KPopPageChangeListener$init$1_T:KPopPageChangeListener$init$1	4.0		1469468146122946.0
218482	1215833	K_onPageChanged_I:KAKAppContextImpl$init$8_T:	4.0		1469468146980915.0
218494	993907	K_lambda$triggerActivities$0_I:Trigger_T:Trigger	5.0		1469468147192946.0
218517	215729	K_getH5KAKWhiteList_I:KAKAppContextImpl_T:KAKAppContextImpl	6.0		1469468147685863.0
218543	1440469	Lock contention on InternTable lock (owner tid: 0)	4.0		1469468148401436.0
218593	264792	K_accept_I:SerializedRelay_T:SerializedRelay	3.0		1469468150250342.0
218594	239844	K_accept_I:PublishRelay_T:PublishRelay	4.0		1469468150269769.0
218638	4582500	android.os.Handler: com.mini.channel.ServerChannel$$ExternalSyntheticLambda0	1.0		1469468151444405.0
218639	4523958	K_lambda$onDisconnected$0_I:ServerChannel_T:ServerChannel	2.0		1469468151496384.0
218679	284740	binder transaction	3.0		1469468152334092.0
218835	307448	android.app.ActivityThread$H: #-122	1.0		1469468156103884.0
218839	258854	K_handleMessage_I:AsyncScheduleManager$ActivityThreadMhCallbackDelegate_T:ActivityThreadMhCallbackDelegate	2.0		1469468156145759.0
218861	379479	android.os.Handler: io.reactivex.android.schedulers.HandlerScheduler$ScheduledRunnable	1.0		1469468156666071.0
218862	341250	K_run_I:HandlerScheduler$ScheduledRunnable_T:ScheduledRunnable	2.0		1469468156697582.0
218863	317031	K_run_I:ObservableObserveOn$ObserveOnObserver_T:ObserveOnObserver	3.0		1469468156715811.0
218864	262396	K_handlePhotoDetailLeave_I:CommercialLogInitModule_T:CommercialLogInitModule	4.0		1469468156762946.0
218865	235365	K_isAd_I:CommercialLogInitModule_T:CommercialLogInitModule	5.0		1469468156783988.0
218887	1283437	com.yxcorp.utility.ScheduleHandler: #0	1.0		1469468157240759.0
218890	1231928	K_lambda$ensureInit$2_I:PlayerProgressUpdater_T:PlayerProgressUpdater	2.0		1469468157274925.0
218891	1209688	K_sendProgressEvent_I:PlayerProgressUpdater_T:PlayerProgressUpdater	3.0		1469468157291800.0
218914	886979	K_onPlayerProgressUpdateEvent_I:SurveyElement_T:SurveyElement	4.0		1469468157608363.0
218917	351302	K_setSurveyType_I:SurveyElementHelper_T:SurveyElementHelper	5.0		1469468157628259.0
218948	442916	K_getInstance_I:SurveyElementHelper_T:SurveyElementHelper	5.0		1469468157997478.0
218983	1876146	com.kwai.video.aemonplayer.AemonMediaPlayer$EventHandler: #200	1.0		1469468158577582.0
218995	1642188	K_lambda$attachListeners$8_I:_2_AbstractPlayerListenerDelegate_T:WaynePlayer	2.0		1469468158801540.0
219102	1453333	com.kwai.video.aemonplayer.AemonMediaPlayer$EventHandler: #200	1.0		1469468160605707.0
219114	1261406	K_lambda$attachListeners$8_I:_2_AbstractPlayerListenerDelegate_T:WaynePlayer	2.0		1469468160786384.0
219206	2700000	com.kwai.video.aemonplayer.AemonMediaPlayer$EventHandler: #200	1.0		1469468162185290.0
219213	2456771	K_lambda$attachListeners$8_I:_2_AbstractPlayerListenerDelegate_T:WaynePlayer	2.0		1469468162419613.0
219293	635989	K_lambda$startLog$3_I:PhotoDetailPlayLoggerHelper_T:PhotoDetailPlayLoggerHelper	3.0		1469468164145707.0
219296	564584	K_recordAudioFirstRenderTime_I:PhotoDetailPlayLoggerHelper_T:PhotoDetailPlayLoggerHelper	4.0		1469468164212321.0
219300	273593	K_isCurrentFragmentOnScreen_I:DetailSlidePlayFragment_T:NasaGrootDetailVMFragment	5.0		1469468164301280.0
219301	252708	K_getCurrentShowIndex_I:SlidePlayViewModel_T:SlidePlayViewModel	6.0		1469468164314978.0
219336	2237344	android.view.ViewRootImpl$ViewRootHandler: com.kwai.component.photo.detail.slide.log.DetailPhotoLogPresenter$2$$ExternalSyntheticLambda0	1.0		1469468164949248.0
219338	2203437	K_lambda$logShowEvent$1_I:DetailPhotoLogPresenter$2_T:	2.0		1469468164977686.0
219340	2178698	K_doLogShowEvent_I:DetailPhotoLogPresenter_T:DetailPhotoLogPresenter	3.0		1469468164997373.0
219373	669687	K_logEvent_I:LogManager_T:LogManager--action: 0 content_package <   photo_package <     atlastype: 0     author_id: 1988932079 	4.0		1469468166500082.0
219398	208073	android.os.Handler: io.reactivex.android.schedulers.HandlerScheduler$ScheduledRunnable	1.0		1469468167256436.0
219412	699844	com.yxcorp.utility.ScheduleHandler: #0	1.0		1469468167511800.0
219414	650677	K_run_I:PhotoPlayCommonSceneDetectorPresenter$initProgressUpdateHandler$1_T:PhotoPlayCommonSceneDetectorPresenter$initProgressU	2.0		1469468167547686.0
219415	631406	K_sendProgressEvent_I:PhotoPlayCommonSceneDetectorPresenter_T:PhotoPlayCommonSceneDetectorPresenter	3.0		1469468167562217.0
219461	218125	android.os.Handler: io.reactivex.android.schedulers.HandlerScheduler$ScheduledRunnable	1.0		1469468168259405.0
219484	465729	com.yxcorp.utility.ScheduleHandler: #0	1.0		1469468168728311.0
219485	427500	K_lambda$ensureInit$2_I:PlayerProgressUpdater_T:PlayerProgressUpdater	2.0		1469468168753155.0
219486	402083	K_sendProgressEvent_I:PlayerProgressUpdater_T:PlayerProgressUpdater	3.0		1469468168766957.0
219514	458020	android.os.Handler: com.yxcorp.gifshow.detail.slidev2.presenter.SlidePlayerAttachLoggerPresenter$$ExternalSyntheticLambda0	1.0		1469468169237478.0
219516	424531	K_logPlayerAttached_I:SlidePlayerAttachLoggerPresenter_T:SlidePlayerAttachLoggerPresenter	2.0		1469468169265186.0
219551	1211407	com.yxcorp.utility.ScheduleHandler: #0	1.0		1469468169747321.0
219553	1173959	K_lambda$initProgressUpdateHandler$2_I:RedesignPlayProgressEventPresenter_T:RedesignPlayProgressEventPresenter	2.0		1469468169771696.0
219554	1112552	K_sendProgressEvent_I:RedesignPlayProgressEventPresenter_T:RedesignPlayProgressEventPresenter	3.0		1469468169828259.0
219574	292760	K_accept_I:SidebarProgressElement$onBind$2_T:	4.0		1469468170147113.0
219575	277344	K_accept_I:SidebarProgressElement$onBind$2_T:	5.0		1469468170157946.0
219576	251094	K_onMediaPlayProgressChange_I:SidebarProgressElement_T:SidebarProgressElement	6.0		1469468170179092.0
219577	228698	K_updateProgress_I:SidebarProgressElement_T:SidebarProgressElement	7.0		1469468170196280.0
219592	417396	K_lambda$becomesAttachedOnPageSelected$0_I:NasaMilanoProgressPresenter$1_T:	4.0		1469468170452894.0
219593	394739	K_updateSeekBarProgressWhenPlayProgressChange_I:NasaMilanoProgressPresenter_T:NasaMilanoProgressPresenter	5.0		1469468170470759.0
219630	23956094	android.os.Handler: io.reactivex.android.schedulers.HandlerScheduler$ScheduledRunnable	1.0		1469468171015394.0
219631	23921979	K_run_I:HandlerScheduler$ScheduledRunnable_T:ScheduledRunnable	2.0		1469468171043155.0
219632	23900937	K_run_I:ObservableObserveOn$ObserveOnObserver_T:ObserveOnObserver	3.0		1469468171058988.0
219634	23819636	K_accept_I:DanmakuElement$notifyDanmakuInputVisible$1$1_T:DanmakuElement$notifyDanmakuInputVisible$1$1	4.0		1469468171090342.0
219635	23793020	K_accept_I:DanmakuElement$notifyDanmakuInputVisible$1$1_T:DanmakuElement$notifyDanmakuInputVisible$1$1	5.0		1469468171111853.0
219642	23571094	K_notifyDanmakuInputVisible_I:DanmakuGroupEventBus_T:DanmakuGroupEventBus	6.0		1469468171328050.0
219643	23522084	K_accept_I:BasePage$bindInner$$inlined$dispatchRun$lambda$15_T:	7.0		1469468171372269.0
219644	23500885	K_accept_I:BasePage$bindInner$$inlined$dispatchRun$lambda$15_T:	8.0		1469468171388936.0
219647	23424896	K_notifyDanmakuInputVisible_I:InformationGroupEventBus_T:InformationGroupEventBus	9.0		1469468171459509.0
219648	23386979	K_lambda$doInBind$0_I:BaseSendDanmakuNewElement_T:SendDanmakuLowPriorityNewElement	10.0		1469468171492009.0
219649	23362344	K_refreshVisible_I:BaseSendDanmakuNewElement_T:SendDanmakuLowPriorityNewElement	11.0		1469468171512269.0
219659	22504063	K_tryShow_I:BaseElement_T:SendDanmakuLowPriorityNewElement	12.0		1469468171825498.0
219666	22265209	K_setValue_I:KLiveData_T:KDispatchLiveData	13.0		1469468172059769.0
219667	22247031	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	14.0		1469468172073155.0
219670	22207292	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	15.0		1469468172107790.0
219671	22186927	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	16.0		1469468172122894.0
219673	22170052	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	17.0		1469468172134509.0
219674	22152031	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	18.0		1469468172147790.0
219675	22130625	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	19.0		1469468172163728.0
219676	22111093	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	20.0		1469468172176228.0
219685	1347448	K_calculateElementRealShow_I:BaseGroup_T:InformationGroup	21.0		1469468172510915.0
219790	16974271	K_create_I:BaseElementView_T:SendDanmakuElementView	21.0		1469468174697790.0
219791	16273958	K_createView_I:SendDanmakuElementView_T:SendDanmakuElementView	22.0		1469468174715707.0
219794	10634062	AssetManager::OpenXmlAsset(r/ao/y6.xml)	23.0		1469468174837113.0
219872	5469687	inflate	23.0		1469468185500759.0
219875	288333	LinearLayout	24.0		1469468185771540.0
219886	2233906	res/color/highlighted_text_material.xml	24.0		1469468186740030.0
219887	2156875	AssetManager::OpenXmlAsset(res/color/highlighted_text_material.xml)	25.0		1469468186756957.0
220018	238229	K_<init>_I:AppCompatTextView_T:AppCompatTextView	24.0		1469468189224040.0
220037	903334	r/a_/b1o.xml	24.0		1469468189761696.0
220093	649688	K_onViewCreated_I:SendDanmakuElementView_T:SendDanmakuElementView	22.0		1469468191015915.0
220149	693281	K_bindData_I:BaseElementView_T:SendDanmakuElementView	21.0		1469468191826280.0
220151	668177	K_onBindData_I:SendDanmakuElementView_T:SendDanmakuElementView	22.0		1469468191844509.0
220153	645886	K_onBindData_I:SendDanmakuElementView_T:SendDanmakuElementView	23.0		1469468191861800.0
220210	1342968	K_layout_I:LinearGroupLayout_T:LinearGroupLayout	21.0		1469468192683207.0
220328	494635	K_show_I:SendDanmakuViewModel_T:SendDanmakuViewModel	12.0		1469468194375134.0
220329	473438	K_setValue_I:KLiveData_T:KDispatchLiveData	13.0		1469468194391071.0
220330	456354	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	14.0		1469468194403780.0
220333	413906	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	15.0		1469468194441280.0
220334	396407	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	16.0		1469468194453675.0
220335	379114	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	17.0		1469468194465811.0
220337	362292	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	18.0		1469468194477373.0
220339	329792	K_onChanged_I:SendDanmakuElementView$onBindData$1_T:SendDanmakuElementView$onBindData$1	19.0		1469468194505186.0
220340	306406	K_onChanged_I:SendDanmakuElementView$onBindData$1_T:SendDanmakuElementView$onBindData$1	20.0		1469468194523728.0
220344	272083	K_show_I:SendDanmakuElementView_T:SendDanmakuElementView	21.0		1469468194552738.0
220367	24012344	android.os.Handler: io.reactivex.android.schedulers.HandlerScheduler$ScheduledRunnable	1.0		1469468195096696.0
220371	23975313	K_run_I:HandlerScheduler$ScheduledRunnable_T:ScheduledRunnable	2.0		1469468195127425.0
220374	23950937	K_run_I:ObservableObserveOn$ObserveOnObserver_T:ObserveOnObserver	3.0		1469468195145863.0
220376	23920417	K_accept_I:DanmakuElement$initKit$2$1$1_T:DanmakuElement$initKit$2$1$1	4.0		1469468195168363.0
220378	23886823	K_accept_I:DanmakuElement$initKit$2$1$1_T:DanmakuElement$initKit$2$1$1	5.0		1469468195196592.0
220379	23861563	K_execute_I:DanmakuElement_T:DanmakuElement	6.0		1469468195215550.0
220388	23707656	K_updateVisible_I:DanmakuElement_T:DanmakuElement	7.0		1469468195364092.0
220402	256511	K_notifyDanmakuInputVisible_I:DanmakuElement_T:DanmakuElement	8.0		1469468195547217.0
220408	210156	K_apply_I:DanmakuElement$notifyDanmakuInputVisible$1_T:	9.0		1469468195587582.0
220422	1772187	K_showDanmakuList_I:NasaDetailDanmakuUtil_T:NasaDetailDanmakuUtil	8.0		1469468195825863.0
220439	1609584	K_tryShowDanmakuTipBubble_I:CommentElement_T:CommentElement	9.0		1469468195980394.0
220443	1580104	K_setCommentCount_I:CommentElement_T:CommentElement	10.0		1469468196004353.0
220468	1306511	K_setComment_I:CommentViewModel_T:CommentViewModel	11.0		1469468196272269.0
220511	752500	K_setValue_I:KLiveData_T:KDispatchLiveData	12.0		1469468196820759.0
220513	734896	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	13.0		1469468196833259.0
220516	690677	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	14.0		1469468196872165.0
220517	672032	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	15.0		1469468196886071.0
220519	649427	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	16.0		1469468196903832.0
220520	630208	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	17.0		1469468196915863.0
220524	601302	K_onChanged_I:CommentElementView$onBindData$1_T:CommentElementView$onBindData$1	18.0		1469468196939405.0
220525	580261	K_onChanged_I:CommentElementView$onBindData$1_T:CommentElementView$onBindData$1	19.0		1469468196953571.0
220569	1336458	K_tryShow_I:BaseElement_T:DanmakuElement	8.0		1469468197773207.0
220581	1097604	K_setValue_I:KLiveData_T:KDispatchLiveData	9.0		1469468198006696.0
220582	1079948	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	10.0		1469468198019248.0
220585	1035208	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	11.0		1469468198058884.0
220588	1009948	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	12.0		1469468198078571.0
220590	985209	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	13.0		1469468198096123.0
220591	968177	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	14.0		1469468198108155.0
220593	941354	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	15.0		1469468198122061.0
220594	920937	K_onChanged_I:BaseGroup$observer$1_T:BaseGroup$observer$1	16.0		1469468198136332.0
220612	283802	K_calculateElementRealShow_I:BaseGroup_T:DanmakuGroup	17.0		1469468198423728.0
220674	19761250	K_attach_I:DanmakuElement_T:DanmakuElement	8.0		1469468199303728.0
221254	1336511	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	9.0		1469468211834248.0
221255	451354	K_logMsg_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	10.0		1469468211853571.0
221260	308073	K_getLogPrefix_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl	11.0		1469468211901488.0
221283	832761	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	10.0		1469468212332842.0
221284	454896	K_logMsg_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	11.0		1469468212349821.0
221287	259166	K_getLogPrefix_I:KwaiMediaPlayerWrapper_T:QPhotoMediaPlayerImplV3	12.0		1469468212449509.0
221301	329271	K_getIKwaiMediaPlayer_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	11.0		1469468212831071.0
221302	271093	K_logMsg_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3	12.0		1469468212852530.0
221340	699218	K_accept_I:SerializedRelay_T:SerializedRelay	9.0		1469468214094353.0
221341	675260	K_accept_I:PublishRelay_T:PublishRelay	10.0		1469468214107634.0
221366	3296823	K_setState$detail_release_I:DanmakuElement$State_T:State	9.0		1469468215437009.0
221376	3039167	K_accept_I:DanmakuElement$sam$androidx_core_util_Consumer$0_T:DanmakuElement$sam$androidx_core_util_Consumer$0	10.0		1469468215688936.0
221377	3001875	K_invoke_I:DanmakuElement$mState$2_T:	11.0		1469468215719717.0
221378	2976979	K_invoke_I:DanmakuElement$mState$2_T:	12.0		1469468215738103.0
221379	2953542	K_show_I:DanmakuElement_T:DanmakuElement	13.0		1469468215756071.0
221389	1807240	K_showDanmakuList_I:NasaDetailDanmakuUtil_T:NasaDetailDanmakuUtil	14.0		1469468215950238.0
221403	1636719	K_tryShowDanmakuTipBubble_I:CommentElement_T:CommentElement	15.0		1469468216114144.0
221404	1613386	K_setCommentCount_I:CommentElement_T:CommentElement	16.0		1469468216130446.0
221422	1360156	K_setComment_I:CommentViewModel_T:CommentViewModel	17.0		1469468216377217.0
221454	846458	K_setValue_I:KLiveData_T:KDispatchLiveData	18.0		1469468216884665.0
221457	649375	K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData	19.0		1469468217076332.0
221459	590000	K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction	20.0		1469468217129978.0
221460	569895	K_access$301_I:KDispatchLiveData_T:KDispatchLiveData	21.0		1469468217144405.0
221461	549010	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	22.0		1469468217159665.0
221463	529323	K_notifyValueSet_I:KLiveData_T:KDispatchLiveData	23.0		1469468217173832.0
221464	499948	K_onChanged_I:CommentElementView$onBindData$1_T:CommentElementView$onBindData$1	24.0		1469468217197113.0
221465	477812	K_onChanged_I:CommentElementView$onBindData$1_T:CommentElementView$onBindData$1	25.0		1469468217213207.0
221501	428334	android.os.Handler: io.reactivex.android.schedulers.HandlerScheduler$ScheduledRunnable	1.0		1469468219247894.0
221502	386302	K_run_I:HandlerScheduler$ScheduledRunnable_T:ScheduledRunnable	2.0		1469468219282946.0
221503	358021	K_run_I:ObservableObserveOn$ObserveOnObserver_T:ObserveOnObserver	3.0		1469468219305446.0
221504	299531	K_accept_I:ActivityPendantCommonVM$sam$io_reactivex_functions_Consumer$0_T:ActivityPendantCommonVM$sam$io_reactivex_functions_C	4.0		1469468219352478.0
221506	428750	android.os.Handler: io.reactivex.android.schedulers.HandlerScheduler$ScheduledRunnable	1.0		1469468219732842.0
221507	390781	K_run_I:HandlerScheduler$ScheduledRunnable_T:ScheduledRunnable	2.0		1469468219764144.0
221508	362865	K_run_I:ObservableObserveOn$ObserveOnObserver_T:ObserveOnObserver	3.0		1469468219786123.0
221509	331041	K_accept_I:NasaSpecialCameraIconPresenter$onBind$1_T:	4.0		1469468219807634.0
221514	16444063	android.view.Choreographer$FrameHandler: android.view.Choreographer$FrameDisplayEventReceiver	1.0		1469468220208519.0
221515	16406094	Choreographer#doFrame 13432349	2.0		1469468220234873.0
221516	16353542	Choreographer#skippedFrames 7	3.0		1469468220277946.0
221518	254844	animation	4.0		1469468220349821.0
221528	15986250	traversal	4.0		1469468220616228.0
221529	2568854	measure	5.0		1469468220661436.0
221530	2499218	K_onMeasure_I:KwaiSlidingPaneLayout_T:KwaiSlidingPaneLayout	6.0		1469468220711905.0
221531	2466615	K_onMeasure_I:SlidingPaneLayout_T:KwaiSlidingPaneLayout	7.0		1469468220733050.0
221532	2265573	K_onMeasure_I:ScrollStrategyViewPager_T:ScrollStrategyViewPager	8.0		1469468220753832.0
221533	2242916	K_onMeasure_I:ViewPager_T:ScrollStrategyViewPager	9.0		1469468220769405.0
221534	2183906	K_onMeasure_I:ConstraintLayout_T:ConstraintLayout	10.0		1469468220819769.0
221536	1078125	K_onMeasure_I:KwaiGrootViewPager_T:KwaiGrootViewPager	11.0		1469468220980759.0
221537	1037448	K_onMeasure_I:VerticalViewPager_T:KwaiGrootViewPager	12.0		1469468221014561.0
221563	756302	K_onMeasure_I:KwaiGrootViewPager_T:KwaiGrootViewPager	11.0		1469468222201019.0
221566	727135	K_onMeasure_I:VerticalViewPager_T:KwaiGrootViewPager	12.0		1469468222224040.0
221611	4659688	layout	5.0		1469468223361175.0
221612	4419115	K_onLayout_I:KwaiSlidingPaneLayout_T:KwaiSlidingPaneLayout	6.0		1469468223394769.0
221615	4397031	K_onLayout_I:SlidingPaneLayout_T:KwaiSlidingPaneLayout	7.0		1469468223410655.0
221616	4320886	K_onLayout_I:ViewPager_T:ScrollStrategyViewPager	8.0		1469468223434196.0
221619	4267031	K_onLayout_I:ConstraintLayout_T:ConstraintLayout	9.0		1469468223471644.0
221622	4190730	K_onLayout_I:KwaiGrootViewPager_T:KwaiGrootViewPager	10.0		1469468223527425.0
221623	4141042	K_onLayout_I:VerticalViewPager_T:KwaiGrootViewPager	11.0		1469468223545550.0
221654	1761406	K_onMeasure_I:UserNameLinearLayout_T:UserNameLinearLayout	12.0		1469468224394561.0
221655	1643386	K_onMeasure_I:PriorityLinearLayout_T:UserNameLinearLayout	13.0		1469468224421071.0
221656	783593	K_generateMeasureList_I:PriorityLinearLayout_T:UserNameLinearLayout	14.0		1469468224436905.0
221774	324635	K_onLayout_I:KwaiSizeAdjustableTextView_T:KwaiSizeAdjustableTextView	12.0		1469468227230082.0
221798	578386	K_onGlobalLayout_I:InformationGroup$mOnGlobalLayoutListener$1_T:	5.0		1469468228093571.0
221800	528125	K_notifyInformationPosition_I:InformationGroupEventBus_T:InformationGroupEventBus	6.0		1469468228138675.0
221801	503542	K_accept_I:BasePage$bindInner$$inlined$dispatchRun$lambda$16_T:	7.0		1469468228157686.0
221802	483541	K_accept_I:BasePage$bindInner$$inlined$dispatchRun$lambda$16_T:	8.0		1469468228172478.0
221806	405573	K_notifyInformationPosition_I:CenterEntranceGroupEventBus_T:CenterEntranceGroupEventBus	9.0		1469468228244769.0
221807	382083	K_accept_I:LandscapeCenterEntranceElement$onBind$4_T:	10.0		1469468228262634.0
221808	361770	K_accept_I:LandscapeCenterEntranceElement$onBind$4_T:	11.0		1469468228276853.0
221809	334167	K_changeVerticalLocation_I:LandscapeCenterEntranceElement_T:LandscapeCenterEntranceElement	12.0		1469468228299300.0
221832	507239	K_onGlobalLayout_I:InformationGroup$mOnGlobalLayoutListener$1_T:	5.0		1469468228851853.0
221835	455989	K_notifyInformationPosition_I:InformationGroupEventBus_T:InformationGroupEventBus	6.0		1469468228897478.0
221836	432969	K_accept_I:BasePage$bindInner$$inlined$dispatchRun$lambda$16_T:	7.0		1469468228913519.0
221837	414584	K_accept_I:BasePage$bindInner$$inlined$dispatchRun$lambda$16_T:	8.0		1469468228926696.0
221840	344792	K_notifyInformationPosition_I:CenterEntranceGroupEventBus_T:CenterEntranceGroupEventBus	9.0		1469468228990811.0
221841	323386	K_accept_I:LandscapeCenterEntranceElement$onBind$4_T:	10.0		1469468229006696.0
221842	297343	K_accept_I:LandscapeCenterEntranceElement$onBind$4_T:	11.0		1469468229027478.0
221843	275364	K_changeVerticalLocation_I:LandscapeCenterEntranceElement_T:LandscapeCenterEntranceElement	12.0		1469468229044457.0
221861	7092656	draw	5.0		1469468229486019.0
221862	5793178	Record View#draw()	6.0		1469468229511800.0
221873	320156	K_onPageScrolled_I:GrootViewPager$1_T:GrootViewPager$1	7.0		1469468230030811.0
221905	507708	K_onPageScrolled_I:NasaFeaturedAutoRefreshPresenter$2_T:	7.0		1469468230623311.0
221914	312552	K_getRealPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	8.0		1469468230809821.0
221938	804948	K_onPageScrolled_I:DraggingLayerPresenter$mPageChangeListener$1_T:	7.0		1469468231157686.0
221942	622969	K_getPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	8.0		1469468231247946.0
221950	503073	K_getPositionInAdapter_I:PositionService_T:PositionService	9.0		1469468231360238.0
221952	313906	K_getPositionInAdapter_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter	10.0		1469468231379509.0
222011	787395	K_onPageScrolled_I:DraggingLayerPresenter$mPageChangeListener$1_T:	7.0		1469468232045603.0
222017	679375	K_getPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel	8.0		1469468232110342.0
222029	553281	K_getPositionInAdapter_I:PositionService_T:PositionService	9.0		1469468232230238.0
222031	361458	K_getPositionInAdapter_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter	10.0		1469468232261228.0
222099	728854	K_onSurfaceTextureUpdated_I:SurfaceTextureProxy_T:SurfaceTextureProxy	7.0		1469468233264978.0
222104	673229	K_onSurfaceTextureUpdated_I:PlayerKitContentFrame$3_T:PlayerKitContentFrame$3	8.0		1469468233314769.0
222106	648281	K_quickRunOnUiThread_I:ThreadUtils_T:ThreadUtils	9.0		1469468233330967.0
