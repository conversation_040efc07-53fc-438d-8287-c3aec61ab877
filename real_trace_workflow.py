#!/usr/bin/env python3
"""
处理真实trace文件的RAG工作流程
"""

import os
import pandas as pd
import re
from openai import OpenAI
from kp import KwaiPilot

# 配置
API_KEY_QWEN = "4kqyobx9g9jjixgk0xaqool9ldkc5e83qxl7"
API_KEY_ANALYSIS = "ofdvapet0gfvdtnpdbr5pffd2qvn44sxt32h"
KWAI_PILOT_TOKEN = "DDKmVYBIF2P9aN"
KWAI_PILOT_USER = "lvaolin"

def extract_real_trace_data():
    """提取真实trace数据"""
    print("🔄 开始提取真实trace数据...")

    tp = None
    try:
        from perfetto.trace_processor import TraceProcessor

        # 初始化TraceProcessor
        print("初始化TraceProcessor...")
        tp = TraceProcessor(trace='./trace.perfetto-trace')

        # 执行原始查询
        print("执行SQL查询...")
        qr_it = tp.query('''
        select id, dur, name, depth, thread_dur, ts
        from slice
        where track_id in (11)
        order by ts asc, depth asc
        ''')

        qr_df = qr_it.as_pandas_dataframe()

        # 保存原始结果
        output_path = './query_result.txt'
        qr_df.to_csv(output_path, sep='\t', index=False)

        print(f"✅ 成功提取 {len(qr_df)} 行数据")
        print(f"保存到: {output_path}")

        return qr_df

    except Exception as e:
        print(f"❌ 提取失败: {e}")

        if tp is not None:
            print("尝试使用更简单的查询...")
            try:
                # 尝试更简单的查询
                qr_it = tp.query('''
                select id, dur, name, depth, thread_dur, ts
                from slice
                where dur > 1000000
                order by dur desc
                limit 1000
                ''')

                qr_df = qr_it.as_pandas_dataframe()

                if len(qr_df) > 0:
                    output_path = './query_result.txt'
                    qr_df.to_csv(output_path, sep='\t', index=False)
                    print(f"✅ 简化查询成功提取 {len(qr_df)} 行数据")
                    return qr_df
                else:
                    print("❌ 查询返回空结果")

            except Exception as e2:
                print(f"❌ 简化查询也失败: {e2}")

        # 如果所有方法都失败，提供手动解决方案
        print("\n🔧 自动提取失败，请尝试以下解决方案:")
        print("1. 使用Perfetto Web UI: https://ui.perfetto.dev")
        print("2. 上传您的trace文件")
        print("3. 执行以下SQL查询:")
        print("   SELECT id, dur, name, depth, thread_dur, ts FROM slice WHERE track_id IN (11) ORDER BY ts ASC, depth ASC")
        print("4. 导出结果为CSV格式")
        print("5. 将文件重命名为 'query_result.txt' 并放在当前目录")
        print("6. 然后重新运行此脚本")

        return None

def filter_short_frames(df, threshold_ms=16.66):
    """过滤持续时间小于指定阈值的帧"""
    print(f"🔄 过滤短于 {threshold_ms}ms 的帧...")
    
    df = df.copy()
    threshold_ns = threshold_ms * 1_000_000 

    current_group = -1
    groups = []
    for depth in df['depth']:
        if depth == 0:
            current_group += 1
        groups.append(current_group)
    df['_group'] = groups
    
    try:
        group_first = df.groupby('_group').first()
        valid_groups = group_first[group_first['dur'] >= threshold_ns].index
        result_df = df[df['_group'].isin(valid_groups)].drop(columns='_group')
    finally:
        if '_group' in df.columns:
            df.drop(columns='_group', inplace=True)
    
    print(f"✅ 过滤后剩余: {len(result_df)} 行")
    return result_df.reset_index(drop=True)

def filter_dur(df, threshold=0.02):
    """过滤持续时间"""
    print(f"🔄 过滤短于 {threshold}ms 的持续时间...")
    result_df = df[df['dur'] >= threshold * 1e6]
    print(f"✅ 过滤后剩余: {len(result_df)} 行")
    return result_df

def split_into_blocks_and_save(df, path, chunk_size=1700):
    """拆分数据块"""
    print(f"🔄 分块数据，每块最多 {chunk_size} 行...")
    
    path = path if path.endswith("/") else path + "/"
    os.makedirs(path, exist_ok=True)

    # 找到所有 depth=0 的行的索引
    zero_depth_indices = df[df['depth'] == 0].index.tolist()

    if not zero_depth_indices:
        print("未找到 depth=0 的行，使用全部数据。")
        filename = f"{path}block_1.txt"
        df.to_csv(filename, sep='\t', index=False, 
                 columns=['id', 'dur', 'name', 'depth', 'thread_dur', 'ts'])
        print(f"保存: {filename} (行数: {len(df)})")
        return [df]

    # 分割原始块
    original_blocks = []
    num_blocks = len(zero_depth_indices)
    for i in range(num_blocks):
        start_idx = zero_depth_indices[i]
        end_idx = zero_depth_indices[i+1]-1 if i < num_blocks-1 else df.index[-1]
        original_blocks.append(df.loc[start_idx:end_idx])

    # 合并处理逻辑
    merged_blocks = []
    current_chunks = []
    current_rows = 0

    for chunk in original_blocks:
        chunk_rows = len(chunk)

        if current_rows + chunk_rows >= chunk_size:
            if current_chunks:
                merged = pd.concat(current_chunks)
                merged_blocks.append(merged)
                current_chunks = []
                current_rows = 0

            if chunk_rows >= chunk_size:
                merged_blocks.append(chunk)
            else:
                current_chunks.append(chunk)
                current_rows += chunk_rows
        else:
            current_chunks.append(chunk)
            current_rows += chunk_rows

    if current_chunks:
        merged = pd.concat(current_chunks)
        merged_blocks.append(merged)

    # 保存处理
    parent_block_num = 1
    blocks_to_return = []

    for parent_block in merged_blocks:
        total_rows = len(parent_block)

        if total_rows > chunk_size:
            num_sub_blocks = (total_rows - 1) // chunk_size + 1

            for sub_idx in range(num_sub_blocks):
                start = sub_idx * chunk_size
                end = start + chunk_size
                sub_block = parent_block.iloc[start:end]

                filename = f"{path}block_{parent_block_num}_{sub_idx+1}.txt"
                sub_block.to_csv(filename, sep='\t', index=False, header=True,
                               columns=['id', 'dur', 'name', 'depth', 'thread_dur', 'ts'])
                print(f"切分保存: {filename} (行数: {len(sub_block)})")
                blocks_to_return.append(sub_block)
        else:
            filename = f"{path}block_{parent_block_num}.txt"
            parent_block.to_csv(filename, sep='\t', index=False, header=True,
                              columns=['id', 'dur', 'name', 'depth', 'thread_dur', 'ts'])
            print(f"直接保存: {filename} (行数: {total_rows})")
            blocks_to_return.append(parent_block)

        parent_block_num += 1

    print(f"✅ 生成了 {len(blocks_to_return)} 个数据块")
    return blocks_to_return

def step1_extract_and_process_data():
    """步骤1: 数据提取和预处理"""
    print("=== 步骤1: 提取和处理真实trace数据 ===")
    
    # 提取真实数据
    qr_df = extract_real_trace_data()
    if qr_df is None:
        print("❌ 数据提取失败")
        return False
    
    # 过滤数据
    filtered_df = filter_short_frames(qr_df)
    filtered_df = filter_dur(filtered_df, threshold=0.2)
    
    # 分块
    dfs = split_into_blocks_and_save(filtered_df, path="./chunk3/")
    
    print("✅ 数据预处理完成！")
    return True

def step2_ai_analysis():
    """步骤2: AI分析日志信息"""
    print("\n=== 步骤2: AI分析日志信息 ===")
    
    client = OpenAI(
        base_url="https://wanqing-api.staging.kuaishou.com/api/agent/v1/apps",
        api_key=API_KEY_QWEN
    )
    
    chunk_files = [f for f in os.listdir("./chunk3/") if f.endswith('.txt')]
    os.makedirs("./log_anaysis", exist_ok=True)
    
    for i, filename in enumerate(chunk_files):
        print(f"处理文件: {filename}")
        
        with open(f"./chunk3/{filename}", 'r', encoding='utf-8') as f:
            input_content = f.read()
        
        try:
            response_text = ""
            stream = client.chat.completions.create(
                model="app-msvz3y-1748316514060935132",
                messages=[{"role": "user", "content": input_content}],
                stream=True,
                extra_body={"enable_thinking": False}
            )

            for chunk in stream:
                if chunk.choices and chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    response_text += content

            output_file = f"./log_anaysis/response_df{i}.txt"
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(response_text)
            
            print(f"✅ 保存分析结果: {output_file}")
            
        except Exception as e:
            print(f"❌ 处理 {filename} 时出错: {e}")
    
    print("✅ AI分析完成")

def step3_code_search():
    """步骤3: 代码搜索"""
    print("\n=== 步骤3: 代码搜索 ===")
    
    os.makedirs("./code", exist_ok=True)
    pilot = KwaiPilot(token=KWAI_PILOT_TOKEN, user_name=KWAI_PILOT_USER)
    
    pattern = re.compile(r'<think>.*?</think>', re.DOTALL)
    
    for filename in os.listdir("./log_anaysis"):
        if filename.endswith(".txt"):
            print(f"处理文件: {filename}")
            
            file_path = os.path.join("./log_anaysis", filename)
            
            with open(file_path, 'r', encoding='utf-8') as f:
                raw_content = f.read().strip()
                clean_query = pattern.sub('', raw_content).strip()
                query = clean_query if clean_query else raw_content

            try:
                answer = pilot.codeSearch(query=query)
                
                output_filename = f"result_{filename}"
                output_path = os.path.join("./code", output_filename)
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(answer)
                
                print(f"✅ 保存代码搜索结果: {output_path}")
                
            except Exception as e:
                print(f"❌ 代码搜索失败 {filename}: {e}")
    
    print("✅ 代码搜索完成")

def main():
    """主流程"""
    print("=== 真实Trace文件RAG工作流程 ===\n")
    
    # 执行数据提取和预处理
    if not step1_extract_and_process_data():
        return
    
    # 执行AI分析
    step2_ai_analysis()
    
    # 执行代码搜索
    step3_code_search()
    
    print("\n🎉 真实trace数据处理完成！")
    print("接下来可以运行后续的合并和最终分析步骤...")
    print("python complete_workflow.py  # 从步骤4开始")

if __name__ == "__main__":
    main()
