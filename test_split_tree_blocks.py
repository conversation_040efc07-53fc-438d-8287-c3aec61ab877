import pandas as pd

def find_subtree_end(df, start_idx):
    root_depth = df.iloc[start_idx]['depth']
    n = len(df)
    end_idx = start_idx
    for i in range(start_idx + 1, n):
        if df.iloc[i]['depth'] <= root_depth:
            break
        end_idx = i
    return end_idx

def get_parent_path(df, idx):
    path = []
    cur_depth = df.iloc[idx]['depth']
    j = idx - 1
    while j >= 0 and cur_depth > 0:
        if df.iloc[j]['depth'] < cur_depth:
            path.insert(0, j)
            cur_depth = df.iloc[j]['depth']
        j -= 1
    return path

def split_tree_blocks_with_parents(df, chunk_size=8):
    blocks = []
    n = len(df)
    i = 0
    while i < n:
        # 1. 先补父节点
        parent_path = get_parent_path(df, i)
        block_indices = list(parent_path)
        block_len = len(block_indices)
        # 2. 然后尽量多地加入完整子树
        first = True
        while i < n:
            subtree_end = find_subtree_end(df, i)
            subtree_len = subtree_end - i + 1
            # 如果加下一个子树会超限
            if block_len + subtree_len > chunk_size:
                # 如果当前块还没内容，且单个子树就超限，强行单独成块
                if first:
                    block_indices.extend(range(i, subtree_end + 1))
                    i = subtree_end + 1
                break
            block_indices.extend(range(i, subtree_end + 1))
            block_len += subtree_len
            i = subtree_end + 1
            first = False
        block_indices = sorted(set(block_indices))
        block = df.iloc[block_indices]
        blocks.append(block)
    return blocks

if __name__ == "__main__":
    df = pd.read_csv("test_tree.csv")
    blocks = split_tree_blocks_with_parents(df, chunk_size=8)
    for idx, block in enumerate(blocks):
        print(f"\nBlock {idx+1}:")
        print(block) 