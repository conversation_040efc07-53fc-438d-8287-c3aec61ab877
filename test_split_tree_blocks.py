import pandas as pd

def find_subtree_end(df, start_idx):
    root_depth = df.iloc[start_idx]['depth']
    n = len(df)
    end_idx = start_idx
    for i in range(start_idx + 1, n):
        if df.iloc[i]['depth'] <= root_depth:
            break
        end_idx = i
    return end_idx

def get_parent_path(df, idx):
    path = []
    cur_depth = df.iloc[idx]['depth']
    j = idx - 1
    while j >= 0 and cur_depth > 0:
        if df.iloc[j]['depth'] < cur_depth:
            path.insert(0, j)
            cur_depth = df.iloc[j]['depth']
        j -= 1
    return path

def split_tree_blocks_with_parents(df, chunk_size=8):
    blocks = []
    n = len(df)
    i = 0
    continuing_subtree = False  # 标记是否是同一子树的延续
    parent_path = []

    while i < n:
        # 动态获取父节点路径
        if continuing_subtree:
            # 如果是子树延续，不添加父节点
            block_indices = set()
            continuing_subtree = False
        else:
            # 新的子树，动态获取父节点路径
            parent_path = get_parent_path(df, i)
            block_indices = set(parent_path)

        # 3. 尝试添加当前子树
        subtree_end = find_subtree_end(df, i)
        subtree_indices = set(range(i, subtree_end + 1))

        # 检查加入当前子树后是否超限
        combined_indices = block_indices | subtree_indices

        if len(combined_indices) > chunk_size:
            # 如果加入当前子树会超限
            if len(block_indices) == 0:
                # 如果连父节点都没有，说明单个子树就超限
                if len(combined_indices) > chunk_size:
                    # 加上父节点后超过chunk_size，需要截断
                    print(f"警告: 子树 {i}-{subtree_end} 大小 {len(subtree_indices)} 加上父节点 {len(block_indices)} 超过chunk_size {chunk_size}，截断处理")

                    # 计算可以容纳的子树节点数量
                    available_space = chunk_size - len(block_indices)
                    if available_space <= 0:
                        # 父节点已经占满，只能包含父节点，强制跳过当前节点
                        print(f"警告: 父节点已占满空间，跳过当前节点 {i}")
                        i = i + 1  # 强制前进，避免死循环
                    else:
                        # 截断子树以适应剩余空间
                        truncated_subtree = set(range(i, min(i + available_space, subtree_end + 1)))
                        block_indices = block_indices | truncated_subtree
                        # 更新i到截断位置的下一个
                        new_i = min(i + available_space, subtree_end + 1)
                        # 如果还没到子树结尾，标记为延续
                        if new_i <= subtree_end:
                            continuing_subtree = True
                        i = new_i
                else:
                    # 子树大小正常，但加上父节点会超限，单独成块
                    print(f"警告: 子树 {i}-{subtree_end} 加上父节点后超过chunk_size，单独成块")
                    block_indices = subtree_indices
                    i = subtree_end + 1
            else:
                # 如果已有父节点，当前子树无法加入，结束当前块
                # 当前子树留给下一个块处理
                pass  # i 不变，下次循环处理
        else:
            # 可以加入当前子树
            block_indices = combined_indices
            i = subtree_end + 1

            # 4. 尝试继续添加更多完整子树
            while i < n:
                subtree_end = find_subtree_end(df, i)
                subtree_indices = set(range(i, subtree_end + 1))

                # 获取新子树的父节点路径
                next_parent_path = get_parent_path(df, i)
                next_parent_indices = set(next_parent_path)

                # 检查加入下一个子树（包括其父节点）是否会超限
                new_combined = block_indices | subtree_indices | next_parent_indices
                if len(new_combined) > chunk_size:
                    # 会超限，立即切分
                    break

                # 不会超限，加入这个子树和其父节点
                block_indices = new_combined
                i = subtree_end + 1

        # 5. 生成当前块
        block_indices = sorted(block_indices)
        block = df.iloc[block_indices]
        blocks.append(block)

        print(f"生成块 {len(blocks)}: 大小 {len(block_indices)}/{chunk_size}, 下一个i={i}, continuing={continuing_subtree}")

    return blocks

def test_multiple_subtrees():
    """测试多个子树的处理，模拟您遇到的问题"""
    print("=== 测试多个子树处理 ===")

    data = []
    # 第一个大子树 (会被截断)
    data.append([1, 10, "main1", 0, 10, 100])
    for i in range(2, 20):  # 18个节点的大子树
        data.append([i, 10, f"func1_{i}", 1, 10, 100 + i * 10])

    # 第二个正常子树
    data.append([20, 10, "main2", 0, 10, 300])
    data.append([21, 10, "func2_1", 1, 10, 310])
    data.append([22, 10, "func2_2", 1, 10, 320])

    # 第三个正常子树
    data.append([23, 10, "main3", 0, 10, 400])
    data.append([24, 10, "func3_1", 1, 10, 410])
    data.append([25, 10, "func3_2", 1, 10, 420])
    data.append([26, 10, "func3_3", 1, 10, 430])

    df = pd.DataFrame(data, columns=['id', 'dur', 'name', 'depth', 'thread_dur', 'ts'])

    print(f"总数据量: {len(df)} 行")

    # 显示子树边界
    i = 0
    subtree_count = 0
    while i < len(df):
        end = find_subtree_end(df, i)
        subtree_count += 1
        print(f"子树 {subtree_count}: 索引 {i}-{end}, 大小 {end-i+1}, 根节点: {df.iloc[i]['name']}")
        i = end + 1

    print("\n=== 开始分块 ===")
    blocks = split_tree_blocks_with_parents(df, chunk_size=8)

    for idx, block in enumerate(blocks):
        print(f"\nBlock {idx+1}: 大小 {len(block)}/8")
        if len(block) > 0:
            print(f"节点: {block['name'].tolist()}")

if __name__ == "__main__":
    test_multiple_subtrees()