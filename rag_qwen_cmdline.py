#!/usr/bin/env python3
"""
按照rag+qwen.ipynb的逻辑，使用命令行调用perfetto处理数据
完全复制原始notebook的处理流程
"""

import os
import subprocess
import pandas as pd
import re
from openai import OpenAI
from kp import KwaiPilot

# 配置 - 从原始notebook复制
API_KEY_QWEN = "4kqyobx9g9jjixgk0xaqool9ldkc5e83qxl7"
API_KEY_ANALYSIS = "ofdvapet0gfvdtnpdbr5pffd2qvn44sxt32h"
API_KEY_SUMMARY = "ofdvapet0gfvdtnpdbr5pffd2qvn44sxt32h"
KWAI_PILOT_TOKEN = "DDKmVYBIF2P9aN"
KWAI_PILOT_USER = "lvaolin"

def step1_sql_extract():
    """步骤1: SQL提取信息 - 使用命令行perfetto"""
    print("=== 步骤1: SQL提取信息 ===")
    
    # 原始SQL查询
    sql_query = '''
    select id, dur, name, depth, thread_dur, ts 
    from slice 
    where track_id in (11) 
    order by ts asc, depth asc
    '''
    
    # 写入SQL文件
    sql_file = 'extract_query.sql'
    with open(sql_file, 'w') as f:
        f.write(sql_query)
    
    try:
        # 使用命令行工具执行查询
        shell_path = './mac-arm64/trace_processor_shell'
        cmd = [shell_path, '--query-file', sql_file, 'trace.perfetto-trace']
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0 and result.stdout.strip():
            lines = result.stdout.strip().split('\n')
            if len(lines) > 1:  # 有数据行
                # 解析数据
                data = []
                for line in lines[1:]:  # 跳过表头
                    parts = line.split('|')  # Perfetto用|分隔
                    if len(parts) >= 6:
                        data.append([p.strip() for p in parts[:6]])
                
                if data:
                    # 创建DataFrame
                    qr_df = pd.DataFrame(data, columns=['id', 'dur', 'name', 'depth', 'thread_dur', 'ts'])
                    
                    # 转换数值列
                    for col in ['id', 'dur', 'depth', 'thread_dur', 'ts']:
                        qr_df[col] = pd.to_numeric(qr_df[col], errors='coerce')
                    
                    # 保存结果 - 按照原始notebook格式
                    output_path = './query_result.txt'
                    qr_df.to_csv(output_path, sep='\t', index=False)
                    
                    print(f"✅ Query result saved to {output_path}")
                    print(f"数据行数: {len(qr_df)}")
                    return qr_df
                else:
                    print("❌ 无法解析数据")
                    return None
            else:
                print("❌ 查询返回空结果")
                return None
        else:
            print(f"❌ 查询失败: {result.stderr}")
            return None
            
    except Exception as e:
        print(f"❌ 执行查询时出错: {e}")
        return None
    finally:
        if os.path.exists(sql_file):
            os.remove(sql_file)

def filter_short_frames(df, threshold_ms=16.66):
    """
    过滤持续时间小于指定阈值的帧 - 完全按照原始notebook逻辑
    """
    print(f"🔄 过滤短于 {threshold_ms}ms 的帧...")
    
    df = df.copy()
    threshold_ns = threshold_ms * 1_000_000 

    current_group = -1
    groups = []
    for depth in df['depth']:
        if depth == 0:
            current_group += 1
        groups.append(current_group)
    df['_group'] = groups
    
    try:
        group_first = df.groupby('_group').first()
        valid_groups = group_first[group_first['dur'] >= threshold_ns].index
        result_df = df[df['_group'].isin(valid_groups)].drop(columns='_group')
    finally:
        if '_group' in df.columns:
            df.drop(columns='_group', inplace=True)
    
    print(f"✅ 过滤后剩余: {len(result_df)} 行")
    return result_df.reset_index(drop=True)

def filter_dur(df, threshold=0.02):
    """小于threshold ms duration的日志信息都cut掉 - 按照原始notebook"""
    print(f"🔄 过滤短于 {threshold}ms 的持续时间...")
    result_df = df[df['dur'] >= threshold * 1e6]
    print(f"✅ 过滤后剩余: {len(result_df)} 行")
    return result_df

def split_into_blocks_and_save(df, path, chunk_size=1700):
    """拆分符合到模型的上下文 - 完全按照原始notebook逻辑"""
    print(f"🔄 拆分数据块，chunk_size={chunk_size}...")
    
    path = path if path.endswith("/") else path + "/"
    os.makedirs(path, exist_ok=True)

    # 找到所有 depth=0 的行的索引
    zero_depth_indices = df[df['depth'] == 0].index.tolist()

    if not zero_depth_indices:
        print("未找到 depth=0 的行，无需分割。")
        return []

    # 分割原始块
    original_blocks = []
    num_blocks = len(zero_depth_indices)
    for i in range(num_blocks):
        start_idx = zero_depth_indices[i]
        end_idx = zero_depth_indices[i+1]-1 if i < num_blocks-1 else df.index[-1]
        original_blocks.append(df.loc[start_idx:end_idx])

    # 合并处理逻辑
    merged_blocks = []
    current_chunks = []
    current_rows = 0

    for chunk in original_blocks:
        chunk_rows = len(chunk)

        if current_rows + chunk_rows >= chunk_size:
            if current_chunks:
                merged = pd.concat(current_chunks)
                merged_blocks.append(merged)
                current_chunks = []
                current_rows = 0

            if chunk_rows >= chunk_size:
                merged_blocks.append(chunk)
            else:
                current_chunks.append(chunk)
                current_rows += chunk_rows
        else:
            current_chunks.append(chunk)
            current_rows += chunk_rows

    if current_chunks:
        merged = pd.concat(current_chunks)
        merged_blocks.append(merged)

    # 保存处理 + 收集所有分块
    parent_block_num = 1
    blocks_to_return = []  # 用于收集所有分块的 DataFrame

    for parent_block in merged_blocks:
        total_rows = len(parent_block)

        if total_rows > chunk_size:
            num_sub_blocks = (total_rows - 1) // chunk_size + 1

            for sub_idx in range(num_sub_blocks):
                start = sub_idx * chunk_size
                end = start + chunk_size
                sub_block = parent_block.iloc[start:end]

                filename = f"{path}block_{parent_block_num}_{sub_idx+1}.txt"
                sub_block.to_csv(
                    filename,
                    sep='\t',
                    index=False,
                    header=True,
                    columns=['id', 'dur', 'name', 'depth', 'thread_dur', 'ts']
                )
                print(f"切分保存: {filename} (原块{parent_block_num}，行数: {len(sub_block)})")
                blocks_to_return.append(sub_block)  # 收集子块
        else:
            filename = f"{path}block_{parent_block_num}.txt"
            parent_block.to_csv(
                filename,
                sep='\t',
                index=False,
                header=True,
                columns=['id', 'dur', 'name', 'depth', 'thread_dur', 'ts']
            )
            print(f"直接保存: {filename} (行数: {total_rows})")
            blocks_to_return.append(parent_block)  # 收集主块

        parent_block_num += 1

    print(f"✅ 生成了 {len(blocks_to_return)} 个数据块")
    return blocks_to_return  # 返回所有分块的 DataFrame 列表

def step2_extract_key_info(dfs):
    """步骤2: 提取关键日志信息 - 按照原始notebook逻辑"""
    print("\n=== 步骤2: 提取关键日志信息 ===")
    
    # 初始化 OpenAI 客户端 - 按照原始notebook配置
    client = OpenAI(
        base_url="https://wanqing-api.staging.kuaishou.com/api/agent/v1/apps",
        api_key=API_KEY_QWEN
    )
    
    # 用于保存结构化结果
    results = []
    # 用于保存所有模型响应的文本
    all_responses = []
    
    os.makedirs("./log_anaysis", exist_ok=True)
    
    for df_index, df in enumerate(dfs):
        print(f"\nProcessing DataFrame {df_index}")
        
        # 将DataFrame转换为字符串输入
        input_content = df.to_string(index=False)
        
        try:
            response_text = ""
            stream = client.chat.completions.create(
                model="app-msvz3y-1748316514060935132",  # 原始notebook中的模型ID
                messages=[{"role": "user", "content": input_content}],
                stream=True,
                extra_body={"enable_thinking": False}
            )

            for chunk in stream:
                if chunk.choices and chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    response_text += content

            # 保存响应
            output_file = f"./log_anaysis/response_df{df_index}.txt"
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(response_text)
            
            all_responses.append(response_text)
            print(f"✅ 保存分析结果: {output_file}")
            
        except Exception as e:
            print(f"❌ 处理 DataFrame {df_index} 时出错: {e}")
    
    print("✅ 关键信息提取完成")
    return all_responses

def main():
    """主流程 - 完全按照原始notebook的步骤"""
    print("=== RAG+Qwen 命令行版本 ===")
    print("按照原始notebook逻辑处理trace数据\n")
    
    # 步骤1: SQL提取信息
    qr_df = step1_sql_extract()
    if qr_df is None:
        print("❌ SQL提取失败，退出")
        return
    
    # 步骤2: 切分文件，小于16.66ms直接cut
    print("\n=== 步骤2: 切分文件，小于16.66ms直接cut ===")
    filtered_df = filter_short_frames(qr_df)
    
    # 步骤3: 小于0.2ms duration的日志信息都cut掉 (原始notebook用的是0.2)
    print("\n=== 步骤3: 小于0.2ms duration的日志信息都cut掉 ===")
    filtered_df = filter_dur(filtered_df, threshold=0.2)
    
    # 步骤4: 拆分符合到模型的上下文
    print("\n=== 步骤4: 拆分符合到模型的上下文 ===")
    dfs = split_into_blocks_and_save(filtered_df, path="./chunk3/")
    
    # 步骤5: 提取关键日志信息
    all_responses = step2_extract_key_info(dfs)
    
    print(f"\n🎉 RAG+Qwen处理完成！")
    print(f"生成了 {len(dfs)} 个数据块")
    print(f"生成了 {len(all_responses)} 个AI分析结果")
    print("\n接下来可以运行后续步骤:")
    print("- 代码搜索")
    print("- 结果合并")
    print("- 最终分析")

if __name__ == "__main__":
    main()
