#!/usr/bin/env python3
"""
按照rag+qwen.ipynb的逻辑，使用命令行调用perfetto处理数据
完全复制原始notebook的处理流程
"""

import os
import subprocess
import pandas as pd
import re
from openai import OpenAI
from kp import KwaiPilot

# 配置 - 从原始notebook复制
API_KEY_QWEN = "4kqyobx9g9jjixgk0xaqool9ldkc5e83qxl7"
API_KEY_ANALYSIS = "ofdvapet0gfvdtnpdbr5pffd2qvn44sxt32h"
API_KEY_SUMMARY = "ofdvapet0gfvdtnpdbr5pffd2qvn44sxt32h"
KWAI_PILOT_TOKEN = "DDKmVYBIF2P9aN"
KWAI_PILOT_USER = "lvaolin"

def step1_sql_extract():
    """步骤1: SQL提取信息 - 使用命令行perfetto"""
    print("=== 步骤1: SQL提取信息 ===")
    
    # 原始SQL查询
    sql_query = '''
    select id, dur, name, depth, parent_id, thread_dur, ts 
    from slice 
    where track_id in (11) 
    order by ts asc, depth asc
    '''
    
    # 写入SQL文件
    sql_file = 'extract_query.sql'
    with open(sql_file, 'w') as f:
        f.write(sql_query)
    
    try:
        # 使用命令行工具执行查询
        shell_path = './mac-arm64/trace_processor_shell'
        cmd = [shell_path, '--query-file', sql_file, 'trace.perfetto-trace']

        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)

        print("原始输出:")
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)

        if result.returncode == 0:
            # 查找实际的数据行
            lines = result.stdout.strip().split('\n')
            data_lines = []

            # 寻找数据开始的位置（跳过加载信息和错误统计）
            data_started = False
            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 跳过加载信息和错误统计
                if ('Loading trace:' in line or
                    'Trace loaded:' in line or
                    'Error stats' in line or
                    'name' in line and 'idx' in line and 'source' in line or
                    '----' in line or
                    'systrace_parse_failure' in line or
                    'trace_sorter_negative_timestamp_dropped' in line):
                    continue

                # 检查是否是数据行（包含数字和逗号分隔的格式）
                if ',' in line or '|' in line:
                    # 尝试解析为数据行
                    if '|' in line:
                        parts = [p.strip() for p in line.split('|')]
                    else:
                        parts = [p.strip() for p in line.split(',')]

                    if len(parts) >= 6:
                        try:
                            # 验证第一列是否为数字（id）
                            int(parts[0])
                            data_lines.append(parts[:6])
                        except:
                            continue

            if data_lines:
                # 创建DataFrame
                qr_df = pd.DataFrame(data_lines, columns=['id', 'dur', 'name', 'depth', 'parent_id', 'thread_dur', 'ts'])

                # 转换数值列
                for col in ['id', 'dur', 'depth', 'parent_id', 'thread_dur', 'ts']:
                    qr_df[col] = pd.to_numeric(qr_df[col], errors='coerce')

                # 保存结果 - 按照原始notebook格式
                output_path = './query_result.txt'
                qr_df.to_csv(output_path, sep='\t', index=False)

                print(f"✅ Query result saved to {output_path}")
                print(f"数据行数: {len(qr_df)}")
                return qr_df
            else:
                print("❌ 未找到有效数据行")
                return None
        else:
            print(f"❌ 查询失败，返回码: {result.returncode}")
            return None

    except Exception as e:
        print(f"❌ 执行查询时出错: {e}")
        return None
    finally:
        # 保留SQL文件以便调试
        pass

def filter_short_frames(df, threshold_ms=16.66):
    """
    过滤持续时间小于指定阈值的帧 - 完全按照原始notebook逻辑
    """
    print(f"🔄 过滤短于 {threshold_ms}ms 的帧...")
    
    df = df.copy()
    threshold_ns = threshold_ms * 1_000_000 

    current_group = -1
    groups = []
    for depth in df['depth']:
        if depth == 0:
            current_group += 1
        groups.append(current_group)
    df['_group'] = groups
    
    try:
        group_first = df.groupby('_group').first()
        valid_groups = group_first[group_first['dur'] >= threshold_ns].index
        result_df = df[df['_group'].isin(valid_groups)].drop(columns='_group')
    finally:
        if '_group' in df.columns:
            df.drop(columns='_group', inplace=True)
    
    print(f"✅ 过滤后剩余: {len(result_df)} 行")
    return result_df.reset_index(drop=True)

def filter_dur(df, threshold=0.2):
    """小于threshold ms duration的日志信息都cut掉 - 按照原始notebook"""
    print(f"🔄 过滤短于 {threshold}ms 的持续时间...")
    result_df = df[df['dur'] >= threshold * 1e6]
    print(f"✅ 过滤后剩余: {len(result_df)} 行")
    return result_df

def split_into_blocks_and_save(df, path, chunk_size=1700):
    """拆分符合到模型的上下文 - 完全按照原始notebook逻辑"""
    print(f"🔄 拆分数据块，chunk_size={chunk_size}...")
    
    path = path if path.endswith("/") else path + "/"
    os.makedirs(path, exist_ok=True)

    # 找到所有 depth=0 的行的索引
    zero_depth_indices = df[df['depth'] == 0].index.tolist()

    if not zero_depth_indices:
        print("未找到 depth=0 的行，无需分割。")
        return []

    # 分割原始块
    original_blocks = []
    num_blocks = len(zero_depth_indices)
    for i in range(num_blocks):
        start_idx = zero_depth_indices[i]
        end_idx = zero_depth_indices[i+1]-1 if i < num_blocks-1 else df.index[-1]
        original_blocks.append(df.loc[start_idx:end_idx])

    # 合并处理逻辑
    merged_blocks = []
    current_chunks = []
    current_rows = 0

    for chunk in original_blocks:
        chunk_rows = len(chunk)

        if current_rows + chunk_rows >= chunk_size:
            if current_chunks:
                merged = pd.concat(current_chunks)
                merged_blocks.append(merged)
                current_chunks = []
                current_rows = 0

            if chunk_rows >= chunk_size:
                merged_blocks.append(chunk)
            else:
                current_chunks.append(chunk)
                current_rows += chunk_rows
        else:
            current_chunks.append(chunk)
            current_rows += chunk_rows

    if current_chunks:
        merged = pd.concat(current_chunks)
        merged_blocks.append(merged)

    # 保存处理 + 收集所有分块
    parent_block_num = 1
    blocks_to_return = []  # 用于收集所有分块的 DataFrame

    for parent_block in merged_blocks:
        total_rows = len(parent_block)

        if total_rows > chunk_size:
            num_sub_blocks = (total_rows - 1) // chunk_size + 1

            # for sub_idx in range(num_sub_blocks):
            #     start = sub_idx * chunk_size
            #     end = start + chunk_size
            #     sub_block = parent_block.iloc[start:end]
            #
            #     filename = f"{path}block_{parent_block_num}_{sub_idx+1}.txt"
            #     sub_block.to_csv(
            #         filename,
            #         sep='\t',
            #         index=False,
            #         header=True,
            #         columns=['id', 'dur', 'name', 'depth', 'thread_dur', 'ts']
            #     )
            #     print(f"切分保存: {filename} (原块{parent_block_num}，行数: {len(sub_block)})")
            #     blocks_to_return.append(sub_block)  # 收集子块
            from test_split_tree_blocks import split_tree_blocks_with_parents
            blocks = split_tree_blocks_with_parents(parent_block, chunk_size=chunk_size)
            for block in blocks:
                filename = f"{path}block_{parent_block_num}.txt"
                block.to_csv(
                    filename,
                    sep='\t',
                    index=False,
                    header=True,
                    columns=['id', 'dur', 'name', 'depth', 'thread_dur', 'ts']
                )
                print(f"切分保存: {filename} (原块{parent_block_num}，行数: {len(block)})")
                blocks_to_return.append(block)  # 收集子块

        else:
            filename = f"{path}block_{parent_block_num}.txt"
            parent_block.to_csv(
                filename,
                sep='\t',
                index=False,
                header=True,
                columns=['id', 'dur', 'name', 'depth', 'thread_dur', 'ts']
            )
            print(f"直接保存: {filename} (行数: {total_rows})")
            blocks_to_return.append(parent_block)  # 收集主块

        parent_block_num += 1

    print(f"✅ 生成了 {len(blocks_to_return)} 个数据块")
    return blocks_to_return  # 返回所有分块的 DataFrame 列表


def get_completed_analyses():
    """检查已完成的分析文件"""
    completed = []
    analysis_dir = "./log_anaysis"

    if not os.path.exists(analysis_dir):
        return completed

    # 检查所有可能的分析文件
    for filename in os.listdir(analysis_dir):
        if filename.startswith("response_df") and filename.endswith(".txt"):
            # 提取数字索引
            match = re.search(r'response_df(\d+)\.txt', filename)
            if match:
                index = int(match.group(1))
                # 检查文件是否非空
                filepath = os.path.join(analysis_dir, filename)
                if os.path.getsize(filepath) > 0:
                    completed.append(index)

    return sorted(completed)

def step2_extract_key_info(dfs, resume=True):
    """步骤2: 提取关键日志信息 - 支持断点续传"""
    print("\n=== 步骤2: 提取关键日志信息 ===")

    # 检查断点续传
    completed_indices = []
    if resume:
        completed_indices = get_completed_analyses()
        if completed_indices:
            print(f"🔄 发现已完成的分析: {completed_indices}")
            print(f"📊 总共 {len(dfs)} 个数据块，已完成 {len(completed_indices)} 个")

    # 初始化 OpenAI 客户端 - 按照原始notebook配置
    client = OpenAI(
        base_url="https://wanqing-api.staging.kuaishou.com/api/agent/v1/apps",
        api_key=API_KEY_QWEN
    )

    # 用于保存所有模型响应的文本
    all_responses = []

    os.makedirs("./log_anaysis", exist_ok=True)

    # 加载已完成的分析结果
    for completed_idx in completed_indices:
        if completed_idx < len(dfs):
            output_file = f"./log_anaysis/response_df{completed_idx}.txt"
            try:
                with open(output_file, "r", encoding="utf-8") as f:
                    response_text = f.read()
                    all_responses.append(response_text)
                print(f"📁 加载已完成的分析: {output_file}")
            except Exception as e:
                print(f"⚠️ 加载 {output_file} 失败: {e}")

    # 处理剩余的数据块
    remaining_count = 0
    for df_index, df in enumerate(dfs):
        if df_index in completed_indices:
            continue  # 跳过已完成的

        remaining_count += 1
        print(f"\n🔄 Processing DataFrame {df_index} (剩余 {len(dfs) - len(completed_indices) - remaining_count + 1} 个)")

        # 将DataFrame转换为字符串输入
        input_content = df.to_string(index=False)

        try:
            response_text = ""
            stream = client.chat.completions.create(
                model="app-msvz3y-1748316514060935132",  # 原始notebook中的模型ID
                messages=[{"role": "user", "content": input_content}],
                stream=True,
                extra_body={"enable_thinking": False}
            )

            for chunk in stream:
                if chunk.choices and chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    response_text += content

            # 保存响应
            output_file = f"./log_anaysis/response_df{df_index}.txt"
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(response_text)

            all_responses.append(response_text)
            print(f"✅ 保存分析结果: {output_file}")

        except Exception as e:
            print(f"❌ 处理 DataFrame {df_index} 时出错: {e}")
            print(f"💡 可以重新运行程序从此处继续")
            break  # 遇到错误时停止，但保留已完成的进度

    print(f"\n✅ 关键信息提取完成")
    print(f"📊 总共处理了 {len(all_responses)} 个数据块")
    return all_responses

def step3_code_search(folder_path="./log_anaysis", output_folder="./code"):
    """步骤3: 调用code search接口 - 按照原始notebook逻辑"""
    print("\n=== 步骤3: 调用code search接口 ===")

    os.makedirs(output_folder, exist_ok=True)
    pilot = KwaiPilot(token=KWAI_PILOT_TOKEN, user_name=KWAI_PILOT_USER)
    results = []

    pattern = re.compile(r'<think>.*?</think>', re.DOTALL)

    # 获取所有txt文件并排序
    txt_files = [f for f in os.listdir(folder_path) if f.endswith(".txt")]
    txt_files.sort()

    for filename in txt_files:
        print(f"🔍 处理文件: {filename}")
        file_path = os.path.join(folder_path, filename)

        with open(file_path, 'r', encoding='utf-8') as f:
            raw_content = f.read().strip()
            clean_query = pattern.sub('', raw_content).strip()
            query = clean_query if clean_query else raw_content

        try:
            answer = pilot.codeSearch(query=query)
            print(f"✅ 代码搜索成功: {filename}")
        except Exception as e:
            answer = f"Error: {str(e)}"
            print(f"❌ 代码搜索失败: {filename} - {e}")

        output_filename = f"result_{filename}"
        output_path = os.path.join(output_folder, output_filename)

        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(answer)

        results.append({
            "filename": filename,
            "processed_query": query,
            "answer": answer
        })

    print(f"✅ 代码搜索完成，生成了 {len(results)} 个结果文件")
    return pd.DataFrame(results)

def step4_rag_combine_chunks(folder1="./log_anaysis", folder2="./code", max_tokens=92000, output_folder="./truncated_results"):
    """步骤4: RAG结果拼接分块信息 - 按照原始notebook逻辑"""
    print("\n=== 步骤4: RAG结果拼接分块信息 ===")

    try:
        from transformers import AutoTokenizer
        tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen3-235B-A22B", trust_remote_code=True)
        print("✅ 成功加载Qwen tokenizer")
    except Exception as e:
        print(f"❌ 加载tokenizer失败: {e}")
        print("💡 使用简单的字符计数作为替代")
        tokenizer = None

    def read_file(file_path):
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()

    def truncate_text2_to_fit(text1, text2, max_tokens, tokenizer):
        if tokenizer is None:
            # 简单的字符计数替代方案
            max_chars = max_tokens * 4  # 粗略估计
            prefix = text1 + '\n'
            remaining_chars = max_chars - len(prefix)
            if remaining_chars <= 0:
                return ""
            return text2[:remaining_chars]

        # 计算固定前缀（text1 + 换行符）的token数
        prefix = text1 + '\n'
        prefix_tokens = len(tokenizer.encode(prefix, add_special_tokens=False))

        # 计算剩余可用token数
        remaining_tokens = max_tokens - prefix_tokens
        if remaining_tokens <= 0:
            return ""  # 无可用空间返回空字符串

        # 对text2进行编码并截断
        text2_tokens = tokenizer.encode(text2, add_special_tokens=False)
        allowed_tokens = text2_tokens[:remaining_tokens]

        # 解码回文本并清理特殊符号
        return tokenizer.decode(
            allowed_tokens,
            skip_special_tokens=True,
            clean_up_tokenization_spaces=True
        )

    def natural_sort_key(s):
        return [int(part) if part.isdigit() else part.lower() for part in re.split(r'(\d+)', s)]

    # 确保输出文件夹存在
    os.makedirs(output_folder, exist_ok=True)

    # 获取排序后的文件列表
    files1 = sorted((f for f in os.listdir(folder1) if f.endswith('.txt')), key=natural_sort_key)
    files2 = sorted((f for f in os.listdir(folder2) if f.endswith('.txt')), key=natural_sort_key)

    if len(files1) != len(files2):
        print(f"⚠️ 文件数量不匹配: {folder1}({len(files1)}) vs {folder2}({len(files2)})")
        # 取较小的数量
        min_len = min(len(files1), len(files2))
        files1 = files1[:min_len]
        files2 = files2[:min_len]

    # 处理每对文件
    processed_count = 0
    for f1, f2 in zip(files1, files2):
        path1 = os.path.join(folder1, f1)
        path2 = os.path.join(folder2, f2)
        print(f"🔄 处理文件对: {f1} + {f2}")

        try:
            # 读取文件内容
            text1 = read_file(path1)
            text2 = read_file(path2)

            # 计算初始token数
            if tokenizer:
                prefix = text1 + '\n'
                prefix_tokens = len(tokenizer.encode(prefix, add_special_tokens=False))
                text2_tokens = len(tokenizer.encode(text2, add_special_tokens=False))
                total_tokens = prefix_tokens + text2_tokens
                print(f"  初始token数: {total_tokens}")
            else:
                total_tokens = len(text1) + len(text2)
                print(f"  初始字符数: {total_tokens}")

            # 准备输出路径
            output_path = os.path.join(output_folder, "truncated_" + f1)

            if total_tokens > max_tokens:
                # 执行截断处理
                truncated_text2 = truncate_text2_to_fit(text1, text2, max_tokens, tokenizer)

                # 保存裁剪后的内容
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(text1 + '\n' + truncated_text2)

                if tokenizer:
                    final_tokens = len(tokenizer.encode(text1 + '\n' + truncated_text2, add_special_tokens=False))
                    print(f"  ✂️ 已裁剪: {total_tokens} → {final_tokens} tokens")
                else:
                    print(f"  ✂️ 已裁剪文本")
            else:
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(text1 + '\n' + text2)
                print(f"  ✅ 无需裁剪")

            processed_count += 1

        except Exception as e:
            print(f"❌ 处理文件对 {f1}, {f2} 时出错: {str(e)}")

    print(f"✅ RAG结果拼接完成，处理了 {processed_count} 个文件对")
    return processed_count

def step5_qwen3_analysis(api_key="ofdvapet0gfvdtnpdbr5pffd2qvn44sxt32h", input_folder="./truncated_results", output_folder="./result"):
    """步骤5: 调用Qwen3测试code+火焰图 - 按照原始notebook逻辑"""
    print("\n=== 步骤5: 调用Qwen3测试code+火焰图 ===")

    # 确保输出文件夹存在
    os.makedirs(output_folder, exist_ok=True)

    # 初始化 OpenAI 客户端
    client = OpenAI(
        base_url="https://wanqing-api.staging.kuaishou.com/api/agent/v1/apps",
        api_key=api_key
    )

    # 获取所有txt文件并排序
    txt_files = [f for f in os.listdir(input_folder) if f.endswith('.txt')]
    txt_files.sort()

    processed_count = 0

    # 遍历输入文件夹中的所有 .txt 文件
    for filename in txt_files:
        print(f"🤖 处理文件: {filename}")
        input_path = os.path.join(input_folder, filename)

        try:
            # 读取文件内容
            with open(input_path, 'r', encoding='utf-8') as file:
                combined_text = file.read()

            # 发送流式请求
            stream = client.chat.completions.create(
                model="app-ad7yvp-1748252835514881827",
                messages=[{"role": "user", "content": combined_text}],
                stream=True,
            )

            # 收集响应内容
            response_content = ""
            for chunk in stream:
                if chunk.choices:
                    content = chunk.choices[0].delta.content
                    if content:
                        response_content += content

            # 构建输出文件路径
            base_name = os.path.splitext(filename)[0]
            output_path = os.path.join(output_folder, f"{base_name}.md")

            # 保存结果到 .md 文件
            with open(output_path, 'w', encoding='utf-8') as file:
                file.write(response_content)

            print(f"✅ 成功处理文件: {filename}")
            processed_count += 1

        except Exception as e:
            print(f"❌ 处理文件 {filename} 时出错: {e}")

    print(f"✅ Qwen3分析完成，处理了 {processed_count} 个文件")
    return processed_count

def step6_merge_results(folder_path="./result", output_path="./result.md"):
    """步骤6: 合并结果 - 按照原始notebook逻辑"""
    print("\n=== 步骤6: 合并结果 ===")

    output_dir = os.path.dirname(output_path)
    os.makedirs(output_dir, exist_ok=True)

    results = []
    pattern = re.compile(r'<think>.*?</think>', re.DOTALL)

    # 获取所有md文件并排序
    md_files = [f for f in os.listdir(folder_path) if f.endswith(".md")]
    md_files.sort()

    for filename in md_files:
        print(f"📄 合并文件: {filename}")
        file_path = os.path.join(folder_path, filename)

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                raw_content = f.read().strip()

                clean_content = pattern.sub('', raw_content).strip()

                if clean_content:
                    results.append(f"<!-- From {filename} -->\n{clean_content}")
        except Exception as e:
            print(f"❌ 读取文件 {filename} 失败: {e}")

    # 写入合并结果
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write('\n\n'.join(results))

    print(f"✅ 合并完成: {output_path}")
    print(f"📊 合并了 {len(results)} 个文件")
    return len(results)

def step7_final_analysis(api_key="ofdvapet0gfvdtnpdbr5pffd2qvn44sxt32h", input_path="./result.md", output_path="./result_table.md"):
    """步骤7: 最终分析 - 按照原始notebook逻辑"""
    print("\n=== 步骤7: 最终分析 ===")

    # 初始化 OpenAI 客户端
    client = OpenAI(
        base_url="https://wanqing-api.staging.kuaishou.com/api/agent/v1/apps",
        api_key=api_key
    )

    try:
        # 读取合并后的内容
        with open(input_path, "r", encoding='utf-8') as f:
            md_content = f.read()

        print("🤖 发送最终分析请求...")

        # Streaming请求
        stream = client.chat.completions.create(
            model="app-4kdpqh-1748337711643805362",
            messages=[
                {"role": "user", "content": md_content},
            ],
            stream=True,
        )

        # 收集响应内容
        response_content = ""
        for chunk in stream:
            if chunk.choices:
                content = chunk.choices[0].delta.content
                if content:
                    response_content += content

        # 保存结果到 .md 文件
        with open(output_path, 'w', encoding='utf-8') as file:
            file.write(response_content)

        print(f"✅ 最终分析完成: {output_path}")

        # 清理<think>标签
        pattern = re.compile(r'<think>.*?</think>', re.DOTALL)
        cleaned_content = pattern.sub('', response_content)

        # 将处理后的内容写回文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(cleaned_content.strip())

        print("✅ 清理<think>标签完成")
        return True

    except Exception as e:
        print(f"❌ 最终分析失败: {e}")
        return False

def check_existing_data():
    """检查现有数据，确定从哪个步骤开始"""
    steps_status = {
        'sql_extract': os.path.exists('./query_result.txt'),
        'chunks': os.path.exists('./chunk3/') and len([f for f in os.listdir('./chunk3/') if f.startswith('block_')]) > 0,
        'analysis': os.path.exists('./log_anaysis/') and len([f for f in os.listdir('./log_anaysis/') if f.startswith('response_df')]) > 0
    }
    return steps_status

def load_existing_chunks():
    """加载已存在的数据块"""
    chunk_dir = './chunk3/'
    if not os.path.exists(chunk_dir):
        return None

    chunk_files = [f for f in os.listdir(chunk_dir) if f.startswith('block_') and f.endswith('.txt')]
    if not chunk_files:
        return None

    # 按文件名排序
    chunk_files.sort()
    dfs = []

    for chunk_file in chunk_files:
        try:
            filepath = os.path.join(chunk_dir, chunk_file)
            df = pd.read_csv(filepath, sep='\t')
            dfs.append(df)
            print(f"📁 加载数据块: {chunk_file} ({len(df)} 行)")
        except Exception as e:
            print(f"⚠️ 加载 {chunk_file} 失败: {e}")

    return dfs if dfs else None

def main():
    """主流程 - 支持断点续传的智能处理"""
    print("=== RAG+Qwen 命令行版本 (支持断点续传) ===")
    print("按照原始notebook逻辑处理trace数据\n")

    # 检查现有数据状态
    status = check_existing_data()
    print("📊 检查现有数据状态:")
    print(f"  - SQL提取: {'✅' if status['sql_extract'] else '❌'}")
    print(f"  - 数据分块: {'✅' if status['chunks'] else '❌'}")
    print(f"  - AI分析: {'✅' if status['analysis'] else '❌'}")

    qr_df = None
    dfs = None

    # 步骤1: SQL提取信息
    if status['sql_extract']:
        print("\n🔄 发现已有SQL提取结果，直接加载...")
        try:
            qr_df = pd.read_csv('./query_result.txt', sep='\t')
            print(f"✅ 加载SQL结果: {len(qr_df)} 行")
        except Exception as e:
            print(f"⚠️ 加载SQL结果失败: {e}，重新提取...")
            status['sql_extract'] = False

    if not status['sql_extract']:
        print("\n=== 步骤1: SQL提取信息 ===")
        qr_df = step1_sql_extract()
        if qr_df is None:
            print("❌ SQL提取失败，退出")
            return

    # 步骤2-4: 数据处理和分块
    if status['chunks']:
        print("\n🔄 发现已有数据分块，直接加载...")
        dfs = load_existing_chunks()
        if dfs:
            print(f"✅ 加载了 {len(dfs)} 个数据块")
        else:
            print("⚠️ 加载数据块失败，重新处理...")
            status['chunks'] = False

    if not status['chunks']:
        print("\n=== 步骤2: 切分文件，小于16.66ms直接cut ===")
        filtered_df = filter_short_frames(qr_df)

        print("\n=== 步骤3: 小于0.2ms duration的日志信息都cut掉 ===")
        filtered_df = filter_dur(filtered_df, threshold=0.2)

        print("\n=== 步骤4: 拆分符合到模型的上下文 ===")
        dfs = split_into_blocks_and_save(filtered_df, path="./chunk3/")

    # 步骤5: 提取关键日志信息 (支持断点续传)
    all_responses = step2_extract_key_info(dfs, resume=True)

    print(f"\n🎉 RAG+Qwen处理完成！")
    print(f"📊 生成了 {len(dfs)} 个数据块")
    print(f"📊 生成了 {len(all_responses)} 个AI分析结果")

    # 检查是否全部完成
    completed_analyses = get_completed_analyses()
    if len(completed_analyses) == len(dfs):
        print("✅ 所有数据块分析完成！")

        # 询问是否继续后续步骤
        print("\n🤔 是否继续执行后续步骤？")
        print("后续步骤包括:")
        print("  1. 代码搜索")
        print("  2. RAG结果拼接")
        print("  3. Qwen3分析")
        print("  4. 合并结果")
        print("  5. 最终分析")

        user_input = input("\n是否继续？(y/n): ").lower().strip()

        if user_input in ['y', 'yes', '是', '继续']:
            print("\n🚀 开始执行后续步骤...")

            # 步骤6: 代码搜索
            try:
                step3_code_search()
            except Exception as e:
                print(f"❌ 代码搜索失败: {e}")
                return

            # 步骤7: RAG结果拼接
            try:
                step4_rag_combine_chunks()
            except Exception as e:
                print(f"❌ RAG结果拼接失败: {e}")
                return

            # 步骤8: Qwen3分析
            try:
                step5_qwen3_analysis()
            except Exception as e:
                print(f"❌ Qwen3分析失败: {e}")
                return

            # 步骤9: 合并结果
            try:
                step6_merge_results()
            except Exception as e:
                print(f"❌ 合并结果失败: {e}")
                return

            # 步骤10: 最终分析
            try:
                step7_final_analysis()
                print("\n🎉🎉🎉 完整的RAG+Qwen流程执行完成！")
                print("📄 最终结果保存在: ./result_table.md")
            except Exception as e:
                print(f"❌ 最终分析失败: {e}")
                return
        else:
            print("\n⏸️ 已停止，您可以稍后手动执行后续步骤")
            print("💡 提示：重新运行程序会自动跳过已完成的步骤")
    else:
        print(f"⚠️ 还有 {len(dfs) - len(completed_analyses)} 个数据块未完成")
        print("💡 可以重新运行程序继续处理剩余部分")

def run_post_processing():
    """独立运行后续处理步骤"""
    print("=== RAG+Qwen 后续处理步骤 ===")
    print("执行代码搜索、结果合并和最终分析\n")

    try:
        # 步骤1: 代码搜索
        print("🔍 步骤1: 代码搜索")
        step3_code_search()

        # 步骤2: RAG结果拼接
        print("\n🔗 步骤2: RAG结果拼接")
        step4_rag_combine_chunks()

        # 步骤3: Qwen3分析
        print("\n🤖 步骤3: Qwen3分析")
        step5_qwen3_analysis()

        # 步骤4: 合并结果
        print("\n📋 步骤4: 合并结果")
        step6_merge_results()

        # 步骤5: 最终分析
        print("\n🎯 步骤5: 最终分析")
        step7_final_analysis()

        print("\n🎉🎉🎉 后续处理完成！")
        print("📄 最终结果保存在: ./result_table.md")

    except Exception as e:
        print(f"❌ 后续处理失败: {e}")

def show_usage():
    """显示使用说明"""
    print("=== RAG+Qwen 命令行工具使用说明 ===")
    print()
    print("用法:")
    print("  python3 rag_qwen_cmdline.py           # 运行完整流程")
    print("  python3 rag_qwen_cmdline.py --post    # 只运行后续处理步骤")
    print("  python3 rag_qwen_cmdline.py --help    # 显示此帮助信息")
    print()
    print("完整流程包括:")
    print("  1. SQL提取信息")
    print("  2. 数据过滤和分块")
    print("  3. AI关键信息提取")
    print("  4. 代码搜索")
    print("  5. RAG结果拼接")
    print("  6. Qwen3分析")
    print("  7. 合并结果")
    print("  8. 最终分析")
    print()
    print("特性:")
    print("  ✅ 支持断点续传")
    print("  ✅ 智能跳过已完成步骤")
    print("  ✅ 详细进度显示")
    print("  ✅ 错误恢复能力")
    print()

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "--post":
            # 只运行后续处理步骤
            run_post_processing()
        elif sys.argv[1] in ["--help", "-h", "help"]:
            # 显示帮助信息
            show_usage()
        else:
            print(f"❌ 未知参数: {sys.argv[1]}")
            show_usage()
    else:
        # 运行完整流程
        main()
