<think>好的，我现在需要处理用户提供的数据，并从中识别出关键代码片段。首先，我会仔细阅读任务描述和约束条件，确保自己完全理解用户的需求。任务要求根据输入数据中的id、持续时间、名称、深度和线程持续时间等字段，找出感兴趣的代码片段，以便后续在代码仓库中搜索并优化应用程序性能。输出需要是代码形式，尽量减少文字描述，且响应长度不超过1024个令牌。

首先，我需要分析用户提供的数据结构。看起来数据是一个表格，包含多个字段：id、dur、name、depth、thread_dur、ts。其中，name字段似乎包含了类名、方法名以及可能的其他信息，用“_I:”或“_T:”分隔。例如，“K_dispatchTouchEvent_I:KwaiGrootViewPager_T:KwaiGrootViewPager”这样的条目，可能表示某个类中的方法被调用，而“_I”可能代表实例方法，“_T”是类名或类型。

接下来，我需要确定哪些数据点值得关注。通常，性能优化关注耗时较长的操作，因此我会先检查dur列，寻找持续时间较长的条目。例如，数据中某些dur值达到数百万，如2087951406，这可能表明某些操作存在性能瓶颈。同时，depth字段可能表示调用栈的深度，较深的调用可能涉及复杂的嵌套调用，也可能需要关注。

观察数据中的高dur值，比如id为195007的行，dur是2087951406，这可能是一个非常耗时的操作。对应的name是“android.view.Choreographer$FrameHandler: android.view.Choreographer$FrameDisplayEventReceiver”，这通常与UI渲染相关，可能涉及帧处理或显示事件接收，属于系统级的性能监控点，可能是关键路径上的瓶颈。

另一个例子是id 195011，dur 408541198，name为“Choreographer#doFrame”，这通常处理每一帧的逻辑，如果耗时过长，可能导致掉帧，影响UI流畅度。还有id 195012的“Choreographer#skippedFrames”可能表示跳过的帧数，但这里dur也很大，可能存在问题。

接下来是input事件处理阶段的条目，比如id 195014的“input”和id 195016的“processInputEventForCompatibility”，这些可能与输入事件的处理有关，高dur值可能意味着输入处理延迟，影响用户体验。

查看ViewPostImeInputStage相关的条目，如id 195038和195043，dur分别为7979010和7842135，这属于输入阶段的处理，可能涉及IME（输入法编辑器）的事件处理，高延迟可能导致输入响应变慢。

再往下看，K_dispatchTouchEvent_I相关的条目（id 195063、195077等）的dur值较高，比如7, 8百万，这些方法负责处理触摸事件分发，如果耗时过长，可能导致UI卡顿或响应迟缓。

还有一些关于PagerAdapter的方法，如K_getRealPositionInAdapter_I（多次出现，dur从几十万到几百万），这可能与页面适配器的性能有关，尤其是在滑动或切换页面时的位置计算，耗时过长会影响滑动流畅度。

另外，关于KwaiMediaPlayerWrapper和KwaiMediaPlayerImplV3的调用，如K_setSpeed_I（id 197263, 197265等），这些可能与视频播放速度设置相关，高dur值可能影响播放控制的响应时间。

还有涉及LiveData和DispatchLiveData的notifyValueSet操作（如id 197494、197495），这些是数据绑定和UI更新的关键部分，如果频繁且耗时，可能导致UI更新延迟。

锁竞争（Lock contention）相关的条目，如id 198914和202481，这些可能表示线程同步问题，多线程竞争资源导致等待时间增加，进而影响整体性能。

此外，一些页面生命周期相关的方法，如K_becomesDetached_I和K_becomesAttached_I，涉及页面或元素的附加和分离，如果这些操作耗时，可能影响页面切换的流畅性。

现在需要综合这些信息，筛选出持续时间长、调用栈深、或者涉及关键路径的代码片段。例如：

- 高dur的Choreographer相关调用，如doFrame、skippedFrames。
- 多次出现的PagerAdapter中的位置获取方法，如K_getRealPositionInAdapter_I。
- 触摸事件分发和处理的方法，如K_dispatchTouchEvent_I、K_onTouchEvent_I。
- MediaPlayer相关操作，尤其是设置速度、播放状态的方法。
- LiveData的值通知和更新，如notifyValueSet。
- 锁竞争的条目，可能影响线程效率。
- 页面附加和分离的方法，如K_becomesAttached_I和K_becomesDetached_I。
- 数据绑定和UI更新相关的方法，如onBindData等。

需要将这些关键代码片段提取出来，按照用户的要求以代码形式呈现，不添加额外解释。同时注意响应长度限制，避免超出1024个令牌。可能还需要合并重复的条目，只保留不同的关键部分。

例如，对于多个K_dispatchTouchEvent_I的调用，可以统一列出，但可能需要根据不同的类或方法区分。同样，K_getRealPositionInAdapter_I出现在不同深度，可能属于不同层级的调用，需要单独列出。

最后，确保输出格式正确，没有多余的文字，只保留代码块。</think>```code
K_dispatchTouchEvent_I:KwaiGrootViewPager_T:KwaiGrootViewPager
K_onTouchEvent_I:GrootTouchViewPager_T:KwaiGrootViewPager
K_onPageScrolled_I:GrootViewPager$1_T:GrootViewPager$1
K_getRealPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel
K_getPositionInAdapter_I:PositionService_T:PositionService
K_setSpeed_I:KwaiMediaPlayerWrapper_T:PlayerKitQphotoSwitchPlayerImpl
K_setSpeed_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3
K_notifyValueSet_I:KDispatchLiveData_T:KDispatchLiveData
K_logEvent_I:LogManager_T:LogManager
Lock contention on thread list lock (owner tid: 27907)
K_onBecomesDetached_I:BaseGroup_T:SlidePage
K_performViewItemWillDisappear_I:GrootBaseFragment_T:NasaGrootDetailVMFragment
K_becomesDetachedOnPageSelectedListener_I:SlidePlayViewModel_T:SlidePlayViewModel
K_release_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter
K_put_I:PlayProgressPositionManager_T:PlayProgressPositionManager
K_stop_I:PlayerResumePauseController_T:PlayerResumePauseController
K_updateSpeed_I:SlidePlayerPanelViewModelImpl_T:SlidePlayerPanelViewModelImpl
K_getCurrentItemRealPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel
K_getAdapterLastValidItemPosition_I:SlidePlayViewModel_T:SlidePlayViewModel
K_logPlayInfo_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit
K_getPlayModuleInfo_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit
K_start_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3
K_notifyPlayerStateChange_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3
K_subscribeEvent_I:DanmakuElement_T:DanmakuElement
K_initKit_I:DanmakuElement_T:DanmakuElement
K_createDanmakuKit_I:BarrageKitWrapper_T:BarrageKitWrapper
K_onLayout_I:KwaiSlidingPaneLayout_T:KwaiSlidingPaneLayout
K_onMeasure_I:ScrollStrategyViewPager_T:ScrollStrategyViewPager
K_onMeasure_I:KwaiGrootViewPager_T:KwaiGrootViewPager
K_calculateElementRealShow_I:BaseGroup_T:FullScreenLayerGroup
K_notifyInformationPosition_I:InformationGroupEventBus_T:InformationGroupEventBus
K_showLoading_I:FullScreenLayerGroupEventBus_T:FullScreenLayerGroupEventBus
K_stopLoading_I:PlayLoadingElement_T:PlayLoadingElement
K_updateLoadingVisibility_I:PlayLoadingElement_T:PlayLoadingElement
K_tryShowOrHide_I:PlayLoadingElement_T:PlayLoadingElement
K_hideAndLog_I:PlayLoadingElement_T:PlayLoadingElement
K_tryHide_I:BaseElement_T:PlayLoadingElement
K_setValue_I:KLiveData_T:KDispatchLiveData
K_run_I:KDispatchLiveData$NotifyAction_T:NotifyAction
K_access$301_I:KDispatchLiveData_T:KDispatchLiveData
K_onPlayerStateChanged_I:MilanoItemPlayerPresenter$3_T:
K_getCardId_I:SlidePlayRealShowPresenter_T:SlidePlayRealShowPresenter
K_getPageList_I:SlidePlayViewModel_T:SlidePlayViewModel
K_formatKtvScoreInfoText_I:SlideKtvHelper_T:SlideKtvHelper
K_isKtvLabel_I:SlideKtvHelper_T:SlideKtvHelper
K_getUpdateInterval_I:ProgressUtil_T:ProgressUtil
K_getAnimatorUpdateThreshold_I:ProgressUtil_T:ProgressUtil
K_showProgressBar_I:NasaMilanoProgressPresenter_T:NasaMilanoProgressPresenter
K_setSelected_I:IMilanoProgress_T:NasaMilanoProgress
K_onSelectedChanged_I:NasaMilanoProgress_T:NasaMilanoProgress
K_onPageSelected_I:KwaiGrootFooterLoadingManager_T:KwaiGrootFooterLoadingManager
K_updateFootLoading_I:KwaiGrootFooterLoadingManager_T:KwaiGrootFooterLoadingManager
K_getCurrentDataSource_I:KwaiGrootDataSourceManager_T:KwaiGrootDataSourceManager
K_getLastIndexInCurrentDataSource_I:KwaiGrootFooterLoadingManager_T:KwaiGrootFooterLoadingManager
K_setPollUpPrefetchThreshold_I:HomeFeaturedMilanoContainerFragment$1_T:HomeFeaturedMilanoContainerFragment$1
K_setPollUpPrefetchThreshold_I:SlidePlayViewModel_T:SlidePlayViewModel
K_doLogShowEvent_I:DetailPhotoLogPresenter_T:DetailPhotoLogPresenter
K_getManifestString_I:PlayerCacheSizeUtil_T:PlayerCacheSizeUtil
K_toJsonString_I:ManifestInterface_T:KwaiManifest
K_writeAdaption_I:KwaiManifestTypeAdapter_T:KwaiManifestTypeAdapter
K_getPageParams_I:NasaGrootDetailVMFragment_T:NasaGrootDetailVMFragment
K_getGrootPageParams_I:PhotoPageParamsUtils_T:PhotoPageParamsUtils
K_getCommonBuilder_I:PhotoPageParamsUtils_T:PhotoPageParamsUtils
K_getViewItemType_I:ViewItemService_T:ViewItemService
K_onPageChanged_I:KPopPageChangeListener$init$1_T:KPopPageChangeListener$init$1
K_getH5KAKWhiteList_I:KAKAppContextImpl_T:KAKAppContextImpl
K_handlePhotoDetailLeave_I:CommercialLogInitModule_T:CommercialLogInitModule
K_isAd_I:CommercialLogInitModule_T:CommercialLogInitModule
K_getUrl_I:SlidePlayPhotoDetailVMFragment_T:NasaGrootDetailVMFragment
K_getCurrentPlaySource_I:QPhotoMediaPlayerImplV3_T:QPhotoMediaPlayerImplV3
K_usePlayerKitReportLog_I:PhotoDetailPlayLoggerHelper_T:PhotoDetailPlayLoggerHelper
K_logMsg_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3
K_getLogPrefix_I:KwaiMediaPlayerWrapper_T:MultiSourceMediaPlayerImplV3
K_getCurrentItemPositionInViewPager_I:PositionService_T:PositionService
K_getCurrentShowIndex_I:SlidePlayViewModel_T:SlidePlayViewModel
K_getAdapterLastValidItemPosition_I:SlidePlayViewModel_T:SlidePlayViewModel
K_hasNextPhoto_I:KwaiPlayService_T:KwaiPlayService
K_contains_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter
K_getViewItemType_I:NasaGrootItemCreator_T:NasaGrootItemCreator
K_getPageList_I:SlidePlayRealShowPresenter_T:SlidePlayRealShowPresenter
K_addNormalRealShowModel_I:SlidePlayRealShowPresenter_T:SlidePlayRealShowPresenter
K_getScheduleHandlerIntervalMs_I:RedesignPlayProgressEventPresenter_T:RedesignPlayProgressEventPresenter
K_getUpdateInterval_I:ProgressUtil_T:ProgressUtil
K_getSpeed_I:ProgressUtil_T:ProgressUtil
K_getCurrentShowIndex_I:SlideUserProcessLoggerPresenter_T:SlideUserProcessLoggerPresenter
K_initSlidePhoto_I:SlideUserProcessLoggerPresenter_T:SlideUserProcessLoggerPresenter
K_getMediaType_I:QPhotoMediaType_T:QPhotoMediaType
K_hasWelcomeBackLoginDialogShowed_I:WelcomeBackHelper_T:WelcomeBackHelper
K_clearStatusListener_I:ActionTriggerVMPresenter_T:ActionTriggerVMPresenter
K_unregisterVideoListeners_I:VideoPlayedReporter_T:VideoPlayedReporter
K_finishWithPlayerKit_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit
K_finishLogWhenUsePlayerKit_I:PhotoDetailPlayLoggerHelper_T:PhotoDetailPlayLoggerHelper
K_setupLogInfo_I:PhotoDetailPlayLoggerHelper_T:PhotoDetailPlayLoggerHelper
K_logPlaySourceInfo_I:QPhotoMediaPlayerImplV3_T:QPhotoMediaPlayerImplV3
K_getCurrentItemRealPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel
K_getCurrentRealPositionInAdapter_I:PositionService_T:PositionService
K_getOriginPhotoList_I:SlidePlayViewModel_T:SlidePlayViewModel
K_checkPosition_I:NasaSlideMonitorIndexPresenter_T:NasaSlideMonitorIndexPresenter
K_whetherToCheckPosition_I:NasaSlideMonitorIndexPresenter_T:NasaSlideMonitorIndexPresenter
K_nextIsUnShow_I:NasaSlideMonitorIndexPresenter_T:NasaSlideMonitorIndexPresenter
K_getPage2_I:NasaGrootDetailVMFragment_T:NasaGrootDetailVMFragment
K_getCurrentElementName_I:BaseElement_T:PayCourseTrailFinishElement
K_getGrootService_I:SlidePlayViewModel_T:SlidePlayViewModel
K_getService_I:GrootController_T:KwaiGrootController
K_updateKsOrderList_I:GrootBaseFragment_T:NasaGrootDetailVMFragment
K_logInfo_I:GrootLogger_T:GrootLogger
K_logPlayInfo_I:KwaiMediaPlayerImplV3_T:KwaiMediaPlayerImplV3
K_logCustomEventByNewUser_I:GrowthLoggerHelper_T:GrowthLoggerHelper
K_doCurrentMainDispatch_I:SlidePlayLifecycleHelper_T:SlidePlayLifecycleHelper
K_performTrackStage_I:DetailSlidePlayFragment_T:NasaGrootDetailVMFragment
K_getCurrentShowIndex_I:SlidePlayViewModel_T:SlidePlayViewModel
K_getCurrentItemRealPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel
K_getCurrentRealPositionInAdapter_I:PositionService_T:PositionService
K_hasNextPhoto_I:SlidePlayViewModel_T:SlidePlayViewModel
K_getPositionInAdapter_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter
K_getPositionInAdapter_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter
K_updateProgress_I:SidebarProgressElement_T:SidebarProgressElement
K_enableSmoothProgress_I:NasaMilanoProgress_T:NasaMilanoProgress
K_resetProgress_I:NasaFeaturedSeekBar_T:NasaFeaturedSeekBar
K_changeSeekBarShown_I:NasaDetailBottomBarVMPresenter_T:NasaDetailBottomBarVMPresenter
K_showOrHideBottomBar_I:NasaDetailBottomBarVMPresenter_T:NasaDetailBottomBarVMPresenter
K_becomesAttachInner_I:BasePage_T:SlidePage
K_logViewState_I:BottomProgressLoadingElement_T:BottomProgressLoadingElement
K_createOnScrollListener_I:MilanoProfileFeedLoadMoreOptPresenter_T:MilanoProfileFeedLoadMoreOptPresenter
K_performViewItemWillAppear_I:DetailSlidePlayFragment_T:NasaGrootDetailVMFragment
K_performTrackPageStage_I:DetailSlidePlayFragment_T:NasaGrootDetailVMFragment
K_callBecomesAttachedOnPageSelectedListener_I:SlidePlayPhotoDetailVMFragment_T:NasaGrootDetailVMFragment
K_traverseListenersBecomesAttached_I:SlidePlayLifecycleHelper_T:SlidePlayLifecycleHelper
K_becomesAttachedOnPageSelected_I:DetailPlayModuleImplPlayerKit_T:DetailPlayModuleImplPlayerKit
K_logApmFirstFrameStart_I:PhotoDetailPlayLoggerHelper_T:PhotoDetailPlayLoggerHelper
K_logApmFirstFrameSuccess_I:PhotoDetailPlayLoggerHelper_T:PhotoDetailPlayLoggerHelper
K_recordAudioFirstRenderTime_I:PhotoDetailPlayLoggerHelper_T:PhotoDetailPlayLoggerHelper
K_getCurrentShowIndex_I:SlidePlayViewModel_T:SlidePlayViewModel
K_getCurrentItemRealPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel
K_getCurrentRealPositionInAdapter_I:PositionService_T:PositionService
K_hasNextPhoto_I:SlidePlayViewModel_T:SlidePlayViewModel
K_getPositionInAdapter_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter
K_contains_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter
K_getPlayerSpeedOutsideShow_I:NasaExperimentUtils_T:NasaExperimentUtils
K_isSaveTrafficEnable_I:SlidePlayerPanelPhotoConfig_T:SlidePlayerPanelPhotoConfig
K_isSaveTrafficEnable_I:SlidePlayerPanelConfig_T:SlidePlayerPanelConfig
K_getEnablePlayerSpeedAndDataSaveModeOptimize_I:NasaExperimentUtils_T:NasaExperimentUtils
K_getQualityEnable_I:SlidePlayerPanelPhotoConfig_T:SlidePlayerPanelPhotoConfig
K_getQualityEnable_I:SlidePlayerPanelConfig_T:SlidePlayerPanelConfig
K_isAccessibilityEnable_I:SlidePlayerPanelPhotoConfig_T:SlidePlayerPanelPhotoConfig
K_isAccessibilityEnable_I:SlidePlayerPanelConfig_T:SlidePlayerPanelConfig
K_isSoundEffectEnable_I:SlidePlayerPanelPhotoConfig_T:SlidePlayerPanelPhotoConfig
K_isSoundEffectEnable_I:SlidePlayerPanelConfig_T:SlidePlayerPanelConfig
K_isCollectDislikeEnable_I:SlidePlayerPanelPhotoConfig_T:SlidePlayerPanelPhotoConfig
K_isSmallWindowEnable_I:SlidePlayerPanelPhotoConfig_T:SlidePlayerPanelPhotoConfig
K_isSmallWindowEnable_I:SlidePlayerPanelConfig_T:SlidePlayerPanelConfig
K_getCardId_I:SlidePlayRealShowPresenter_T:SlidePlayRealShowPresenter
K_getInstance_I:SurveyElementHelper_T:SurveyElementHelper
K_write_I:KwaiManifestTypeAdapter_T:KwaiManifestTypeAdapter
K_onPlayerStateChanged_I:MilanoItemPlayerPresenter$3_T:
K_swipeToShowElement_I:PlayPauseCenterElement_T:PlayPauseCenterElement
K_setRightActionBarSwipeProgress_I:BasePage_T:SlidePage
K_setViewSwipeProgress_I:BasePage_T:SlidePage
K_generateMeasureList_I:PriorityLinearLayout_T:UserNameLinearLayout
K_getCurrentDataSource_I:KwaiGrootDataSourceManager_T:KwaiGrootDataSourceManager
K_getLastIndexInCurrentDataSource_I:KwaiGrootFooterLoadingManager_T:KwaiGrootFooterLoadingManager
K_getPhotoCacheSize_I:PlayerCacheSizeUtil_T:PlayerCacheSizeUtil
K_getH5KAKWhiteList_I:KAKAppContextImpl_T:KAKAppContextImpl
K_doCurrentMainDispatch_I:SlidePlayLifecycleHelper_T:SlidePlayLifecycleHelper
K_getCurrentFragmentOnScreen_I:DetailSlidePlayFragment_T:NasaGrootDetailVMFragment
K_getCurrentShowIndex_I:SlidePlayViewModel_T:SlidePlayViewModel
K_getCurrentItemRealPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel
K_getCurrentRealPositionInAdapter_I:PositionService_T:PositionService
K_hasNextPhoto_I:SlidePlayViewModel_T:SlidePlayViewModel
K_getPositionInAdapter_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter
K_contains_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter
K_getViewItemType_I:ViewItemService_T:ViewItemService
K_getViewItemType_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter
K_getViewItemType_I:NasaGrootItemCreator_T:NasaGrootItemCreator
K_getPageList_I:SlidePlayRealShowPresenter_T:SlidePlayRealShowPresenter
K_getScheduleHandlerIntervalMs_I:RedesignPlayProgressEventPresenter_T:RedesignPlayProgressEventPresenter
K_getUpdateInterval_I:ProgressUtil_T:ProgressUtil
K_getSpeed_I:ProgressUtil_T:ProgressUtil
K_getCurrentShowIndex_I:SlideUserProcessLoggerPresenter_T:SlideUserProcessLoggerPresenter
K_initSlidePhoto_I:SlideUserProcessLoggerPresenter_T:SlideUserProcessLoggerPresenter
K_getMediaType_I:QPhotoMediaType_T:QPhotoMediaType
K_getCurrentFragmentOnScreen_I:DetailSlidePlayFragment_T:NasaGrootDetailVMFragment
K_getCurrentShowIndex_I:SlidePlayViewModel_T:SlidePlayViewModel
K_getCurrentItemRealPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel
K_getCurrentRealPositionInAdapter_I:PositionService_T:PositionService
K_hasNextPhoto_I:SlidePlayViewModel_T:SlidePlayViewModel
K_getPositionInAdapter_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter
K_contains_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter
K_getViewItemType_I:ViewItemService_T:ViewItemService
K_getViewItemType_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter
K_getViewItemType_I:NasaGrootItemCreator_T:NasaGrootItemCreator
K_getPageList_I:SlidePlayRealShowPresenter_T:SlidePlayRealShowPresenter
K_getScheduleHandlerIntervalMs_I:RedesignPlayProgressEventPresenter_T:RedesignPlayProgressEventPresenter
K_getUpdateInterval_I:ProgressUtil_T:ProgressUtil
K_getSpeed_I:ProgressUtil_T:ProgressUtil
K_getCurrentShowIndex_I:SlideUserProcessLoggerPresenter_T:SlideUserProcessLoggerPresenter
K_initSlidePhoto_I:SlideUserProcessLoggerPresenter_T:SlideUserProcessLoggerPresenter
K_getMediaType_I:QPhotoMediaType_T:QPhotoMediaType
K_getCurrentFragmentOnScreen_I:DetailSlidePlayFragment_T:NasaGrootDetailVMFragment
K_getCurrentShowIndex_I:SlidePlayViewModel_T:SlidePlayViewModel
K_getCurrentItemRealPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel
K_getCurrentRealPositionInAdapter_I:PositionService_T:PositionService
K_hasNextPhoto_I:SlidePlayViewModel_T:SlidePlayViewModel
K_getPositionInAdapter_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter
K_contains_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter
K_getViewItemType_I:ViewItemService_T:ViewItemService
K_getViewItemType_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter
K_getViewItemType_I:NasaGrootItemCreator_T:NasaGrootItemCreator
K_getPageList_I:SlidePlayRealShowPresenter_T:SlidePlayRealShowPresenter
K_getScheduleHandlerIntervalMs_I:RedesignPlayProgressEventPresenter_T:RedesignPlayProgressEventPresenter
K_getUpdateInterval_I:ProgressUtil_T:ProgressUtil
K_getSpeed_I:ProgressUtil_T:ProgressUtil
K_getCurrentShowIndex_I:SlideUserProcessLoggerPresenter_T:SlideUserProcessLoggerPresenter
K_initSlidePhoto_I:SlideUserProcessLoggerPresenter_T:SlideUserProcessLoggerPresenter
K_getMediaType_I:QPhotoMediaType_T:QPhotoMediaType
K_getCurrentFragmentOnScreen_I:DetailSlidePlayFragment_T:NasaGrootDetailVMFragment
K_getCurrentShowIndex_I:SlidePlayViewModel_T:SlidePlayViewModel
K_getCurrentItemRealPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel
K_getCurrentRealPositionInAdapter_I:PositionService_T:PositionService
K_hasNextPhoto_I:SlidePlayViewModel_T:SlidePlayViewModel
K_getPositionInAdapter_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter
K_contains_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter
K_getViewItemType_I:ViewItemService_T:ViewItemService
K_getViewItemType_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter
K_getViewItemType_I:NasaGrootItemCreator_T:NasaGrootItemCreator
K_getPageList_I:SlidePlayRealShowPresenter_T:SlidePlayRealShowPresenter
K_getScheduleHandlerIntervalMs_I:RedesignPlayProgressEventPresenter_T:RedesignPlayProgressEventPresenter
K_getUpdateInterval_I:ProgressUtil_T:ProgressUtil
K_getSpeed_I:ProgressUtil_T:ProgressUtil
K_getCurrentShowIndex_I:SlideUserProcessLoggerPresenter_T:SlideUserProcessLoggerPresenter
K_initSlidePhoto_I:SlideUserProcessLoggerPresenter_T:SlideUserProcessLoggerPresenter
K_getMediaType_I:QPhotoMediaType_T:QPhotoMediaType
K_getCurrentFragmentOnScreen_I:DetailSlidePlayFragment_T:NasaGrootDetailVMFragment
K_getCurrentShowIndex_I:SlidePlayViewModel_T:SlidePlayViewModel
K_getCurrentItemRealPositionInAdapter_I:SlidePlayViewModel_T:SlidePlayViewModel
K_getCurrentRealPositionInAdapter_I:PositionService_T:PositionService
K_hasNextPhoto_I:SlidePlayViewModel_T:SlidePlayViewModel
K_getPositionInAdapter_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter
K_contains_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter
K_getViewItemType_I:ViewItemService_T:ViewItemService
K_getViewItemType_I:GrootBasePagerAdapter_T:KwaiGrootSmoothUpdatePagerAdapter
K_getViewItemType_I:NasaGrootItemCreator_T:NasaGrootItemCreator
K_getPageList_I:SlidePlayRealShowPresenter_T:SlidePlayRealShowPresenter
K_getScheduleHandlerIntervalMs_I:RedesignPlayProgressEventPresenter_T:RedesignPlayProgressEventPresenter
K_getUpdateInterval_I:ProgressUtil_T:ProgressUtil
K_getSpeed_I:ProgressUtil_T:ProgressUtil
K_getCurrentShowIndex_I:SlideUserProcessLoggerPresenter_T:SlideUserProcessLoggerPresenter
K_initSlidePhoto_I:SlideUserProcessLoggerPresenter_T:SlideUserProcessLoggerPresenter
K_getMediaType_I:QPhotoMediaType_T:QPhotoMediaType
K_getCurrentFragmentOnScreen_I:DetailSlidePlayFragment_T:NasaGrootDetailVMFragment
```