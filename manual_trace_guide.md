# 🔧 手动处理Trace文件指南

由于您的trace文件在自动处理时遇到问题，请按照以下步骤手动提取数据：

## 步骤1: 使用Perfetto Web UI

1. **打开Perfetto Web UI**
   - 访问: https://ui.perfetto.dev
   - 这是Google官方的在线trace分析工具

2. **上传trace文件**
   - 点击 "Open trace file" 或直接拖拽您的 `trace.perfetto-trace` 文件到页面中
   - 等待文件加载完成

3. **执行SQL查询**
   - 在界面中找到 "Query (SQL)" 选项卡
   - 输入以下SQL查询：
   ```sql
   SELECT id, dur, name, depth, thread_dur, ts 
   FROM slice 
   WHERE track_id IN (11) 
   ORDER BY ts ASC, depth ASC
   ```

4. **导出数据**
   - 执行查询后，点击结果表格右上角的导出按钮
   - 选择 "Export as CSV" 或 "Export as TSV"
   - 下载文件

5. **重命名文件**
   - 将下载的文件重命名为 `query_result.txt`
   - 将文件放在您的项目目录中 (`/Users/<USER>/Desktop/草稿/Comptation/`)

## 步骤2: 运行后续处理

一旦您有了 `query_result.txt` 文件，运行以下命令：

```bash
python process_manual_data.py
```

## 备选SQL查询

如果上面的查询没有返回数据，可以尝试以下查询：

### 查询1: 获取所有耗时较长的slice
```sql
SELECT id, dur, name, depth, thread_dur, ts 
FROM slice 
WHERE dur > 1000000
ORDER BY dur DESC 
LIMIT 1000
```

### 查询2: 获取特定进程的slice
```sql
SELECT id, dur, name, depth, thread_dur, ts 
FROM slice 
WHERE name LIKE '%function%' OR name LIKE '%method%'
ORDER BY ts ASC
LIMIT 1000
```

### 查询3: 获取所有slice（如果数据量不大）
```sql
SELECT id, dur, name, depth, thread_dur, ts 
FROM slice 
ORDER BY ts ASC
LIMIT 5000
```

## 故障排除

### 如果Web UI无法打开文件：
1. 检查文件大小（超过100MB可能需要更长时间）
2. 尝试使用Chrome浏览器
3. 确保网络连接稳定

### 如果查询返回空结果：
1. 尝试不同的查询条件
2. 检查trace文件是否包含slice数据
3. 尝试查看所有可用的表：
   ```sql
   SELECT name FROM sqlite_master WHERE type='table'
   ```

## 完成后

一旦您成功导出了数据，我们就可以继续运行完整的RAG分析流程了！
