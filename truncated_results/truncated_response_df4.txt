<think>好的，我现在需要根据用户提供的数据来识别关键的代码片段，以便后续优化应用程序性能。首先，我得仔细看看这些数据中的各个字段，包括id、dur、name、depth、thread_dur和ts。用户希望找出那些dur（持续时间）较长的条目，因为这可能意味着某些操作耗时较多，影响性能。

首先，我会浏览所有dur列，寻找数值较大的项。例如，id为92879的dur是524679583，看起来非常大，这可能是一个关键点。对应的名字是K_onActivityCreated_I:NasaGrootDetailVMFragment_T:NasaGrootDetailVMFragment，这可能涉及到Fragment的生命周期方法，可能导致初始化时间过长。

接下来，id 99495的dur是24293750，名字是K_beforeBindPage_I:NasaGrootDetailVMFragment_T:NasaGrootDetailVMFragment，可能是在绑定页面之前执行的操作耗时较多。同样，id 92543的dur是24468，对应的名称是K_initPresenter_I:SlidePlayPhotoDetailVMFragment_T:NasaGrootDetailVMFragment，初始化Presenter可能存在问题。

还有id 92478的dur是105934，是K_addToWrapper_I:PhotoMetaAccessor_T:PhotoMetaAccessor，添加到包装器的过程可能耗时。id 92384的dur是2948854，是K_createCorePresenterIfNeed_I:SlidePlayPhotoDetailVMFragment_T:NasaGrootDetailVMFragment，创建核心Presenter可能需要优化。

此外，一些线程竞争的条目，如id 100514和100515，可能涉及锁竞争，导致性能瓶颈。还有像id 93005的dur是7018386，K_createDetailPlayModule_I:DetailPlayModuleFactory_T:DetailPlayModuleFactory，创建播放模块的时间较长，需要检查。

还需要注意深度（depth）较深的条目，虽然不一定持续时间长，但可能嵌套调用影响性能。比如id 105275的深度是38，对应的K_setCaptionText_I:CaptionViewModel_T:CaptionViewModel，可能涉及多层回调。

另外，数据绑定和观察者模式相关的条目，如K_setValue_I和K_notifyValueSet，如果频繁调用且耗时，可能需要优化数据流。例如id 100509的K_setValue_I耗时385ms，可能影响响应时间。

总结下来，需要关注高dur值、锁竞争、深度嵌套的方法，以及频繁的数据绑定和观察者通知操作。这些可能是性能优化的重点区域。
</think>```code
[
  {
    "id": 92879,
    "dur": 524679583,
    "name": "K_onActivityCreated_I:NasaGrootDetailVMFragment_T:NasaGrootDetailVMFragment",
    "depth": 14.0,
    "thread_dur": NaN
  },
  {
    "id": 99495,
    "dur": 24293750,
    "name": "K_beforeBindPage_I:NasaGrootDetailVMFragment_T:NasaGrootDetailVMFragment",
    "depth": 18.0,
    "thread_dur": NaN
  },
  {
    "id": 92543,
    "dur": 24468021,
    "name": "K_initPresenter_I:SlidePlayPhotoDetailVMFragment_T:NasaGrootDetailVMFragment",
    "depth": 17.0,
    "thread_dur": NaN
  },
  {
    "id": 92478,
    "dur": 1059346,
    "name": "K_addToWrapper_I:PhotoMetaAccessor_T:PhotoMetaAccessor",
    "depth": 17.0,
    "thread_dur": NaN
  },
  {
    "id": 93005,
    "dur": 7018386,
    "name": "K_createDetailPlayModule_I:DetailPlayModuleFactory_T:DetailPlayModuleFactory",
    "depth": 18.0,
    "thread_dur": NaN
  },
  {
    "id": 100514,
    "dur": 335313,
    "name": "K_notifyValueSet_I:KLiveData_T:KDispatchLiveData",
    "depth": 24.0,
    "thread_dur": NaN
  },
  {
    "id": 105275,
    "dur": 596250,
    "name": "K_setCaptionText_I:CaptionViewModel_T:CaptionViewModel",
    "depth": 32.0,
    "thread_dur": NaN
  },
  {
    "id": 94752,
    "dur": 25674635,
    "name": "K_bind_I:BasePage_T:SlidePage",
    "depth": 16.0,
    "thread_dur": NaN
  },
  {
    "id": 100494,
    "dur": 296423281,
    "name": "K_bindInner_I:BasePage_T:SlidePage",
    "depth": 17.0,
    "thread_dur": NaN
  },
  {
    "id": 92872,
    "dur": 524679,
    "name": "K_onActivityCreated_I:NasaGrootDetailVMFragment_T:NasaGrootDetailVMFragment",
    "depth": 14.0,
    "thread_dur": NaN
  }
]
```
path:ks-features/ft-feed/detail/src/com/yxcorp/gifshow/detail/slideplay/nasa/groot/vm/NasaPhotoDetailFragment.javacode:@Override
  public void onActivityCreated(@Nullable Bundle savedInstanceState) {
    SlidePlayMonitorLogger.log(TAG, "onActivityCreated", "start");
    NasaCommentPositionUtil.setComment(getActivity(), mDetailParam);
    super.onActivityCreated(savedInstanceState);
    // 做个保险，如果出现 restore 的时候， fragment 会执行到这里
    // 现在在 PhotoDetailActivity 里面已经坐了保护, 这里理论不会出现。
    if (mDetailParam == null || mDetailParam.mPhoto == null) {
      return;
    }
    mPerfLogger.markStart(PerfLoggerType.FRAGMENT_TYPE, TAG, "onActivityCreated");
    createDetailVMParam();
    SlidePlayMonitorLogger.log(TAG, "onActivityCreated", "createCallerContext");
    performTrackStage(STAGE_CREATE_CALLER_CONTEXT, true);
    updateCallerContext();
    performTrackStage(STAGE_CREATE_CALLER_CONTEXT, false);
    SlidePlayMonitorLogger.log(TAG, "onActivityCreated", "createCorePresenter");
    performTrackStage(STAGE_CREATE_CORE_PRESENTER, true);
    createCorePresenterIfNeed(getView());
    //尝试异步Presenter效果，目前未上线。
    //createAsyncPresenterIfNeed(getView());
    performTrackStage(STAGE_CREATE_CORE_PRESENTER, false);
    createPresenterIfNeed(getView());
    SlidePlayMonitorLogger.log(TAG, "onActivityCreated", "mCorePresenter.bind");
    performTrackStage(STAGE_BIND_CORE_PRESENTER, true);
    mDetailFactory.performOnBind(mDetailParam, mFragmentCallerContext, mPhoto);
    Object[] contexts;
    AccessorWrapper accessorWrapper =
        (new InjectExtension(this, PresenterV2.class)).buildAccessorWrapper(mDetailParam,
            mFragmentCallerContext,
            this,
            getActivity());
    contexts = new Object[]{accessorWrapper};
    mCorePresenter.bind(contexts);
    if (DetailExperimentUtils.enableElementAndPresenterChangeOrder()) {
      bindSlidePage();
    }
    performTrackStage(STAGE_BIND_CORE_PRESENTER, false);
    SlidePlayMonitorLogger.log(TAG, "onActivityCreated", "mMainThreadScatterPresenterGroup.bind");
    performTrackStage(STAGE_BIND_SCATTER_PRESENTER, true);
    mMainThreadScatterPresenterGroup.bind(contexts);
    performTrackStage(STAGE_BIND_SCATTER_PRESENTER, false);
    if (!DetailExperimentUtils.enableElementAndPresenterChangeOrder()) {
      bindSlidePage();
    }
    // 进入详情页停止直播播放.
    mDetailFactory.afterBind();

    //这里不要做打散，内部已经做的打散
    checkIsSelectedAfterCreate();
    // 需要放到最后保证SlideTaskDispatcher监听到onActivityCreated生命周期时，已经添加完了create任务
    if (mSlideDispatchAssist != null) {
      if (!needIgnoreFlushCreateTask) {
        mSlideDispatchAssist.flushCreateTask();
      }
    }
    if (AppEnv.isTestChannel()) {
      SlideDispatchHelper.checkWaterMark(this, getDispatcherContext());
    }
    performTrackPageStage(DetailStageRecorder.PAGE_STAGE_CREATE, false);
    SlidePlayMonitorLogger.log(TAG, "onActivityCreated", "end");
    mVolumeStateDisposable = GlobalMuteModeHelper.observeVolumeChange(this::changeVolumeInfNeed, Functions.ERROR_CONSUMER);
    mPerfLogger.markEnd(PerfLoggerType.FRAGMENT_TYPE, TAG, "onActivityCreated");
  }
path:ks-features/ft-feed/featured-detail/src/main/java/com/yxcorp/gifshow/featured/detail/featured/milano/HomeFeaturedMilanoContainerFragment.javacode:@Override
  protected void afterViewCreated() {
    if (getCompositeLifecycleState().isReallySelect()) {
      RxBus.INSTANCE.postSticky(new NasaFeatureLaunchSelectEvent());
    }
    // 监控精选页 fragment 的帧率
    new HomeSceneLifecycleHelper(this).start();
    IAtomicController atomicController = getAtomicControllerNullable();
    if (atomicController != null) {
      // 注册一个获取当前作品播放时长的action，供外部获取播放时长。
      atomicController.actionObserver().observeAction(getLifecycle(), PLAYER_DURATION_GETTER,
          (IPlayerDurationGetter) () -> {
            if (mCallerContext == null || mCallerContext.mGlobalParams == null
                || mCallerContext.mGlobalParams.mMilanoContainerEventBus == null
                || mCallerContext.mGlobalParams.mMilanoContainerEventBus.mCurrentPlayerProxy == null) {
              return -1;
            }
            return mCallerContext.mGlobalParams.mMilanoContainerEventBus.mCurrentPlayerProxy
                .getActualPlayDuration();
          });
    }
    mGrootViewPager.registerOnPageChangeObserver(new ViewPager.SimpleOnPageChangeListener() {
      @Override
      public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
        GestureMonitor.recordInterceptEvent(getContext(), GestureBiz.SLIDE_SWITCH_PHOTO, true, "scrolled");
      }
    });
    mGrootViewPager.mMeasureOptimizer.setEnable(SlidePerfOptExp4.config.enableSlideItemMeasureOpt);
  }
path:ks-features/ft-feed/detail/src/com/yxcorp/gifshow/detail/slidev2/serial/presenter/NasaDetailSerialPhaseCostPresenter.ktcode:private fun reportPhaseLog() {
    val map = mutableMapOf<String, String>()
    mNasaBizParam?.nasaSlideParam?.mNasaSlideSerialParam?.let {
      map["serialId"] = it.mSerialId ?: TextUtils.EMPTY_STRING
      map["serialType"] = it.mSerialType.toString()
      map["serialContext"] = it.mSerialContext ?: TextUtils.EMPTY_STRING
      map["serialPhotoPage"] = it.mPhotoPage ?: TextUtils.EMPTY_STRING
      map["isClusterSerial"] = if (it.mIsClusterSerial) {
        "1"
      } else {
        "0"
      }
      map["continueSessionId"] = it.mContinueSessionID ?: TextUtils.EMPTY_STRING
      map["photoId"] = NasaDetailEpisodeSerialUtil.getSelectedPhotoId(activity?.intent) ?: TextUtils.EMPTY_STRING
      map["intentScheme"] = activity?.intent?.data?.toString() ?: TextUtils.EMPTY_STRING
      map["startPlayDuration"] = it.mFlowPresenterStartTimeStamp.minus(it.mCreateTimeStamp).toString()
      map["serialPageApiDuration"] = it.mNetApiStopTimeStamp.minus(it.mNetApiStartTimeStamp).toString()
      map["serialRenderDuration"] = it.mViewRenderCompleteTimeStamp.minus(it
          .mNetApiStopTimeStamp).toString()

      // 播放器用户首屏耗时
      val startPlayTimeStamp = mPhotoDetailLogger.get()?.clickOpenTimeStampIfAny ?: 0L
      if (startPlayTimeStamp != 0L && startPlayTimeStamp != -1L && startPlayTimeStamp != Long
              .MAX_VALUE) {
        map["serialQPhotoPlayerFirstFrameDuration"] = SystemClock.elapsedRealtime()
            .minus(startPlayTimeStamp).toString()
      }
      map["serialTotalCost"] = System.currentTimeMillis().minus(it.mCreateTimeStamp).toString()

      CoronaMonitorUtils.logCustomEvent(
          CoronaMonitorEventId.PHASE_DURATION,
          CoronaBizType.BIZ_SERIAL_DETAIL,
          CoronaSubBizType.DETAIL_PAGE,
          "success",
          null,
          map
        )
    }
  }
path:ks-features/ft-feed/detail/src/com/yxcorp/gifshow/detail/slideplay/nasa/vm/presenter/NasaMilanoProgressPresenter.javacode:@Override
  protected void doInject() {
    super.doInject();
    mFragmentLocalBus = inject(TypeEventBus.class);
    mPhoto = inject(QPhoto.class);
    mNasaBizParam = inject(NasaBizParam.class);
    mFragment = inject(AccessIds.DETAIL_FRAGMENT);
    mPhotoDetailParam = inject(PhotoDetailParam.class);
    mDetailParam = inject(DetailParam.class);
    mSidebarStatusHelper = inject(DetailAccessIds.NASA_SIDEBAR_STATUS);
    mIsClosingStateRef = injectRef(AccessIds.SLIDE_PLAY_CLOSE_STATE);
    mPlayModule = inject(DetailPlayModule.class);
    mSwipeToProfileFeedMovement = inject(SwipeToProfileFeedMovement.class);
    mScreenCleanStatusCombination = inject(AccessIds.DETAIL_SCREEN_CLEAN_STATUS);
    mMoreViewShowObservable = inject(AccessIds.MORE_VIEW_SHOW_OBSERVABLE);
    mParentPageScrollHelper = injectOptional(IParentPageScrollHelper.class);
    mMoreTrendingListShowPublisher = injectOptional(AccessIds.MORE_TRENDING_LIST_SHOW_EVENT);
    mNasaProgressBarShowObserver = inject(AccessIds.PROGRESS_BAR_SHOW_OBSERVER);
    mMilanoContainerEventBus = inject(MilanoContainerEventBus.class);
    mNasaScaleCleanControllerShow =
        inject(DetailAccessIds.NASA_SCALE_CLEAN_CONTROLLER_SHOW_OBSERVABLE);
    mNasaProgressBarEnhanceObservable = inject(AccessIds.PROGRESS_BAR_ENHANCE_OBSERVABLE);
    mCancelGuideObserver = inject(DetailAccessIds.DETAIL_CANCEL_GUIDE_OBSERVER);
    mCommentPanelAnimationListeners =
        inject(AccessIds.SLIDE_PLAY_COMMENT_PANEL_STATE_LISTENER_LIST);
    mDetailPlayerSpeedGetter = inject(DetailAccessIds.PLAYER_SPEED_GETTER);
    mSlidePlayGlobalCache = inject(SlidePlayGlobalCache.class);
  }
path:ks-features/ft-live/live-features/live-audience/src/main/java/com/kuaishou/live/audience/basic/fragment/LiveDidShowLoader.javacode:public void reportComponentsDestroyDuration() {
    try {
      JsonStringBuilder detailBuilder = JsonStringBuilder.newInstance();
      detailBuilder.addProperty("unload", mComponentsUnloadDurationMs);
      detailBuilder.addProperty("destroy", mComponentsDestroyDurationMs);
      JsonStringBuilder builder = JsonStringBuilder.newInstance()
          .addProperty("count",
              mRootPresenterManager != null ? mRootPresenterManager.getPresenterCount() : 0)
          .addProperty("dur", mComponentsDestroyDurationMs + mComponentsUnloadDurationMs)
          .addProperty("detail", detailBuilder.build())
          .addProperty("enter_action", mContainer.getEnterAction())
          .addProperty("live_source_type", mAudienceParam.mLiveSourceType)
          .addProperty("is_slide_play", mContainer.isContainerSlidePlayMode() ? "1" : "0")
          .addProperty("is_slide_in", mContainer.isSlideIn() ? "1" : "0")
          .addProperty("live_stream_id", mLivePlayConfig.getLiveStreamId())
          .addProperty("stream_type", mLivePlayConfig.mStreamType)
          .addProperty("pattern_type", mLivePlayConfig.mPatternType)
          .addProperty("is_shop_or_merchant_live",
              LiveMerchantTypeUtils.INSTANCE.isShopOrMerchantLive(mPhoto) ? "1" : "0")
          .addProperty("is_local_life_live",
              LiveLocalLifeUtil.isLocalLifeLive(mPhoto.mEntity) ? "1" : "0")
          .addProperty("is_recruit_live",
              LiveRecruitUtils.isRecruitLiveForAudience(mPhoto.mEntity) ? "1" : "0")
          .addProperty("is_game_live",
              mContainer.getGzoneLiveType().isGameOrMultiTabLive() ? "1" : "0");
      String reportString = builder.build();
      LiveDebugLoggerV2.i(TAG, "reportComponentsDestroyDuration", "reportString", reportString);
      Logger.logCustomEvent(
          "live_component_destroy_duration_event",
          reportString,
          ClientEvent.CustomEvent.CustomEventBiz.LIVE
      );
    } catch (Exception e) {
      LiveDebugLoggerV2.e(TAG, "reportComponentsDestroyDuration", e);
    }
  }
path:ks-features/ft-feed/detail/src/com/yxcorp/gifshow/detail/slideplay/nasa/groot/vm/NasaPhotoDetailFragment.javacode:@SuppressWarnings("MethodCyclomaticComplexity")
  private void updateCallerContext() {
    if (mDetailParam == null) {
      return;
    }
    mFragmentCallerContext.mLogger = mLogger;
    initLogger();
    mDetailParam.getDetailPlayConfig()
        .setUsePlayerKitPlay(DetailOperationUtils.enablePlayerKitPlay(mDetailParam));
    initVideoBackFpsIfNeeded(); // 限制播放器帧率
    mDetailFactory.initPhotoDetailParam(mPhoto, mDetailParam);
    DetailPlayModule playModule =
        DetailPlayModuleFactory.createDetailPlayModule(this, mDetailParam.mPhoto,
            mDetailParam.getDetailPlayConfig(), mDetailParam.enableSlidePlay(),
            mDetailParam.isThanos());
    playModule.setPhotoDetailLogger(mLogger);
    playModule.getPlayer().addPlaySpeedChangedListener(mPlaySpeedChangedListener);
    mFragmentCallerContext.mDetailGestureConflictHelper = new DetailGestureConflictHelper();
    if (mSlidePlayViewModel != null) {
      mSlidePlayViewModel.doAddListener(this, (PhotoDetailAttachChangedListener) playModule);
    }
    mFragmentCallerContext.mPlayModule = playModule;
    logInfo("createCallerContext after create playModule");
    //收口播放器状态监听，支持播放器异步添加监听。
    if (mDetailFactory.enableRecordPlayerCreateTime() && mPhoto != null && !mPhoto.isImageType() &&
        HomePagePlugin.getInstance().isHomeSlideOrTabFragmentSelected(this)) {
      Singleton.get(HomeLaunchInfoRecorder.class).recordPlayerCreateTime();
    }
    mFragmentCallerContext.mIsFromSlide = isFromSlide();
    mFragmentCallerContext.mMarketSenseInfoMeta = MarketDataFeedExt.getMarketSenseInfoMeta(mPhoto);
    NasaSlideParam nasaSlideParam = mNasaBizParam == null ? null : mNasaBizParam.getNasaSlideParam();
    mFragmentCallerContext.mSlideFeedbackProvider = MarketDataFeedExt.getSlideFeedbackProvider(mPhoto, nasaSlideParam);
    mFragmentCallerContext.mDetailPlayerSpeedGetter = new DetailPlayerSpeedGetter(playModule.getPlayer());
    mDetailFactory.initCallerContext(mDetailParam, mFragmentCallerContext);
  }
path:ks-features/ft-feed/detail/src/com/yxcorp/gifshow/detail/slidev2/serial/util/NasaDetailEpisodeSerialUtil.ktcode:@JvmStatic
  fun processIntentParams(
      fragment: GrootSlidePlayDetailBaseContainerFragment,
      intent: Intent,
      photoDetailParam: PhotoDetailParam,
  ): NasaBizParam {

    photoDetailParam.isEpisodeSerial = true // 标记来自新版合集，极速版二级页激励挂件会用到这个判断
    photoDetailParam.detailLogParam.recoTabId = RecoBizEnum.RecoBiz.KUAISHOU_RELATED_VIDEO
    val photoSourcePage = intent.data?.getQueryParameter(PARAM_SOURCE_PHOTO_PAGE) ?: ""
    val isAutoShowPanel = SafetyUriUtil.getQueryParameterFromUri(
        intent.data,
        PARAM_EPISODE_SERIAL_AUTO_SHOW_PANEL,
        "false"
    ).toBoolean()
    // 获取共享播放器key
    val sessionKeyGenerator = getSessionKeyGenerator(intent)

    val bottomPanelSelectedType  = CommonUtil.toInt(intent.data?.getQueryParameter(
      PARAM_BOTTOM_PANEL_SELECTED_TYPE), NasaSlideSerialParam.PanelSelectedType.DEFAULT)
    val serialId = intent.data?.getQueryParameter(PARAM_EPISODE_SERIAL_ID)
    val isTubeSerial = intent.data?.getQueryParameter(PARAM_EPISODE_IS_TUBE_SERIAL) == "true"
    val serialType = CommonUtil.toInt(intent.data?.getQueryParameter(PARAM_EPISODE_SERIAL_TYPE), 1)

    val clusterId = intent.data?.getQueryParameter(PARAM_EPISODE_CLUSTER_ID)
    val clusterType = CommonUtil.toInt(intent.data?.getQueryParameter(PARAM_EPISODE_CLUSTER_TYPE)
        , 1)
    val callback = intent.data?.getQueryParameter(PARAM_COMMERCIAL_CALLBACK)
    if (!TextUtils.isEmpty(callback) && CoronaApiExperimentUtil.enableSerialPayCommercialCallback()) {
      // 每次跳转进合集二级页，重置商业化激活唤端条件
      SerialPayCommonUtil.reset(serialId)
      // 商业化进入合集二级页上报
      SerialPayCommonUtil.logEnterSerialDetailCommercial(callback!!)
    }
    val continueSessionID = intent.data?.getQueryParameter(CONTINUE_SESSION_ID)
    val photoPageSource = intent.data?.getQueryParameter(KEY_PHOTO_PAGE_SOURCE)
    val nasaSourcePage = intent.data?.getQueryParameter(PARAM_NASA_SOURCE_PAGE)
      ?: intent.getStringExtra(KEY_PHOTO_PAGE_SOURCE)
    val preCommercialPhoto = Singleton.get(PhotoAdLogAdditionInterface::class.java)
        .getPhotoBySerialId(serialId)
    val tubeExtParams = generateTubeParamsJsonString(intent)

    val showSubscribe = PadUtil.isPadAdaptation() &&
        intent.data?.getQueryParameter(PAD_SHOW_SUBSCRIBE).equalsIgnoreCase("1")
    val autoEnterTubeInside = PadUtil.isPadAdaptation() &&
        intent.data?.getQueryParameter(PAD_AUTO_ENTER_TUBE_INSIDE).equalsIgnoreCase("1")
    val autoSubscribe = PadUtil.isPadAdaptation() &&
        intent.data?.getQueryParameter(PAD_AUTO_SUBSCRIBE).equalsIgnoreCase("1")
    val isFromPadTube = PadUtil.isPadAdaptation() &&
        intent.data?.getQueryParameter(PAD_IS_FROM_TUBE_TAB).equalsIgnoreCase("1")
    val forbidShowCollectAlert  = PadUtil.isPadAdaptation() &&
        intent.data?.getQueryParameter(PAD_FORBID_SHOW_COLLECT_ALERT).equalsIgnoreCase("1")

    val nasaSlideParam = NasaSlideParam.Builder()
        .setPage(NasaSlideParam.NasaSlidePage.DETAIL)
        .setEnableSwipeDownBack(true)
        .setEnableCameraButton(false)
        .setDisableShowBottomTips(true)
        .setSourcePage(photoSourcePage)
        .setIsFromPadTube(isFromPadTube)
        .setPadAutoEnterTubeInside(autoEnterTubeInside)
        .setAllowShowFloatWidget(!BuildConfig.IS_NEBULA) // 极速版禁止展示激励挂件
        .setNasaSlideSerialParam(
            NasaSlideSerialParam.Builder()
                // 非短剧 多作者合集
                .setPullUpRecoSerial(!isTubeSerial && TextUtils.isEmpty(clusterId))
                .setSerialId(clusterId)
                .setPadShowCollect(showSubscribe)
                .setPadAutoSubscribe(autoSubscribe)
                .setPadForbidShowCollectAlert(forbidShowCollectAlert)
                .setSerialType(clusterType)
                .setPhotoPage(photoSourcePage)
                .setNasaSourcePage(nasaSourcePage)
                .setCommercialActiveCallback(callback)
                .setTubeExtParams(tubeExtParams)
                .setIsClusterSerial(!TextUtils.isEmpty(clusterId))
                .setContinueSessionId(continueSessionID)
                .setTubeSourceNativeEnum(TubeRelatedUtil.getTubeSourceEnum(intent))
                .setIsAutoShowPanel(isAutoShowPanel)
              .setBottomPanelSelectedType(bottomPanelSelectedType)
                .setFromProfileSerialBtnOrItem(
                    TextUtils.equals(photoPageSource, VALUE_PHOTO_PAGE_SOURCE_BTN)
                        || TextUtils.equals(photoPageSource, VALUE_PHOTO_PAGE_SOURCE_ITEM))
               