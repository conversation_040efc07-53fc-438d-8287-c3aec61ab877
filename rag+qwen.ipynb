#%% md
### SQL 提取信息
#%%
from perfetto.trace_processor import TraceProcessor
# Initialise TraceProcessor with a trace file
tp = TraceProcessor(trace='./trace.perfetto-trace')
qr_it = tp.query('''
select id, dur, name, depth, thread_dur, ts from slice where track_id in (11) order by ts asc, depth asc
''')
# qr_it = tp.query('''
# select id, ts, dur, name, depth, thread_dur from slice where track_id in (11) and ts >= 1469468088907009 and ts <= 1469468096543467 order by ts asc
# ''')
qr_df = qr_it.as_pandas_dataframe()
# Save the DataFrame to a .txt file
output_path = './query_result.txt'
qr_df.to_csv(output_path, sep='\t', index=False)

print(f"Query result saved to {output_path}")
#%% md
### 切分文件，小于16.66ms直接cut
#%%
import pandas as pd

def filter_short_frames(df, threshold_ms=16.66):
    """
    过滤持续时间小于指定阈值的帧
    
    参数：
    df : pandas.DataFrame
        包含列：id, dur, name, depth, thread_dur
    threshold_ms : float
        持续时间阈值（单位：毫秒），默认16.66ms
    
    返回：
    pandas.DataFrame
        过滤后的DataFrame，保持原始顺序
    """
    df = df.copy()
    
    threshold_ns = threshold_ms * 1_000_000 

    current_group = -1
    groups = []
    for depth in df['depth']:
        if depth == 0:
            current_group += 1
        groups.append(current_group)
    df['_group'] = groups
    
    try:
        group_first = df.groupby('_group').first()
        valid_groups = group_first[group_first['dur'] >= threshold_ns].index
        result_df = df[df['_group'].isin(valid_groups)].drop(columns='_group')
    finally:
        if '_group' in df.columns:
            df.drop(columns='_group', inplace=True)
    
    return result_df.reset_index(drop=True)

filtered_df = filter_short_frames(qr_df)
# output_path = './query_result_cut.txt'
# filtered_df.to_csv(output_path, sep='\t', index=False)
# print(f"Query result saved to {output_path}")
#%% md
### 小于0.02ms duration的日志信息都cut掉
#%%
def filter_dur(df, threshold=0.02):
    return df[df['dur'] >= threshold * 1e6] 

filtered_df = filter_dur(filtered_df, threshold=0.2)
# output_path = './query_result_cut_dur.txt'
# filtered_df.to_csv(output_path, sep='\t', index=False)
# print(f"Query result saved to {output_path}")
#%% md
### 拆分符合到模型的上下文
#%%
import pandas as pd

def split_into_blocks_and_save(df, path, chunk_size=1700):
    path = path if path.endswith("/") else path + "/"

    # 找到所有 depth=0 的行的索引
    zero_depth_indices = df[df['depth'] == 0].index.tolist()

    if not zero_depth_indices:
        print("未找到 depth=0 的行，无需分割。")
        return []

    # 分割原始块
    original_blocks = []
    num_blocks = len(zero_depth_indices)
    for i in range(num_blocks):
        start_idx = zero_depth_indices[i]
        end_idx = zero_depth_indices[i+1]-1 if i < num_blocks-1 else df.index[-1]
        original_blocks.append(df.loc[start_idx:end_idx])

    # 合并处理逻辑
    merged_blocks = []
    current_chunks = []
    current_rows = 0

    for chunk in original_blocks:
        chunk_rows = len(chunk)

        if current_rows + chunk_rows >= chunk_size:
            if current_chunks:
                merged = pd.concat(current_chunks)
                merged_blocks.append(merged)
                current_chunks = []
                current_rows = 0

            if chunk_rows >= chunk_size:
                merged_blocks.append(chunk)
            else:
                current_chunks.append(chunk)
                current_rows += chunk_rows
        else:
            current_chunks.append(chunk)
            current_rows += chunk_rows

    if current_chunks:
        merged = pd.concat(current_chunks)
        merged_blocks.append(merged)

    # 保存处理 + 收集所有分块
    parent_block_num = 1
    blocks_to_return = []  # 用于收集所有分块的 DataFrame

    for parent_block in merged_blocks:
        total_rows = len(parent_block)

        if total_rows > chunk_size:
            num_sub_blocks = (total_rows - 1) // chunk_size + 1

            for sub_idx in range(num_sub_blocks):
                start = sub_idx * chunk_size
                end = start + chunk_size
                sub_block = parent_block.iloc[start:end]

                filename = f"{path}block_{parent_block_num}_{sub_idx+1}.txt"
                sub_block.to_csv(
                    filename,
                    sep='\t',
                    index=False,
                    header=True,
                    columns=['id', 'dur', 'name', 'depth', 'thread_dur', 'ts']
                )
                print(f"切分保存: {filename} (原块{parent_block_num}，行数: {len(sub_block)})")
                blocks_to_return.append(sub_block)  # 收集子块
        else:
            filename = f"{path}block_{parent_block_num}.txt"
            parent_block.to_csv(
                filename,
                sep='\t',
                index=False,
                header=True,
                columns=['id', 'dur', 'name', 'depth', 'thread_dur', 'ts']
            )
            print(f"直接保存: {filename} (行数: {total_rows})")
            blocks_to_return.append(parent_block)  # 收集主块

        parent_block_num += 1

    return blocks_to_return  # 返回所有分块的 DataFrame 列表


dfs = split_into_blocks_and_save(filtered_df, path="./chunk3/")
#%% md
### 提取关键日志信息
#%%
import os
from openai import OpenAI

# 请确保您已将 API Key 存储在环境变量 WQ_API_KEY 中
# 初始化 OpenAI 客户端，从环境变量中读取您的 API Key
client = OpenAI(
    # 此为默认路径，您可根据业务所在地域进行配置
    base_url="https://wanqing-api.staging.kuaishou.com/api/agent/v1/apps",
    # 从环境变量中获取您的 API Key
    api_key="4kqyobx9g9jjixgk0xaqool9ldkc5e83qxl7"
)

# 用于保存结构化结果
results = []

# 用于保存所有模型响应的文本
all_responses = []

# 用于记录当前处理的 DataFrame 编号
df_index = 0

# 用于保存结构化结果
results = []

# 用于保存所有模型响应的文本
all_responses = []

for df_index, df in enumerate(dfs):
    print(f"\nProcessing DataFrame {df_index}")

    # 将整个 DataFrame 转换为字符串作为输入
    input_content = df.to_string(index=False)
    # 收集模型响应
    response_text = ""
    stream = client.chat.completions.create(
        model="app-msvz3y-1748316514060935132",
        messages=[
            {"role": "user", "content": input_content}
        ],
        stream=True,
        extra_body={"enable_thinking": False}
    )

    for chunk in stream:
        if chunk.choices and chunk.choices[0].delta.content:
            content = chunk.choices[0].delta.content
            response_text += content

    print(f"\n--- {df_index} End of Response ---")

    # 保存到结构化结果中
    results.append({
        'df_index': df_index,
        'response_text': response_text
    })

    # 保存到文本文件
    with open(f"./log_anaysis/response_df{df_index}.txt", "w", encoding="utf-8") as f:
        f.write(response_text)

    all_responses.append(response_text)


result_df = pd.DataFrame(results)

print("\n✅ 所有模型响应已保存为：")
print("- 每个响应保存为独立 txt 文件")

#%% md
### 调用code search接口
#%%
import os
import re
import pandas as pd
from kp import KwaiPilot

def process_txt_files(folder_path, output_folder="results"):
    os.makedirs(output_folder, exist_ok=True)
    pilot = KwaiPilot(token="DDKmVYBIF2P9aN", user_name="lvaolin")
    results = []

    pattern = re.compile(r'<think>.*?</think>', re.DOTALL) 
    
    for filename in os.listdir(folder_path):
        if filename.endswith(".txt"):
            file_path = os.path.join(folder_path, filename)
            
            with open(file_path, 'r', encoding='utf-8') as f:
                raw_content = f.read().strip()
                clean_query = pattern.sub('', raw_content).strip()
                query = clean_query if clean_query else raw_content

            try:
                answer = pilot.codeSearch(query=query)
            except Exception as e:
                answer = f"Error: {str(e)}"

            output_filename = f"result_{filename}"
            output_path = os.path.join(output_folder, output_filename)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(answer)
            
            results.append({
                "filename": filename,
                "processed_query": query,
                "answer": answer
            })

    return pd.DataFrame(results)

# 使用示例
process_txt_files(folder_path="./log_anaysis", output_folder="./code")
#%% md
### rag结果 拼接 分块信息
#%%
import os
from transformers import AutoTokenizer

def read_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read()

def truncate_text2_to_fit(text1, text2, max_tokens, tokenizer):
    # 计算固定前缀（text1 + 换行符）的token数
    prefix = text1 + '\n'
    prefix_tokens = len(tokenizer.encode(prefix, add_special_tokens=False))
    
    # 计算剩余可用token数
    remaining_tokens = max_tokens - prefix_tokens
    if remaining_tokens <= 0:
        return ""  # 无可用空间返回空字符串
    
    # 对text2进行编码并截断
    text2_tokens = tokenizer.encode(text2, add_special_tokens=False)
    allowed_tokens = text2_tokens[:remaining_tokens]
    
    # 解码回文本并清理特殊符号
    return tokenizer.decode(
        allowed_tokens, 
        skip_special_tokens=True,
        clean_up_tokenization_spaces=True
    )

def natural_sort_key(s):
    return [int(part) if part.isdigit() else part.lower() for part in re.split('(\d+)', s)]

def process_folders(folder1, folder2, max_tokens, tokenizer, output_folder=None):
    # 获取排序后的文件列表
    files1 = sorted((f for f in os.listdir(folder1) if f.endswith('.txt')), key=natural_sort_key)
    files2 = sorted((f for f in os.listdir(folder2) if f.endswith('.txt')), key=natural_sort_key)
    

    if len(files1) != len(files2):
        raise ValueError("文件夹中的txt文件数量不一致")

    # 处理每对文件
    for f1, f2 in zip(files1, files2):
        path1 = os.path.join(folder1, f1)
        path2 = os.path.join(folder2, f2)
        print(f"处理文件对: {f1}, {f2}")
        try:
            # 读取文件内容
            text1 = read_file(path1)
            text2 = read_file(path2)
            
            # 计算初始token数
            prefix = text1 + '\n'
            prefix_tokens = len(tokenizer.encode(prefix, add_special_tokens=False))
            text2_tokens = len(tokenizer.encode(text2, add_special_tokens=False))
            total_tokens = prefix_tokens + text2_tokens
            print(f"初始token数: {total_tokens}")
            # 准备输出路径
            output_path = os.path.join(output_folder, "truncated_" + f1)
            if output_folder:
                os.makedirs(output_folder, exist_ok=True)
        
            if total_tokens > max_tokens:
                # 执行截断处理
                truncated_text2 = truncate_text2_to_fit(
                    text1, text2, max_tokens, tokenizer
                )
                
                # 保存裁剪后的内容
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(truncated_text2)
                    
                print(f"已裁剪文件: {f2} (总token数: {total_tokens} → {prefix_tokens + len(tokenizer.encode(truncated_text2, add_special_tokens=False))})")
            else:
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(prefix + text2)
                
        except Exception as e:
            print(f"处理文件对 {f1}, {f2} 时出错: {str(e)}")

# 使用示例
if __name__ == "__main__":
    tokenizer = AutoTokenizer.from_pretrained(
        "Qwen/Qwen3-235B-A22B", 
        trust_remote_code=True
    )
    
    process_folders(
        folder1="./chunk3",
        folder2="./code",
        max_tokens=92000,
        tokenizer=tokenizer,
        output_folder="./truncated_results"
    )

#%% md
### 调用qwen3,测试code+火焰图
#%%
import os
from openai import OpenAI

def process_txt_files(apiKey = "ofdvapet0gfvdtnpdbr5pffd2qvn44sxt32h", input_folder = None, output_folder = None):
    # 确保输出文件夹存在
    os.makedirs(output_folder, exist_ok=True)

    # 初始化 OpenAI 客户端
    client = OpenAI(
        base_url="https://wanqing-api.staging.kuaishou.com/api/agent/v1/apps",
        api_key= apiKey
    )

    # 遍历输入文件夹中的所有 .txt 文件
    for filename in os.listdir(input_folder):
        if filename.endswith('.txt'):
            input_path = os.path.join(input_folder, filename)
            try:
                # 读取文件内容
                with open(input_path, 'r', encoding='utf-8') as file:
                    combined_text = file.read()

                # 发送流式请求
                stream = client.chat.completions.create(
                    model="app-ad7yvp-1748252835514881827",
                    messages=[{"role": "user", "content": combined_text}],
                    stream=True,
                )

                # 收集响应内容并打印到控制台
                response_content = ""
                for chunk in stream:
                    if chunk.choices:
                        content = chunk.choices[0].delta.content
                        if content:
                            response_content += content

                # 构建输出文件路径
                base_name = os.path.splitext(filename)[0]
                output_path = os.path.join(output_folder, f"{base_name}.md")

                # 保存结果到 .md 文件
                with open(output_path, 'w', encoding='utf-8') as file:
                    file.write(response_content)

                print(f"成功处理文件: {filename}")

            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")

if __name__ == "__main__":
    input_folder = "./truncated_results"
    output_folder = "./result"
    process_txt_files(input_folder=input_folder, output_folder=output_folder)
#%% md
### 合并结果
#%%
import os
from openai import OpenAI
import re

folder_path = "./results"
output_path="./result.md"

output_dir = os.path.dirname(output_path)
os.makedirs(output_dir, exist_ok=True)

results = []
pattern = re.compile(r'<think>.*?</think>', re.DOTALL)

for filename in sorted(f for f in os.listdir(folder_path) if f.endswith(".md")):
    file_path = os.path.join(folder_path, filename)
    with open(file_path, 'r', encoding='utf-8') as f:
        raw_content = f.read().strip()
        
        clean_content = pattern.sub('', raw_content).strip()
        
        if clean_content:
            results.append(f"<!-- From {filename} -->\n{clean_content}")

with open(output_path, 'w', encoding='utf-8') as f:
    f.write('\n\n'.join(results))
print(f"成功处理文件: {output_path}")

# 请确保您已将 API Key 存储在环境变量 WQ_API_KEY 中
# 初始化 OpenAI 客户端，从环境变量中读取您的 API Key
client = OpenAI(
    # 此为默认路径，您可根据业务所在地域进行配置
    base_url="https://wanqing-api.staging.kuaishou.com/api/agent/v1/apps",
    # 从环境变量中获取您的 API Key
    api_key="ofdvapet0gfvdtnpdbr5pffd2qvn44sxt32h"
)

with open("./result.md", "r") as f:
    md_content = f.read()

# Streaming:
print("----- streaming request -----")
stream = client.chat.completions.create(
    model="app-4kdpqh-1748337711643805362",  # app-4kdpqh-1748337711643805362 为您当前的智能体应用的ID
    messages=[
        {"role": "user", "content": md_content},
    ],
    stream=True,
)
# 收集响应内容并打印到控制台
response_content = ""
for chunk in stream:
    if chunk.choices:
        content = chunk.choices[0].delta.content
        if content:
            response_content += content


output_path = os.path.join("./", f"result_table.md")

# 保存结果到 .md 文件
with open(output_path, 'w', encoding='utf-8') as file:
    file.write(response_content)

print(f"成功处理文件: {output_path}")

#%%
import re

# 先读取文件内容（使用只读模式）
with open("./result_table.md", 'r', encoding='utf-8') as f:
    raw_content = f.read()

# 创建正则表达式模式并执行替换
pattern = re.compile(r'<think>.*?</think>', re.DOTALL)
cleaned_content = pattern.sub('', raw_content)

# 将处理后的内容写回文件（使用写入模式覆盖原内容）
with open("./result_table.md", 'w', encoding='utf-8') as f:
    f.write(cleaned_content.strip())
#%%
from transformers import AutoTokenizer

tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen3-235B-A22B", trust_remote_code=True)

def check_input(text):
    char_count = len(text)
    token_count = len(tokenizer.encode(text))
    
    print(f"字符数: {char_count}, token 数: {token_count / 1000:.2f}k")
    

text = open('./chunk3/block_3.txt').read()
check_input(text)
