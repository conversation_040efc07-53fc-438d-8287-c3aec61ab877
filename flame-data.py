import subprocess
import pandas as pd


def get_flame_data(file_path: str, sql: str) -> str:
    """
    直接用 --query 参数传递 SQL 语句。
    :param file_path: perfetto trace 文件路径
    :param sql: SQL 查询语句
    :return: 查询结果字符串
    """
    shell_path = './mac-arm64/trace_processor_shell'  # 可根据实际情况调整
    sql_file = 'temp_query.sql'
    with open(sql_file, 'w') as f:
        f.write(sql)
    cmd = [shell_path, '--query-file', sql_file, file_path]
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        if result.returncode != 0:
            return f"❌ 查询失败，返回码: {result.returncode}\n{result.stderr}"
        lines = result.stdout.strip().split('\n')
        data_lines = []
        for line in lines:
            line = line.strip()
            if not line:
                continue
            if ('Loading trace:' in line or
                'Trace loaded:' in line or
                'Error stats' in line or
                'name' in line and 'idx' in line and 'source' in line or
                '----' in line or
                'systrace_parse_failure' in line or
                'trace_sorter_negative_timestamp_dropped' in line):
                continue
            if ',' in line or '|' in line:
                if '|' in line:
                    parts = [p.strip() for p in line.split('|')]
                else:
                    parts = [p.strip() for p in line.split(',')]
                if len(parts) >= 6:
                    try:
                        int(parts[0])
                        data_lines.append(parts[:6])
                    except:
                        continue
        if not data_lines:
            return "❌ 未找到有效数据行"
        df = pd.DataFrame(data_lines, columns=['id', 'dur', 'name', 'depth', 'thread_dur', 'ts'])
        for col in ['id', 'dur', 'depth', 'thread_dur', 'ts']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        # 直接返回全部数据
        return df.to_string(index=False)
    except Exception as e:
        return f"❌ 执行查询时出错: {e}"

def test_get_flame_data():
    """
    测试 get_flame_data 方法。请根据实际 trace 文件路径和 SQL 语句修改参数。
    """
    trace_file = "./trace.perfetto-trace"  # 请替换为实际文件路径
    sql = '''select id, dur, name, depth, thread_dur, ts 
    from slice 
    where track_id in (11) 
    order by ts asc, depth asc'''  # 示例SQL
    result = get_flame_data(trace_file, sql)
    print("查询结果:\n", result)

# 你可以直接运行 test_get_flame_data() 进行测试
if __name__ == "__main__":
    test_get_flame_data()