#!/usr/bin/env python3
"""
完整的RAG工作流程
包含所有步骤的集成版本
"""

import os
import pandas as pd
import re
from openai import OpenAI
from kp import KwaiPilot

# 配置
API_KEY_QWEN = "4kqyobx9g9jjixgk0xaqool9ldkc5e83qxl7"  # 第一个模型的API Key
API_KEY_ANALYSIS = "ofdvapet0gfvdtnpdbr5pffd2qvn44sxt32h"  # 分析模型的API Key
API_KEY_SUMMARY = "ofdvapet0gfvdtnpdbr5pffd2qvn44sxt32h"  # 总结模型的API Key

KWAI_PILOT_TOKEN = "DDKmVYBIF2P9aN"
KWAI_PILOT_USER = "lvaolin"

def step1_extract_and_process_data():
    """步骤1: 数据提取和预处理"""
    print("=== 步骤1: 数据提取和预处理 ===")
    
    # 运行我们之前创建的脚本
    os.system("python fixed_rag_pipeline.py")
    
    # 检查结果
    if os.path.exists("./chunk3/block_1.txt"):
        print("✅ 数据预处理完成")
        return True
    else:
        print("❌ 数据预处理失败")
        return False

def step2_ai_analysis():
    """步骤2: AI分析日志信息"""
    print("\n=== 步骤2: AI分析日志信息 ===")
    
    client = OpenAI(
        base_url="https://wanqing-api.staging.kuaishou.com/api/agent/v1/apps",
        api_key=API_KEY_QWEN
    )
    
    # 获取chunk3目录中的所有文件
    chunk_files = [f for f in os.listdir("./chunk3/") if f.endswith('.txt')]
    
    os.makedirs("./log_anaysis", exist_ok=True)
    
    for i, filename in enumerate(chunk_files):
        print(f"处理文件: {filename}")
        
        # 读取文件内容
        with open(f"./chunk3/{filename}", 'r', encoding='utf-8') as f:
            input_content = f.read()
        
        try:
            # 调用AI模型
            response_text = ""
            stream = client.chat.completions.create(
                model="app-msvz3y-1748316514060935132",
                messages=[{"role": "user", "content": input_content}],
                stream=True,
                extra_body={"enable_thinking": False}
            )

            for chunk in stream:
                if chunk.choices and chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    response_text += content

            # 保存结果
            output_file = f"./log_anaysis/response_df{i}.txt"
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(response_text)
            
            print(f"✅ 保存分析结果: {output_file}")
            
        except Exception as e:
            print(f"❌ 处理 {filename} 时出错: {e}")
    
    print("✅ AI分析完成")

def step3_code_search():
    """步骤3: 代码搜索"""
    print("\n=== 步骤3: 代码搜索 ===")
    
    os.makedirs("./code", exist_ok=True)
    pilot = KwaiPilot(token=KWAI_PILOT_TOKEN, user_name=KWAI_PILOT_USER)
    
    # 处理log_anaysis目录中的文件
    pattern = re.compile(r'<think>.*?</think>', re.DOTALL)
    
    for filename in os.listdir("./log_anaysis"):
        if filename.endswith(".txt"):
            print(f"处理文件: {filename}")
            
            file_path = os.path.join("./log_anaysis", filename)
            
            with open(file_path, 'r', encoding='utf-8') as f:
                raw_content = f.read().strip()
                clean_query = pattern.sub('', raw_content).strip()
                query = clean_query if clean_query else raw_content

            try:
                answer = pilot.codeSearch(query=query)
                
                output_filename = f"result_{filename}"
                output_path = os.path.join("./code", output_filename)
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(answer)
                
                print(f"✅ 保存代码搜索结果: {output_path}")
                
            except Exception as e:
                print(f"❌ 代码搜索失败 {filename}: {e}")
    
    print("✅ 代码搜索完成")

def step4_combine_results():
    """步骤4: 合并结果"""
    print("\n=== 步骤4: 合并结果 ===")
    
    try:
        from transformers import AutoTokenizer
        tokenizer = AutoTokenizer.from_pretrained(
            "Qwen/Qwen3-235B-A22B", 
            trust_remote_code=True
        )
        print("✅ Tokenizer加载成功")
    except Exception as e:
        print(f"❌ Tokenizer加载失败: {e}")
        print("继续使用简化版本...")
        tokenizer = None
    
    # 简化版本的合并逻辑
    os.makedirs("./truncated_results", exist_ok=True)
    
    chunk_files = sorted([f for f in os.listdir("./chunk3/") if f.endswith('.txt')])
    code_files = sorted([f for f in os.listdir("./code/") if f.endswith('.txt')])
    
    for chunk_file, code_file in zip(chunk_files, code_files):
        print(f"合并: {chunk_file} + {code_file}")
        
        # 读取两个文件
        with open(f"./chunk3/{chunk_file}", 'r', encoding='utf-8') as f:
            chunk_content = f.read()
        
        with open(f"./code/{code_file}", 'r', encoding='utf-8') as f:
            code_content = f.read()
        
        # 简单合并
        combined_content = f"{chunk_content}\n\n--- 代码搜索结果 ---\n\n{code_content}"
        
        # 如果有tokenizer，可以进行截断
        if tokenizer:
            tokens = tokenizer.encode(combined_content, add_special_tokens=False)
            if len(tokens) > 90000:  # 截断到90k tokens
                truncated_tokens = tokens[:90000]
                combined_content = tokenizer.decode(truncated_tokens, skip_special_tokens=True)
                print(f"内容已截断到90k tokens")
        
        # 保存合并结果
        output_file = f"./truncated_results/truncated_{chunk_file}"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(combined_content)
        
        print(f"✅ 保存合并结果: {output_file}")
    
    print("✅ 结果合并完成")

def step5_final_analysis():
    """步骤5: 最终分析"""
    print("\n=== 步骤5: 最终分析 ===")
    
    client = OpenAI(
        base_url="https://wanqing-api.staging.kuaishou.com/api/agent/v1/apps",
        api_key=API_KEY_ANALYSIS
    )
    
    os.makedirs("./result", exist_ok=True)
    
    # 处理truncated_results中的文件
    for filename in os.listdir("./truncated_results"):
        if filename.endswith('.txt'):
            print(f"最终分析: {filename}")
            
            with open(f"./truncated_results/{filename}", 'r', encoding='utf-8') as f:
                content = f.read()
            
            try:
                response_content = ""
                stream = client.chat.completions.create(
                    model="app-ad7yvp-1748252835514881827",
                    messages=[{"role": "user", "content": content}],
                    stream=True,
                )

                for chunk in stream:
                    if chunk.choices:
                        delta_content = chunk.choices[0].delta.content
                        if delta_content:
                            response_content += delta_content

                # 保存结果
                base_name = os.path.splitext(filename)[0]
                output_path = f"./result/{base_name}.md"
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(response_content)

                print(f"✅ 保存最终分析: {output_path}")

            except Exception as e:
                print(f"❌ 最终分析失败 {filename}: {e}")
    
    print("✅ 最终分析完成")

def main():
    """主流程"""
    print("=== 完整RAG工作流程 ===\n")
    
    # 执行所有步骤
    if not step1_extract_and_process_data():
        return
    
    step2_ai_analysis()
    step3_code_search()
    step4_combine_results()
    step5_final_analysis()
    
    print("\n🎉 完整工作流程执行完成！")
    print("结果文件位于:")
    print("- ./result/ - 最终分析结果")
    print("- ./truncated_results/ - 合并的中间结果")
    print("- ./code/ - 代码搜索结果")
    print("- ./log_anaysis/ - AI分析结果")

if __name__ == "__main__":
    main()
