path:ks-components/groot-slide/kwai-slide/src/main/java/com/kwai/library/groot/slide/controller/KwaiGrootController.javacode:@NonNull
  @Override
  protected KwaiGrootPagerAdapter createPagerAdapter() {
    KwaiGrootPagerAdapter adapter;
    if (mKwaiGrootConfig.getEnableDynamicLoop()) {
      adapter = new KwaiGrootDynamicLoopPagerAdapter(
          mFragmentManager,
          mViewItemCreator,
          mKwaiGrootConfig.getDisableFirstSlideDown(),
          mKwaiGrootConfig.getDisableAddBlankItem()
      );
    } else if (mKwaiGrootConfig != null && mKwaiGrootConfig.enableOnlyUpSlide()) {
      adapter = new KwaiGrootUnidirectionalSlidePagerAdapter(mFragmentManager, mViewItemCreator);
    } else if (canLoop()) {
      adapter = new KwaiGrootLoopPagerAdapter(mFragmentManager, mViewItemCreator);
    } else if (mKwaiGrootConfig.getEnableSideProfile()) {
      adapter =
          new KwaiGrootSmoothUpdatePagerAdapter(mFragmentManager, mViewItemCreator);
    } else {
      adapter = new KwaiGrootNormalPagerAdapter(mFragmentManager, mViewItemCreator);
      GrootLogger.logInfo(TAG, mKwaiGrootConfig.getBizType() + " createPagerAdapter");
    }
    if (mKwaiGrootConfig != null) {
      if (mKwaiGrootConfig.getPollUpPrefetchThreshold() > 0) {
        adapter.setPollUpPrefetchThreshold(mKwaiGrootConfig.getPollUpPrefetchThreshold());
      }
      adapter.setAllowDuplicate(mKwaiGrootConfig.allowDuplicate());
      KwaiGrootTransactionOptConfig transactionConfig = mKwaiGrootConfig.getTransactionOptConfig();
      if (transactionConfig == null || !transactionConfig.isEnableTransactionOpt()) {
        transactionConfig =
            new KwaiGrootTransactionOptConfig(SlideFragmentTransactionExp.enableAllSlideFragmentTransactionOpt,
                SlideFragmentTransactionExp.getSlideFTOFrameInterval(), SlideFragmentTransactionExp.getSlideFTOStrategy());
      }
      adapter.setFragmentTransactionConfig(transactionConfig);
    }
    if (SlidePerfOptExp4.config.enableGrootAdapterMethodOpt) {
      adapter.setEnableMethodOpt(true);
    }
    if (mSlideAsyncTaskManager != null) {
      mSlideAsyncTaskManager.bindAdapter(adapter);
    }
    return adapter;
  }
path:ks-features/ft-x/gamezone/src/main/java/com/kuaishou/gamezone/tube/groot/GzoneQPhotoGrootController.javacode:@NonNull
  @Override
  protected KwaiGrootPagerAdapter createPagerAdapter() {
    return new GzoneKwaiGrootPagerAdapter(mFragmentManager, mViewItemCreator);
  }
path:ks-features/ft-feed/featured-detail/src/main/java/com/yxcorp/gifshow/featured/detail/featured/presenter/NasaViewPagerFragmentPresenter.javacode:Producer> taskProducers = new ArrayList<>();
      taskProducers.add(new SlideCaptionTaskProducer(getActivity()));
      SlidePreLoadRegister slidePreLoadRegister = new SlidePreLoadRegister(getActivity(), mLiveUseProfileSlideSwitch);

      mSlidePlayViewModel.initAsyncTaskManager(taskProducers, mFragment, getActivity(),
          slidePreLoadRegister);

      if (mEnableSlideInitTab) {
        mSlidePlayViewModel.registerGrootInitProgressCallback(mGrootInitProgressCallback);
      }
      mSlidePlayViewModel.initGrootSlidePlay(mDetailParam.mPhoto, mGlobalParams);
    }

    // groot 放在controller.init 之后
    mSlidePlayViewModel.setEnableLogIsDone(true);
    // 免流
    KwaiDataSourceService service =
        (KwaiDataSourceService) mSlidePlayViewModel.getGrootService(
            KwaiGrootService.KwaiGrootServiceType.DATA_SOURCE_SERVICE);
    if (service != null) {
      service.getOriginFeedFreeTrafficHelper().updateKCardPhotos(mActivity, mDetailParam.mPhoto,
          photoResponse -> mPhotoUpdatedPublisher.onNext(true), null);
    }

    // 滑滑板接口请求结束，view初始化完成认为launch end
    if (tracker != null) {
      tracker.getTabLaunchTracker().onFetchCoverSuccess(mFragment,
          PageListUtils.isCurResponseIsFromCache(mFragment), false);
    }
  }
path:ks-features/ft-x/gamezone/src/main/java/com/kuaishou/gamezone/tube/slideplay/GzoneTubeDetailActivity.javacode:private void initGrootSlidePlay() {
    mGrootDataSource =
        new GzoneKwaiGrootDataSource(mPageList, mGzoneTubeDetailParams, new Predicate<QPhoto>() {
          @Override
          public boolean apply(@Nullable QPhoto input) {
            return true;
          }
        });
    KwaiGrootConfig kwaiGrootConfig =
        new KwaiGrootConfig.Builder().setCanLoop(false)
            // 这个不生效，实际逻辑在 mController.createPagerAdapter中
            .setEnableOnlyUpSlide(false)
            .setEnableLazyLoad(true)
            .setBizType(getPage2())
            .build();
    SlidePlayViewModel.attach2Activity(this, mViewPager, mGrootDataSource);
    mController = new GzoneQPhotoGrootController(getSupportFragmentManager(),
        mGrootDataSource, mViewPager, new GzoneGrootItemCreator(mGrootDataSource) {
      @Override
      public void buildItemParams(GrootInjectItemParams grootInjectItemParams, int type) {
        super.buildItemParams(grootInjectItemParams, type);
        QPhoto photo = grootInjectItemParams.getData(QPhoto.class);
        int position = grootInjectItemParams.mPositionInAdapter;
        grootInjectItemParams.putInt(KEY_INDEX_IN_VIEW_PAGER, position);
        OldPhotoDetailParam detailParam =
            mDetailParam.cloneWithoutUnnecessaryFields();
        detailParam.mPhoto = photo;
        detailParam.mPhotoIndex = position;
        detailParam.mPhotoIndexByLog = photo.getPosition();
        DetailPlayConfig detailPlayConfig = new DetailPlayConfig();
        detailPlayConfig
            .setContinuePlayStrategy(VideoMeta.ContinuePlayStrategy.STRATEGY_DISABLE_CONTINUE_PLAY);
        detailParam.setDetailPlayConfig(detailPlayConfig);
        // 是否使用新版样式.
        mGzoneTubeDetailParams.mEnableNewLevelStyle = mPageList.isNewLevelStyle();
        grootInjectItemParams.putInt(KEY_INDEX_IN_VIEW_PAGER, position);
        grootInjectItemParams.putBoolean(KEY_PROFILE_FEED_ON, false);
        grootInjectItemParams.putString(KEY_CREATE_TYPE, CREATE_TYPE_SLIDE);
        // 上下滑创建的fragment不带入时间戳，first screen cost开始时机由用户实际滑动时触发.
        detailParam.mOpendTimeStamp = -1;
        grootInjectItemParams.putParcelable(SlidePlayParam.KEY_PHOTO, detailParam);
        grootInjectItemParams
            .putParcelable(GzoneTubeDetailActivity.KEY_TUBE_DETAIL_PARAMS, mGzoneTubeDetailParams);
        grootInjectItemParams.putString(GzoneTubeDetailActivity.FROM,
            IntentUtils.getStringExtra(GzoneTubeDetailActivity.this.getIntent(),
                GzoneTubeDetailActivity.FROM));
      }
    }, kwaiGrootConfig);
    mViewPager.setDataSource(mGrootDataSource);
    mController.init(null);
    mController.addViewItemAppearanceListener(mAttachListener);
  }
path:ks-components/photo-detail/detail-slide/src/main/java/com/kwai/component/photo/detail/slide/container/groot/GrootSlidePlayDetailBaseContainerFragment.javacode:private void initViewPager() {
    SlidePlayDataFetcher fetcher = getSlidePlayDataFetcher();
    GrootViewPager viewPager = mSlidePlayViewModel.getViewPageAs(KwaiGrootViewPager.class);
    viewPager.registerOnPageChangeObserver(new GrootPageListener());
    viewPager.setPageScrolledInterceptor(position -> {
      if (mPhotoDetailGlobalParams != null
          && mPhotoDetailGlobalParams.mPreloadPublisher != null) {
        mPhotoDetailGlobalParams.mPreloadPublisher.onNext(new PreloadInfo.Builder()
            .setPosition(position + 1)
            .build());
      }
    });
    KwaiGrootDataSource<?, QPhoto> dataSource = mSlidePlayViewModel.getGrootDataSource();
    if (dataSource.getOriginPageList() != fetcher.getOriginPageList()) {
      dataSource = createGrootDataSource(fetcher);
    }
    SlideMediaType mediaType = SlideMediaType.valueOf(fetcher.getSlideMediaType().value());
    mSlidePlayViewModel.getViewPageAs(KwaiGrootViewPager.class).setDisableShowTopTips(
        mediaType == SlideMediaType.LIVE || mediaType == SlideMediaType.ALL);
    initGroot(mSlidePlayViewModel.getViewPageAs(KwaiGrootViewPager.class),
        dataSource);
    mSlidePlayViewModel.setSlideIdRef(fetcher.id());
  }
path:ks-features/ft-live/live-features/live-common/src/main/java/com/kuaishou/live/core/show/liveslidesquare/LiveGrootSlidePlayDetailContainerFragment.javacode:@Override
  protected void initGroot(KwaiGrootViewPager kwaiGrootViewPager,
      KwaiGrootDataSource<?, QPhoto> kwaiGrootDataSource) {
    KwaiGrootConfig.Builder builder = new KwaiGrootConfig.Builder()
        .setEnableLazyLoad(false);
    builder.setEnablePullToRefresh(!mLiveBizParam.mDisablePullRefresh);
    KwaiGrootConfig config = builder.build();
    KwaiGrootController controller = mSlidePlayViewModel.createGrootController(
        getChildFragmentManager(),
        kwaiGrootDataSource,
        new LiveGrootItemCreator(mLiveBizParam, mDetailParam),
        config);
    if (!mLiveBizParam.mIsSoloLiveStream && !mLiveBizParam.mDisablePullRefresh) {
      // 下拉刷新
      controller.buildPullToRefreshDelegate(new IPullToRefreshDelegate() {
        @Override
        public boolean enablePullToRefresh() {
          return true;
        }

        @Override
        public SlidePlayRefreshView getPullToRefreshView() {
          return getView().findViewById(R.id.refresh_layout);
        }

        @Override
        public PreRefreshListener getPreRefreshListener() {
          return null;
        }
      });
    }
    if (!mLiveBizParam.mDisableLoadMore) {
      // 上拉加载更多
      controller.buildFooterLoadingDelegate(new IGrootFooterLoadingDelegate() {
        SlideViewPagerFooterLoadMore mFooter;

        @Override
        public boolean enableFooterLoading() {
          return true;
        }

        @Override
        public ISlidePlayFooter getFooterLoadingLayout() {
          if (mFooter == null) {
            mFooter = new SlideViewPagerFooterLoadMore();
            mFooter
                .setMoreView(new IMoreView() {
                  @NonNull
                  @Override
                  public View getView() {
                    return mSlidePlayFooterLoadingLayout;
                  }

                  @Override
                  public void onShow() {
                  }

                  @Override
                  public void onHide() {
                  }

                  @Override
                  public void onTranslationY(float v) {
                  }
                });
          }
          return mFooter;
        }
      });
    }
    mSlidePlayViewModel.initGrootSlidePlay(mDetailParam.mPhoto, mPhotoDetailGlobalParams);
    // groot 放在controller.init 之后
    mSlidePlayViewModel.setEnableLogIsDone(true);
  }
path:ks-features/ft-feed/featured-detail/src/main/java/com/yxcorp/gifshow/featured/detail/featured/presenter/NasaViewPagerFragmentPresenter.javacode:@SuppressWarnings("MethodCyclomaticComplexity")
  private void initViewPager() {
    if (mRefreshView.isRefreshing()) {
      mRefreshView.setRefreshing(false);
    }
    if (mSlidePlayViewModel.getPageListCount() == 0) {
      return;
    }
    mHasInitViewPager = true;

    mDetailParam.mPhoto = mSlidePlayViewModel.getGrootDataSource().get(0);

    // 闪播阶段初始化的精选上下滑， 要和闪播共用一个 QPhotoSessionKeyGen
    QPhotoSessionKeyGen quickSilverSessionKeyGen = null;
    Object needForQuickSilver =
        QuickSilverViewModel.isNeedForQuickSilver(mFragment, mDetailParam.mPhoto.getPhotoId());
    if (needForQuickSilver instanceof QPhotoSessionKeyGen) {
      quickSilverSessionKeyGen = (QPhotoSessionKeyGen) needForQuickSilver;
    }
    if (quickSilverSessionKeyGen != null) {
      KLogFeaturedDetail.get().i("Quick_Silver_initViewPager",
          "use quick_silver SessionKeyGen=" + quickSilverSessionKeyGen);
      mDetailParam.getDetailPlayConfig()
          .setSharedPlaySessionKeyGeneratorInternal(quickSilverSessionKeyGen);
    } else {
      // 希望统一对getDetailPlayConfig 置入默认的属性，不知道写在哪里。有更好的地方随时修改。
      KLogFeaturedDetail.get().i("Quick_Silver_initViewPager",
          "use normal SessionKeyGen");
      mDetailParam.getDetailPlayConfig()
          .setSharedPlaySessionKeyGeneratorInternal(new QPhotoSessionKeyGen());
    }

    LaunchTracker tracker = Singleton.get(LaunchTracker.class);
    if (tracker != null) {
      tracker.getTabLaunchTracker().onFetchCoverStart(mFragment,
          PageListUtils.isCurResponseIsFromCache(mFragment));
    }
    // groot nasaBizParam 通过 controller 中的 injectParams 传入
    // ((NasaSlideViewPager) mSlidePlayViewModel.getViewPager()).setNasaBizParam(nasaBizParam);

    // groot 不需要
    // mSlidePlayViewModel.attachFragmentToViewPager(mFragment);

    // groot 通过 controller 中 buildPullToRefreshDelegate() 构造
    // mSlidePlayViewModel.setPreRefreshListener(this::onPreRefresh);

    // groot 通过 KwaiGrootConfig 构造
    // mSlidePlayViewModel.setRetryCountInLastPage(ThanosABUtils.RETRY_COUNT_WHEN_IN_LAST_PAGE
    // .get());

    // 预取 懒加载第二个第三个fragment
    mNeedPrefetchLazyFragment = needLazyFragmentByPrefetcher();
    if (mNeedPrefetchLazyFragment) {
      initLazyFragment();
    } else {
      if (SecondPhotoLazyInitHelper.enableLazyFragmet()) {
        mSecondPhotoLazyInitHelper.scheduleRunInit("NasaViewPagerFragmentPresenter", true);
      } else if (LowPhoneUtils.enableSlidePlaySecondPhotoLazyLayout()) {
        mSecondPhotoLazyInitHelper.scheduleRunInit("NasaViewPagerFragmentPresenter", false);
      }
    }
    NasaGrootItemCreator nasaGrootItemCreator =
        new NasaGrootItemCreator(mNasaBizParam, mDetailParam, mDetailParam.mPhoto, mActivity);
    if (mPreCreatedDetailFragment != null) {
      nasaGrootItemCreator.mPreCreatedDetailVMFragment = mPreCreatedDetailFragment;
    }
    // 首几个作品页打散策略。
    if (enableForceUnDispatchFirst()) {
      nasaGrootItemCreator.setForceUnDispatchStrategy(SlideDispatchHelper.enableForceUnDispatch());
    }
    KwaiGrootController controller = mSlidePlayViewModel.createGrootController(
        mFragment.getChildFragmentManager(), null,
        nasaGrootItemCreator,
        kwaiGrootConfig);
    // 支持下拉刷新
    if (!mNasaBizParam.getNasaSlideParam().mForceDisablePullToRefresh) {
      controller.buildPullToRefreshDelegate(new NasaPullToRefreshDelegate());
    }
    // 支持上拉加载更多
    controller.buildFooterLoadingDelegate(new NasaGrootFooterLoadingDelegate());
    // 保护一下模式切换，fragment/activity 已经不存在的问题
    if (getActivity() != null && !getActivity().isFinishing()) {
      List<TaskProducer> taskProducers = new ArrayList<>();
      taskProducers.add(new SlideCaptionTaskProducer(getActivity()));
      SlidePreLoadRegister slidePreLoadRegister = new SlidePreLoadRegister(getActivity(), mLiveUseProfileSlideSwitch);

      mSlidePlayViewModel.initAsyncTaskManager(taskProducers, mFragment, getActivity(),
          slidePreLoadRegister);

      if (mEnableSlideInitTab) {
        mSlidePlayViewModel.registerGrootInitProgressCallback(mGrootInitProgressCallback);
      }
     