path:ks-features/ft-feed/detail/src/com/yxcorp/gifshow/detail/slideplay/nasa/groot/vm/NasaPhotoDetailFragment.javacode:private void bindSlidePage() {
    beforeBindPage();
    SlidePlayMonitorLogger.log(TAG, "onActivityCreated", "mSlidePage.bind start");
    performTrackStage(STAGE_BIND_SLIDE_PAGE, true);
    mSlidePage.bind();
    performTrackStage(STAGE_BIND_SLIDE_PAGE, false);
    SlidePlayMonitorLogger.log(TAG, "onActivityCreated", "mSlidePage.bind end");
  }
path:ks-features/ft-feed/detail/src/com/yxcorp/gifshow/detail/common/controller/CoronaPlayPayPauseElement.ktcode:override fun onBindData(callerContext: SlidePageCallerContext) {
    super.onBindData(callerContext)
    mFragment = callerContext.mFragment
    mActivity = callerContext.mActivity
    mPlayModule = callerContext.mPlayModule
    mFragmentLocalBus = callerContext.mFragmentLocalBus
    mPhoto = callerContext.mPhotoDetailParam.mPhoto
    mDetailPlayConfig = callerContext.mPhotoDetailParam.detailPlayConfig
    mShouldLogElementShow = true
    if (callerContext.mPhotoDetailGlobalParams != null) {
      mSmallWindowProgressObservable =
        callerContext.mPhotoDetailGlobalParams.mSmallWindowProgressEmitter
      mMilanoContainerEventBus = callerContext.mPhotoDetailGlobalParams.mMilanoContainerEventBus
    }
    mPlayerListenerBundle = callerContext.mPlayInfoListenerBundle
    mContentFrameExecutor =
      mFragment.view?.findViewById<KwaiPlayerKitView>(R.id.slide_playerkit_view)?.
      playerKitContext?.getExecutor(ContentFrameExecutor::class.java)
  }
path:ks-features/ft-feed/detail/src/com/yxcorp/gifshow/detail/common/information/plc/BasePlcStyleElement.ktcode:override fun onBindData(callerContext: SlidePageCallerContext) {
    mValidShouldShow = true
    mShouldShow = callerContext.mPlcSlideContext.plcShouldShow
    mPhoto = callerContext.mPhotoDetailParam.mPhoto
    mPhotoDetailParam = callerContext.mPhotoDetailParam
    mPlcLandscapeToken = callerContext.mPlcSlideContext.plcLandscapeToken
    mPlcInfo = mPhoto?.plcEntryStyleInfo
    mPlcPerfLogger = callerContext.mPlcSlideContext.plcPerfLogger
    mPlcLinkRecoLogger = callerContext.mPlcSlideContext.plcLinkRecoLogger
    mFragment = callerContext.mFragment
    mFragmentLocalBus = callerContext.mFragmentLocalBus
    mPlcAsyncRefreshManager = callerContext.mPlcSlideContext.plcAsyncRefreshManager
    mDetailPlayModule = callerContext.mPlayModule
    mSelectFrameUiInterface = callerContext.mSlidePlayCallerContext.mSelectFrameUiInterface
    mPhotoDetailLogger = callerContext.loggerProvider
  }
path:ks-features/ft-feed/detail/src/com/yxcorp/gifshow/detail/common/controller/BasePlayPauseElement.ktcode:override fun onBindData(callerContext: SlidePageCallerContext) {
    mCallerContext = callerContext
    mLogListener = callerContext.mLogListener
    mScreenCleanStatusCombination = callerContext.mScreenCleanStatusCombination
    mSwipeToProfileFeedMovement = callerContext.mPhotoDetailGlobalParams.mSwipeToProfileFeedMovement
    mPhoto = callerContext.mPhotoDetailParam.mPhoto
    mFragment = callerContext.mFragment
    mHasAudio = hasAudio(mPhoto)
    mPlayModule = callerContext.mPlayModule
    mDetailPlayConfig = callerContext.mPhotoDetailParam.detailPlayConfig
    mGrootEventBus = callerContext.mFragmentLocalBus
    mPlayerListenerBundle = callerContext.mPlayInfoListenerBundle
    mProgressbarDragEvent = callerContext.mPhotoDetailGlobalParams
        .mMilanoContainerEventBus.mProgressbarDragEvent
    mSlidePlayGlobalCache = callerContext.mPhotoDetailGlobalParams.mSlidePlayGlobalCache
  }
path:ks-components/slide-play-detail-framework/src/main/java/com/kwai/slide/play/detail/base/component/BaseComponent.ktcode:final override fun bind() {
    KLogDetailFramework.get().e("BaseComponent", "${componentId()} : bind start isBind = $isBind")
    if (isBind) {
      throw Exception("Already bind")
    }
    perfLogger?.markStart(PerfLoggerType.COMPONENT_TYPE, componentId(), "bind")
    onBind()
    refreshSynchronizer.reset()
    try {
      elementOrderList.forEach {
        it.isRealShow = false
        it.hasBlockedShow = false
        it.bind(pageContext.callerContext, refreshSynchronizer)
      }
    } catch (e: Exception) {
      KLogDetailFramework.get()
        .e("BaseComponent", "${componentId()} : bind elementOrderList throw Exception ", e)
      throw e
    }
    isBind = true
    if (lazyNotifyShowEvents.isNotEmpty()) {
      lazyNotifyShowEvents.forEach { notifyRelatedElement(it.key, it.value) }
      lazyNotifyShowEvents.clear()
    }
    if (elementOrderList.isNotEmpty()) {
      refreshSynchronizer.syncDisplay {
        refreshAll()
      }
    }
    perfLogger?.markEnd(PerfLoggerType.COMPONENT_TYPE, componentId(), "bind")
  }
path:ks-features/ft-feed/detail/src/com/yxcorp/gifshow/detail/common/information/plc/BasePlcWeakStyleElement2.javacode:@Override
  protected void onBindData(SlidePageCallerContext callerContext) {
    super.onBindData(callerContext);
    mPhotoDetailGlobalParams = callerContext.mPhotoDetailGlobalParams;
    mActivity = callerContext.mActivity;
    mPLCLogHelper = callerContext.mPlcSlideContext.getPlcLogHelper();
    mFragment = callerContext.mFragment;
    mPlayInfoListenerBundle = callerContext.mPlayInfoListenerBundle;
    mFragmentLocalBus = callerContext.mFragmentLocalBus;
    mPlcDataFetchedPublisher = callerContext.mPlcDataFetchedPublisher;
    if (mPLCLogHelper != null) {
      mPlcEntryLoggerInterface = mPLCLogHelper.getPlcEntryLogger();
    }
    if (mPhoto != null) {
      mPlcEntryStyleInfo = mPhoto.getPlcEntryStyleInfo();
    }
    mPlcViewInteractiveController =
      callerContext.mPlcSlideContext.getPlcViewInteractiveController();
    if (mPlcViewInteractiveController != null) {
      mPlcViewInteractiveController.initializeWithRefresh(mPhoto, mPlcEntryStyleInfo, mPlcAction,
        mPlcAsyncRefreshManager);
    }
    if (callerContext.mPlayModule != null) {
      mPlayer = callerContext.mPlayModule.getPlayer();
    }
    addToAutoDisposes(
      RxBus.INSTANCE.toObservable(PlcCoupleWeakCloseClickEvent.class)
        .filter(
          plcCoupleWeakCloseClickEvent -> plcCoupleWeakCloseClickEvent.getPhoto() == mPhoto)
        .observeOn(KwaiSchedulers.MAIN)
        .subscribe(plcCoupleWeakCloseClickEvent -> {
          if (mPlcViewInteractiveController != null) {
            TunaPlcLogger.i(TAG, "receive couple weak close click!");
            mPlcViewInteractiveController.onCoupleWeakClose();
          }
        }, throwable -> {
          PlcExceptionHandler.handleException(
            new PlcExceptionBuilder()
              .setThrowable(throwable)
              .setCode(PlcMonitorConstants.PlcProcessState.LOGIC)
              .setMessage("plc strong handle weak close click crash!")
              .build(), TAG);
          Functions.ERROR_CONSUMER.accept(throwable);
        })
    );
  }
path:ks-applications/kwai-android/src-perf-feed/yxcorp/gifshow/apm/SysTraceHelper.javacode:@Pointcut("execution(* com.yxcorp.gifshow.homepage.nasa..*(..))"
      + "|| execution(* com.yxcorp.gifshow.danmaku..*(..))"
      + "|| execution(* com.kwai.library.groot.framework..*(..))"
      + "|| execution(* com.kwai.library.groot.api..*(..))"
      + "|| execution(* com.kwai.library.groot.slide..*(..))"
      + "|| execution(* com.kwai.sdk.rerank..*(..))"
      + "|| execution(* com.kwai.framework.rerank..*(..))"
      + "|| execution(* com.kuaishou.preloader..*(..))"
      + "|| execution(* com.yxcorp.sdk.resource_preloader..*(..))"
      + "|| execution(* com.gifshow.kuaishou.preloader..*(..))"
      + "|| execution(* com.yxcorp.gifshow.nasa..*(..))"
      + "|| execution(* com.yxcorp.gifshow.detail.presenter..*(..))"
      + "|| execution(* com.yxcorp.gifshow.detail.slidev2..*(..))"
      + "|| execution(* com.yxcorp.gifshow.detail.slideplay..*(..))"
      + "|| execution(* com.kwai.video.player..*(..))"
      + "|| execution(* com.yxcorp.gifshow.featured.detail..*(..))"
      + "|| execution(* com.kwai.component.photo.detail.slide..*(..))"
      + "|| execution(* com.kwai.slide.play.detail..*(..))"
      + "|| execution(* com.kwai.library.slide.base..*(..))"
      + "|| execution(* com.kwai.library.groot..*(..))"
      + "|| execution(* com.yxcorp.gifshow.KwaiApp.on*(..))"
      + "|| execution(com.yxcorp.gifshow.init.KwaiInitConfigSupplier+.new(..))"
      + "|| execution(* com.yxcorp.gifshow.init.KwaiInitConfigSupplier.*(..))"
      + "|| execution(* com.yxcorp.gifshow.events.eventbus.KwaiEventBusInstaller.install(..))"
      + "|| execution(* com.smile.gifmaker.mvps.presenter.PresenterV2.create(..))"
      + "|| execution(* com.smile.gifmaker.mvps.presenter.PresenterV2.moveToState(..))"
      + "|| execution(* com.smile.gifmaker.mvps.presenter.PresenterV2.callEntryAction(..))"
      + "|| execution(* com.smile.gifmaker.mvps.presenter.PresenterV2.callOnState(..))"
      + "|| execution(* com.smile.gifmaker.mvps.presenter.PresenterV2.createInternal(..))"
      + "|| execution(* com.smile.gifmaker.mvps.presenter.PresenterV2.bindInternal(..))"
      + "|| execution(* android.app.Service+.on*(..))"
      + "|| execution(* android.content.BroadcastReceiver+.onReceive(..))"
      + "|| execution(android.view.View+.new(..))"
      + "|| execution(* android.view.View+.on*(..))"
      + "|| execution(* java.lang.Runnable+.run(..))"
      + "|| execution(* java.lang.Thread+.run(..))"
      + "|| execution(void *..lambda*(..))" // lambda 表达式，只能这样全部处理
      + "|| execution(* android.content.ServiceConnection+.on*(..))"
      + "|| execution(* android.app.job.JobService+.on*(..))"
      + "|| execution(* com.yxcorp.utility.AppImmersiveUtils.startImmersiveMode(..))"
      + "|| execution(* com.kwai.framework.activitycontext.ActivityContext.on*(..))"
      + "|| execution(* com.yxcorp.gifshow.activity.GifshowActivity.on*(..))"
      + "|| execution(* com.yxcorp.gifshow.init.InitManagerImpl.on*(..))"
      + "|| execution(com.yxcorp.gifshow.recycler.TipsHelper+.new(..))"
      + "|| execution(* androidx.core.content.FileProvider.attachInfo(..))"
      + "|| execution(* com.yxcorp.utility.singleton.Singleton.get(..))"
      + "|| execution(com.smile.gifmaker.mvps.presenter.PresenterV2+.new(..))"
      + "|| execution(com.kwai.framework.init.InitModule+.new(..))"
      + "|| execution(* com.kwai.framework.init.InitModule+.*(..))"
      + "|| execution(* com.yxcorp.gifshow.recycler.fragment.BaseFragment+.*(..))" // 底导模式下部分首页的Fragment方法没能在systrace上监控
      + "|| execution(* com.yxcorp.gifshow.recycler.fragment.RecyclerFragment+.*(..))"
      + "|| execution(* com.kwai.framework.network.KwaiParams.*(..))"
      + "|| execution(*  com.yxcorp.gifshow.nasa.featured.*(..))"
      + "|| execution(*