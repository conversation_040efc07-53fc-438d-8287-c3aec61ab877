path:ks-features/ft-feed/detail/src/com/yxcorp/gifshow/detail/fps/NasaFrameRatePresenter.javacode:private void observeEvent() {
    if (hasObserve) {
      return;
    }
    mSlidePlayViewModel = SlidePlayViewModel.get(mFragment);
    mViewPager = mSlidePlayViewModel.getViewPageAs(GrootViewPager.class);
    //默认没选中
    setOperateFlag(OperateFlags.SELECTED);
    mFragment.getLifecycle().addObserver(mFragmentLifecycleObserver);
    mViewPager.registerPreCallOnPageChangeObserver(mPreCallOnPageChangeListener);
    mSlidePlayViewModel.addViewItemLifecycleListener(mViewItemAppearanceChangedListener);
    initPhoto();
    mSwipeToProfileFeedMovement.addSidebarStatusListener(mSidebarStatusListener);
    if (mFragment instanceof KCubeTabFragmentInterface) {
      ((KCubeTabFragmentInterface) mFragment).eventGroup().addOnScrollStateChangedListener(mOnScrollStateChangedListener);
      ((KCubeTabFragmentInterface) mFragment).eventGroup().addSelfSelectedStateListener(mOnSelfSelectedStateListener);
    }
    if (mScreenMilanoProtocol != null) {
      mScreenMilanoProtocol.addOnConfigurationListener(mMilanoConfigurationChangedListener);
    }
    observeDanmakuSwitch();
    observeCommentPannel();
    observeForwardActionPannel();
    observeMediaFpsPannel();
    observeSmoothSwipeState();
    observeTrendingPannel();
    observeMenuPanelSlide();
    observeSerialPanelState();
    observeDoubleTapLikeState();
    observeOperationPanel();
    if (BuildConfig.DEBUG) {
      mDisplayManager = (DisplayManager) AppEnv.APP.getSystemService(Context.DISPLAY_SERVICE);
      mDefaultDisplay = mDisplayManager.getDisplay(Display.DEFAULT_DISPLAY);
      mDisplayManager
          .registerDisplayListener(mDisplayListener, new Handler(Looper.getMainLooper()));
    }
    hasObserve = true;
  }
path:ks-features/ft-commercial/commercial-splash/src/main/java/com/kuaishou/commercial/splash/SplashTopPhotoInsertPresenter.javacode:@Override
  protected void onCreate() {
    super.onCreate();
    if (mSafeLockDisposable == null) {
      mSafeLockDisposable = RxBus.INSTANCE.toObservable(SafeLockEvent.class, RxBus.ThreadMode.MAIN)
          .subscribe(this::onSafeLockEvent);
    }
    if (mSplashEyemaxEnterDetail == null) {
      mSplashEyemaxEnterDetail =
          RxBus.INSTANCE.toObservable(SplashEyemaxEnterDetail.class).observeOn(KwaiSchedulers.MAIN)
              .subscribe(this::onHomeSplashStateEvent);
    }
    if (mHomeSplashStateEvent == null) {
      if (Singleton.get(SplashDataManager.class).shouldEyemaxInsertNotifyEarlier()) {
        mHomeSplashStateEvent =
            RxBus.INSTANCE.toObservable(HomeSplashStateEvent.class, RxBus.ThreadMode.MAIN)
                .subscribe(this::onHomeSplashStateEvent);
      } else {
        mHomeSplashStateEvent =
            RxBus.INSTANCE.toObservable(HomeSplashStateEvent.class).observeOn(KwaiSchedulers.MAIN)
                .subscribe(this::onHomeSplashStateEvent);
      }
    }
    if (mSplashTopRemoveEvent == null) {
      mSplashTopRemoveEvent =
          RxBus.INSTANCE.toObservable(SplashTopRemoveEvent.class).observeOn(KwaiSchedulers.MAIN)
              .subscribe(this::onEyemaxRemoved);
    }
    if (mSplashEyemaxShowEvent == null) {
      mSplashEyemaxShowEvent =
          RxBus.INSTANCE.toObservable(EyemaxSplashShowEvent.class, RxBus.ThreadMode.MAIN)
              .subscribe(this::onEyemaxShowed);
    }
    if (mHotEyemaxSplashEvent == null && SplashSwitchUtils.enableHotEyemax()) {
      mHotEyemaxSplashEvent =
          RxBus.INSTANCE.toObservable(HotEyemaxSplashEvent.class)
              .observeOn(KwaiSchedulers.MAIN)
              .subscribe(this::onHotEyemaxSplashEvent);
    }
    if (mSplashFinishByConvertedEvent == null) {
      mSplashFinishByConvertedEvent =
          RxBus.INSTANCE.toObservable(SplashFinishByConvertedEvent.class)
              .observeOn(KwaiSchedulers.MAIN)
              .subscribe(this::onSplashFinishByConvertedEvent);
    }
  }
path:ks-features/ft-feed/detail/src/com/yxcorp/gifshow/detail/slidev2/serial/presenter/NasaDetailSerialLoadPrePresenter.javacode:@Override
  public boolean handleData(boolean firstPage,List<QPhoto> dataList) {
    KLogDetail.get().i(LOG_TAG, "onNasaLoadingPageSuccess isFirstPage："
        + firstPage);
    if (CollectionUtils.isEmpty(dataList)) {
      KLogDetail.get().i(LOG_TAG, "onNasaLoadingPageSuccess param invalid");
      return false;
    }

    // 当前选中的QPhoto
    QPhoto currentPhoto = mSlidePlayViewModel.getCurrentPhoto();

    if (!dataList.contains(currentPhoto)) {
      // 同一合集按理说不应该出现这个情况，相关合集（非同一合集）会走到这
      if (serialType == SerialAdapter.SerialItemType.RELATED_SERIAL) {
        currentPhoto = relatedPhoto;
      }
      KLogDetail.get().i(LOG_TAG, "onNasaLoadingPageSuccess no current photo");
    }

    KLogDetail.get().i(LOG_TAG, "onNasaLoadingPageSuccess index:", dataList.indexOf(currentPhoto));
    KLogDetail.get().i(LOG_TAG, "onNasaLoadingPageSuccess size:", dataList.size());
    //商业化插入Story开关,在短剧合集中会插入多个商业化硬广
    if (PluginManager.get(CommercialPlugin.class).enableReplaceCurPhoto()) {
      //由于grootSource允许重复,currentPhoto不是dataList中的对象时GrootUtils.indexOf返回-1,
      // replaceAllItems后mFeedMilanoProtocol.currentPhoto并不是currentPhoto
      if (CoronaSerialUtil.isTubeSerial(currentPhoto)) {
        int selectIndex = dataList.indexOf(currentPhoto);
        if (selectIndex >= 0) {
          //修复携带feed时，首次进入合集不能自动播放下一个，
          //原因是BaseAutoPlayPresenter.hasNextPhoto为空，mPhoto不在list中
          if (PluginManager.get(CommercialPlugin.class).enableFixSerialAutoPlay()) {
            dataList.set(selectIndex, currentPhoto);
          } else {
            currentPhoto = dataList.get(selectIndex);
          }
        }
      }
    }
    // 差分刷新列表
    mSlidePlayViewModel.replaceAllItems(dataList, currentPhoto, false, LOG_TAG);
    return true;
  }
path:ks-features/ft-feed/kwai-growth/src/main/java/com/yxcorp/gifshow/growth/autoplay/GrowthRefluxAutoPlayPresenter.ktcode:private fun initDisposables() {
    // 监听提取文案面板
    addToAutoDisposes(RxBus.INSTANCE.toObservable(QcrPanelEvent::class.java)
      .observeOn(KwaiSchedulers.MAIN)
      .subscribe { qcrPanelEvent: QcrPanelEvent ->
        handleQcrPanelEvent(qcrPanelEvent)
      }
    )

    // 监听评论输入面板
    addToAutoDisposes(RxBus.INSTANCE.toObservable(EmotionFloatEditorEvent::class.java)
      .observeOn(KwaiSchedulers.MAIN)
      .subscribe { event: EmotionFloatEditorEvent ->
        handleEmotionFloatEditorEvent(event)
      }
    )

    // 监听图片下载面板
    addToAutoDisposes(RxBus.INSTANCE.toObservable(DownloadPicDialogEvent::class.java)
      .observeOn(KwaiSchedulers.MAIN)
      .subscribe { event: DownloadPicDialogEvent ->
        handleDownloadPickEvent(event)
      }
    )

    /**
     * 评论面板
     *
     * 存在的问题：通知不太准确，评论面板收起，animStart就通知了隐藏。如果在评论动画期间切换作品，会导致小窗无法正常收起。
     * 评论动画结束后才会设置ViewPager可滚动，所以目前依赖于在[currentSupportAutoPlay]中
     * 通过[SlidePlayViewModel.isViewPagerOperationProhibited]#兜底避免此问题
     */
    addToAutoDisposes(
      mFragmentLocalBus.observable(DetailEventId.DETAIL_SLIDE_COMMENT_FRAGMENT_VISIBLE)
        .subscribe { isVisible: Boolean ->
          setDisableAutoPlay(isVisible, AutoPlayStopType.STOP_BY_COMMENT)
        }
    )
    addToAutoDisposes(
      mFragmentLocalBus.observable(DetailEventId.DETAIL_SLIDE_AI_TEXT_FRAGMENT_VISIBLE)
        .subscribe { isVisible: Boolean ->
          setDisableAutoPlay(isVisible, AutoPlayStopType.STOP_BY_AI_TEXT)
        }
    )

    // 监听侧边栏
    (activity as? FragmentActivity)?.let { fragmentActivity ->
      addToAutoDisposes(MenuViewModel.get(fragmentActivity).openState
        .subscribe { open: Boolean ->
          if (mIsAttached) {
            setDisableAutoPlay(open, AutoPlayStopType.STOP_BY_SLIDE_MENU)
          }
        }
      )
    }

    // 监听弹幕输入面板
    addToAutoDisposes(mBarrageKitWrapper.getDanmakuKitObservable(false, false, null)
      .observeOn(KwaiSchedulers.MAIN)
      .subscribe { danmakuKit: DanmakuKit ->
        mDanmakuKit = danmakuKit
        danmakuKit.addCallback(mDanmakuCallback)
      }
    )

    // 监听清屏状态
    addToAutoDisposes(mNasaScaleCleanControllerShowObservable
      .subscribe { scaleCleanEvent: ScaleCleanControllerShowEvent ->
        handleScaleCleanEvent(scaleCleanEvent)
      }
    )
  }
path:ks-features/ft-follow/follow-slide/src/main/java/com/yxcorp/gifshow/follow/slide/detail/presenter/FollowAutoPlayNextPresenter.ktcode:override fun onBind() {
    KsLogFollowProxy.i(KsLogFollowTag.FOLLOW_NIRVANA.appendTag(TAG), "onBind")
    mSidebarStatusHelper?.addDetailSidebarStatusListener(mSidebarStatusListener)
    mSlidePlayViewModel?.addViewItemLifecycleListener(mItemAppearanceChangedListener)
    mDetailPlayModule.player.addOnInfoListener(mOnInfoListener)
    // 监听评论面板
    addToAutoDisposes(
      mFragmentLocalBus.observable(DetailEventId.DETAIL_SLIDE_COMMENT_FRAGMENT_VISIBLE)
        .subscribe {
          mIsCommentPanelShowing = it
        })

    // 监听AI文稿面板
    addToAutoDisposes(
      mFragmentLocalBus.observable(DetailEventId.DETAIL_SLIDE_AI_TEXT_FRAGMENT_VISIBLE)
        .subscribe {
          mIsAiTextPanelShowing = it
        })

    // 监听提取文案面板
    addToAutoDisposes(
      RxBus.INSTANCE.toObservable(QcrPanelEvent::class.java)
        .observeOn(KwaiSchedulers.MAIN)
        .subscribe { qcrPanelEvent: QcrPanelEvent ->
          mIsQcrPanelShowing = qcrPanelEvent.mStatus == QcrPanelEvent.DIALOG_SHOW
        })

    // 监听评论输入面板
    addToAutoDisposes(RxBus.INSTANCE.toObservable(EmotionFloatEditorEvent::class.java)
      .observeOn(KwaiSchedulers.MAIN)
      .subscribe { event: EmotionFloatEditorEvent ->
        mIsEmotionFloatEditorShowing = event.mStatus == EmotionFloatEditorEvent.DIALOG_SHOW
      }
    )

    // 监听图片下载面板
    addToAutoDisposes(RxBus.INSTANCE.toObservable(DownloadPicDialogEvent::class.java)
      .observeOn(KwaiSchedulers.MAIN)
      .subscribe { event: DownloadPicDialogEvent ->
        mIsDownloadPicDialogShowing = event.mEvent == DownloadPicDialogEvent.PIC_DIALOG_SHOW
      }
    )
    addToAutoDisposes(RxBus.INSTANCE.toObservable(RewardPanelShownEvent::class.java)
        .subscribe { rewardPanelShownEvent: RewardPanelShownEvent ->
          mIsRewardPanelShowing = rewardPanelShownEvent.showed
        })
  }
path:ks-features/ft-live/live-features/live-common/src/main/java/com/kuaishou/live/core/show/statistics/LiveTaskStatisticsPresenter.javacode:@Override
  protected void onBind() {
    super.onBind();
    addToAutoDisposes(
        RxBus.INSTANCE.toObservable(FollowUpdateEvent.class).observeOn(KwaiSchedulers.MAIN).subscribe(this::onFollowUpdateEvent));
    addToAutoDisposes(mLivePlayCallerContext.mStartRenderVideoTimestampMsSubject
        .filter(videoTimeStamp -> videoTimeStamp > 0)
        .subscribe(videoTimeStamp -> mTaskManager.onLiveStreamStart(
            getActivity(),
            mLivePlayCallerContext.mPhoto.mEntity,
            mRewardedUserIdSet),
            Functions.ERROR_CONSUMER));
    mLivePlayCallerContext.mLiveEndService.addLiveStopListener(mLiveStopListener);
    mLivePlayCallerContext.mLiveBasicContext.getFragmentLifeCycleService()
        .addObserver(mFragmentLifecycleCallbacks);
    mSideBarMovement.addPhotoFeedSideBarAnimatorListener(mSideBarAnimatorListener);
    addToAutoDisposes(RxBusUtils.listenInMainThread(LiveTaskFollowPkAnchorEvent.class,
        event -> handleLiveTaskFollowPkAnchorEvent(event)));
    mLivePlayCallerContext.mSlidePlayService.addSlidePlayListener(mLiveSlidePlayListener);
    addToAutoDisposes(RxBusUtils.listenInMainThread(TrustCardTaskEvent.class,
        event -> mTaskManager.startInitiativeBrowseLiveTask(event.mTaskWidgetParams,
            mLivePlayCallerContext.mPhoto.mEntity, (GifshowActivity) getActivity(),
            mRewardedUserIdSet)));
  }
path:ks-features/ft-live/live-features/live-common/src/main/java/com/kuaishou/live/common/core/component/pendant/topright/LiveTopRightTkPendantPresenter.javacode:);
      LiveDebugLoggerV2.i(TAG, traceHeader + "[handlePendantMessage] config is null");
      return;
    }
    if (msg.canShow) {
      config.mTraceId = traceId;
      if(enableAutoClose) {
        tryShowPendant(config, msg.showDeadline);
      }
      else{
        tryShowPendant(config);
      }
    } else {
      LiveDebugLoggerV2.i(TAG, builderTraceHeader(tkPendantTraceInfo.traceId,
          LiveTkPendantStage.REMOVE) + "[removePendantAndCancelCountDown]", "canShow = ", false);
      report(LiveTopRightTkPendantReporter.WIDGET_OPT,
              LiveTopRightTkPendantReportCode.WIDGET_REMOVE,
              config.mBundleId,
              config.mBizId,
              config.mActivityId, "canShow = false，移除挂件，挂件id=" + config.mId, null);
      removePendantAndCancelCountDown(config.mId);
    }
  }