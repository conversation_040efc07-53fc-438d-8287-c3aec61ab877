path:ks-components/user-base-function/src/main/java/com/kwai/user/base/chat/target/bean/BehaviorPublisherAndSyncable.javacode:public Observable<T> observeOnMain(boolean emitLast, boolean lastOnCurThread) {
    if (emitLast) {
      // 解决当前方法在主线程调用，立刻刷新UI状态，避免切主线程闪动的问题
      if (lastOnCurThread) {
        // 原线程中发射一次，还会再往主线程发射一次
        return Observable.merge(
            Observable.just((T) this),
            mDefaultObservable.mPublisher.observeOn(KwaiSchedulers.MAIN)
        );
      } else {
        return Observable.merge(
            Observable.just((T) this),
            mDefaultObservable.mPublisher.hide()
        ).observeOn(KwaiSchedulers.MAIN);
      }
    } else {
      return mDefaultObservable.mPublisher
          .observeOn(KwaiSchedulers.MAIN);
    }
  }
path:ks-features/ft-live/live-features/live-effect/src/main/java/com/kuaishou/live/effect/resource/download/common/LiveMagicGiftDownloadController.javacode:private boolean hasDownloadMagicGift(MagicEmoji.MagicFace magicGift, boolean checkFileExistence) {
    // 尝试修复ANR问题:
    // https://keep.corp.kuaishou.com/#/exception-analysis/block/android/anr/detail?pid=1&md5
    // =8a182aa6eb4b5a61bd119997aa17c778
    // 问题原因：在主线程调用该方法，并且多处调用都是在for循环中，频繁的在主线程做io操作
    // 解决方法：1.对文件是否存在的结果进行缓存，减少io操作次数；2.异步调用该方法
    if (magicGift == null) {
      LiveDebugLoggerV2.i(MAGIC_GIFT_DOWNLOAD_LOG_TAG,
          "[hasDownloadMagicGift]: magicGift is null");
      return false;
    }
    String fileKey = LiveEffectResourceDownloadUtils.getMagicFaceFileKey(magicGift);
    if (fileKey == null) {
      LiveDebugLoggerV2.i(MAGIC_GIFT_DOWNLOAD_LOG_TAG,
          "[hasDownloadMagicGift]: fileKey is null");
      return false;
    }
    Boolean exist = sHasExistMagicGifts.get(fileKey);
    /*
     * 需要检查文件是否下载完成的情况:
     * 1. sHasExistMagicGifts 中不存在其对应状态
     * 2. sHasExistMagicGifts 表示已下载完成, 但是业务指定需要再检查一遍
     */
    if (exist == null || (exist && checkFileExistence)) {
      exist = getMagicGiftResourceLoader().isDownloadCompleted(magicGift);
      synchronized (sHasExistMagicGifts) {
        /*
        这里加一个DoubleCheck，防止因多线程操作时，这里把下载完成的状态给覆盖为错误状态
        只有之前没有对这个Map赋值才使用本次结果，否则相信最近更新的值，不再做刷新，防止覆盖
         */
        Boolean doubleCheckExist = sHasExistMagicGifts.get(fileKey);
        if (doubleCheckExist == null) {
          sHasExistMagicGifts.put(fileKey, exist);
        }
      }
    }
    return exist;
  }
path:ks-kernels/framework-krn/init/src/main/java/com/kwai/framework/krn/init/font/KrnOfflinePackageFont.javacode:private Typeface fetchFontFromOtherBundleImpl(String fontFamily, String bundleId, String name,
      int minBundleVersion) {
    KopBundle kopBundle = Kop.INSTANCE.getCacheBundle(KopBizType.REACT, bundleId);

    // 从内存里读取为Null，那就尝试从DB里面拿一下
    if (kopBundle == null) {
      // 判断当前线程是不是主线程，主线程不建议走IO操作，因为主线程IO操作会导致ANR
      if (!UiThreadUtil.isOnUiThread()) {
        kopBundle = Kop.INSTANCE.getDbBundle(KopBizType.REACT, bundleId);
      }
    }

    if (kopBundle != null && kopBundle.getVersionCode() >= minBundleVersion) {
      String bundleInstallPath = kopBundle.getInstallDirPath();

      // 第一种是static-assets/fonts目录下找
      String staticAssetsDir =
          bundleInstallPath + "/" + STATIC_ASSETS_PATH + "/" + FONTS_ASSET_PATH;
      File staticAssetFile = getFontPathFile(name, new File(staticAssetsDir));
      if (staticAssetFile != null) {
        KLogKrnInit.get()
            .i(TAG, "fetchFontFromOtherBundleImpl from static-assets success , fontFamily :" +
                fontFamily);
        return putCacheAndReturnTypeface(staticAssetFile, fontFamily);
      }

      // 第二种中是raw目录下去找
      String fontDir = bundleInstallPath + "/" + RAW_ASSET_PATH;
      String fontName = joinUnderLines(FONTS_ASSET_PATH, name);
      File fontFile = getFontPathFile(fontName, new File(fontDir));
      if (fontFile != null) {
        KLogKrnInit.get()
            .i(TAG, "fetchFontFromOtherBundleImpl from raw success , fontFamily :" + fontFamily);
        return putCacheAndReturnTypeface(fontFile, fontFamily);
      }
    }
    return null;
  }
path:ks-features/ft-live/live-features/live-common/src/main/java/com/kuaishou/live/common/core/component/partyplay/miniplay/LivePartyPlayMiniPlayLoadingView.ktcode:@RequiresApi(api = Build.VERSION_CODES.KITKAT)
  override fun onDescendantInvalidated(child: View, target: View) {
    // 未开启优化,则直接调用父类方法
    if (!enableOpt) {
      super.onDescendantInvalidated(child, target)
    }
    if (!isAttachedToWindow || !isTimerThread()) {
      super.onDescendantInvalidated(child, target)
    } else {
      // 切到主线程,避免异步更新ui导致barrier泄露的anr
      // 参考 @link com.kwai.framework.ui.debugtools.checkthreadCheckThreadFrameLayout} 
      post { super.onDescendantInvalidated(child, target) }
      // 这里可能会频繁调用,不打日志,做一个标记最后取消loading再打日志;
      hasPosted = true
    }
  }
path:ks-features/ft-locallife/locallife-live/src/main/java/com/kwai/locallife/live/util/LivePerfUtil.ktcode:fun <T, R> tryAsyncAndSwitchToMain(
  asyncTask: () -> T,
  mainTask: (T) -> R,
  extraSwitch: Boolean
): Disposable? {
  return if (extraSwitch) {
    Observable.just(0)
      .observeOn(KwaiSchedulers.ASYNC)
      .map {
        // 子线程
        asyncTask.invoke()
      }
      .observeOn(KwaiSchedulers.MAIN)
      .map {
        // 主线程
        mainTask.invoke(it)
      }
      .subscribe()
  } else {
    val asyncTaskResult = asyncTask.invoke()
    mainTask.invoke(asyncTaskResult)
    null
  }
}
path:ks-features/ft-live/live-libraries/live-basic/src/main/java/com/kuaishou/live/basic/performance/LivePerfUtil.ktcode:fun <T, R> tryAsyncAndSwitchToMain(
  asyncTask: () -> T,
  mainTask: (T) -> R,
  extraSwitch: Boolean
): Disposable? {
  return if (extraSwitch) {
    Observable.just(0)
      .observeOn(KwaiSchedulers.ASYNC)
      .map {
        // 子线程
        asyncTask.invoke()
      }
      .observeOn(KwaiSchedulers.MAIN)
      .map {
        // 主线程
        mainTask.invoke(it)
      }
      .subscribe()
  } else {
    val asyncTaskResult = asyncTask.invoke()
    mainTask.invoke(asyncTaskResult)
    null
  }
}
path:ks-components/kuaishou-forward/src/main/kotlin/com/yxcorp/gifshow/share/KwaiOperator.ktcode:let {
            dialogListener?.onPrepareOperation(it) ?: it
          }
          val opListener = createOpClickListener(listener)
          val dialog = QListAlertDialogBuilder(activity).build(style, activity, ops)
             .setDarkStyle(style == Style.ITEM_LIST_DARK).setOnClickListener { _, which ->
               val op = ops.find { it.textResId == which } ?: return@setOnClickListener
               opListener(op, 0)
             }.show()
          <EMAIL> = dialog
          dialog.setOnCancelListener {
            KLogKuaishouForward.get().d(TAG, "cancel")
            dialogListener?.onDialogCancel(this)
            EventBus.getDefault().post(OperatorShownEvent(false))
          }
          dialog.setOnDismissListener {
            dialogListener?.onDialogDismiss(this)
          }
        }
        // 标题部分为可复制文本
        Style.COPY_TITLE -> {
          if (!ChildLockUtil.getShareEnableWithChildLock()) {
            KSToast.applyStyle(R.style.style_toast_text, R.string.child_lock_unavailable)
            return@Consumer
          }
          MerchantForwardFragment().apply {
            val items = factories.map {
              it.build(model).run { dialogListener?.onPrepareOperation(this) ?: this }
            }
            if(items!=null && items.isNotEmpty()){
              platformItems = items[0]
            }
            showQRCode = dialogShowQRCode
            introduceStr = "" + copyTitleContent
            onQrCodeClickListener = <EMAIL>
            clickListener = createOpClickListener(listener)
            showListener = createOpShowListener(dialogListener)
            this.shareParam = ShareImParam(this@KwaiOperator, platformItems, listener)
            <EMAIL> = this
            setOnCancelListener {
              dialogListener?.onDialogCancel(this)
              EventBus.getDefault().post(OperatorShownEvent(false))
            }
            setOnDismissListener {
              dialogListener?.onDialogDismiss(this)
            }
          }.show(activity.supportFragmentManager, "")
          }
        else -> {
          throw IllegalArgumentException("unsupport style")
        }
      }
      dialogListener?.onDialogShow(popup)
      EventBus.getDefault().post(OperatorShownEvent(true))
      logOperatorShown()
    })
  }