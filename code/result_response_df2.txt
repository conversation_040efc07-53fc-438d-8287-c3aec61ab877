path:ks-components/slide-play-detail-framework/src/main/java/com/kwai/slide/play/detail/base/BaseGroup.ktcode:@JvmOverloads fun bind() {
    KLogDetailFramework.get().e("VM_BaseGroup", "bind start isBind = $isBind, ${getClassName()}")
    if (isBind) {
      throw Exception("${javaClass.name}  Already bind")
    }
    try {
      elementOrderList.forEach {
        if (!(it is DispatchBaseElement && it.isDispatchEnable())) {
          it.tryShow()
        }
        it.isRealShow = false
        it.bind()
      }
    } catch (e: Exception) {
      KLogDetailFramework.get().e("VM_BaseGroup", "bind elementOrderList throw Exception ", e)
      throw e
    }

    isBind = true
    refreshAll()
    KLogDetailFramework.get().e("VM_BaseGroup", "bind end, ${getClassName()}")
  }
path:ks-components/feed-player-ui/src/main/java/com/kwai/feed/player/ui/KwaiXfPlayerView.javacode:private void bindPlayer(KwaiMediaPlayer player) {
    if (player == null) {
      return;
    }
    player.addPlayerSeekTriggerListener(mSeekTriggerListener);
    mLoadingHelper =
        new PlayerLoadingHelper(player, false, new PlayerLoadingHelper.PlayerLoadingCallback() {
          @Override
          public void shouldShowLoading() {
            changeLoading(LoadingHideReason.DEFAULT, true);
          }

          @Override
          public void shouldHideLoading() {
            changeLoading(LoadingHideReason.DEFAULT, false);
            handleLoadingChanged();
          }
        });
    mLoadingHelper.initLoadingCheck();
    mWorkStateHelper =
        new PlayerWorkStateHelper(player, new PlayerWorkStateHelper.PlayerWorkStateListener() {
          @Override
          public void onPlayerDied() {
            logMsg("onPlayerDied");
            mShouldShowError = true;
            changeErrorOverlay();
          }

          @Override
          public void onPlayerWorkHint() {
            logMsg("onPlayerWorkHint");
            mShouldShowError = false;
            changeErrorOverlay();
          }
        });
    refresh();
  }
path:ks-components/profile-access/src/main/java/com/yxcorp/gifshow/profile/common/kslog/KsLogProfileProxy.javacode:public static void i(List<KsLogProfileBaseTag> tags, String msg, String k1, String v1) {
    HashMap<String, String> map = new HashMap<>(1);
    map.put(k1, v1);
    i(tags, msg, map);
  }
path:ks-features/ft-live/live-features/live-common/src/main/java/com/kuaishou/live/common/core/component/hotspot/detail/coremediaarea/LiveHotSpotDetailLivePlayerCardViewController.ktcode:private fun binding() {
    liveViewData.observe(this) {
      cardEventSubject.onNext(LiveHotSpotDetailCardEvent.Show(it))
    }

    //封面的隐藏和显示
    findViewById<KwaiImageView>(R.id.player_cover).let { coverView ->
      coverView.bindVisible(this, viewModel.showCover, true)
      viewModel.cover.observe(this) {
        coverView.bindUrls(it, KwaiImageCallerContext.newBuilder().setBizFt(BuildConfig.PROJECT_NAME).build())
      }
    }

    //卡片左上角的角标绑定数据
    findViewById<LiveCoverIconView>(R.id.live_cover_icon).let { coverIconView ->
      viewModel.coverIcon.observe(this) {
        coverIconView.bind(it)
      }
    }

    //直播观看人数
    findViewById<TextView>(
      R.id.live_hotspot_people_num
    ).bindText(this, viewModel.watchingCount)

    //播放器的播放逻辑
    val textureView: LivePlayTextureView = findViewById(R.id.live_surface)
    viewModel.playEvent.observeForever {
      when (it) {
        is LivePlayEvent.Start -> {
          startPlay(textureView, it.liveStreamFeed)
        }
        is LivePlayEvent.Stop -> {
          //停止播放后，销毁播放器
          playerManager?.destroy()
          playerManager = null
        }
        is LivePlayEvent.Mute -> {
          playerManager?.mute(it.muted)
        }
      }
    }

    viewModel.openDetailProfileEvent.observe(this) {
      cardEventSubject.onNext(LiveHotSpotDetailCardEvent.Click(it, requireContentView()))
    }

    //卡片点击事件
    requireContentView().setOnClickListener {
      viewModel.handleIntent(LiveHotSpotDetailIntent.CardClick)
    }
  }
path:ks-components/profile-access/src/main/java/com/yxcorp/gifshow/profile/common/kslog/KsLogProfileProxy.javacode:public static void i(KsLogProfileBaseTag tag, String msg, String k1, String v1) {
    i(Arrays.asList(tag), msg, k1, v1);
  }
path:ks-features/ft-social/message/src/main/java/com/yxcorp/gifshow/message/detail/message_list/present/paiyipai/KPaiYiPaiMsgPresenter.ktcode:override fun doBindView(rootView: View) {
    super.doBindView(rootView)
    pypRootView = bindWidget(rootView, R.id.pyp_root_view)
    pypContentView = bindWidget(rootView, R.id.pyp_content_view)
    titleView = bindWidget(rootView, R.id.title)
    coverView = bindWidget(rootView, R.id.cover_view)
    maskView = bindWidget(rootView, R.id.mask_view)
    playView = bindWidget(rootView, R.id.play_view)
    placeHolderView = bindWidget(rootView, R.id.placeholder_view)
    replyTextView = bindWidget(rootView, R.id.reply_text)
    infoGroup = bindWidget(rootView, R.id.pyp_info_group)
  }
path:ks-features/ft-commercial/commercial-corona/src/main/java/com/kuaishou/commercial/corona/instream/InstreamAdCanalView.ktcode:protected open fun bindView() {
    inflate(context, layoutId, this)
    mBackView = ViewBindUtils.bindWidget(this, R.id.iv_instream_ad_back)
    mVolumeView = ViewBindUtils.bindWidget(this, R.id.iv_instream_ad_volume)
    mActionBar = ViewBindUtils.bindWidget(this, R.id.tv_instream_btn)
    mBuyVipView = ViewBindUtils.bindWidget(this, R.id.tv_instream_ad_buy_vip)
    mCountDownView = ViewBindUtils.bindWidget(this, R.id.tv_instream_ad_countdown)
    mCountDownSkipView = ViewBindUtils.bindWidget(this, R.id.tv_instream_ad_skip)
    mPlayerKitView = ViewBindUtils.bindWidget(this, R.id.kwai_player_kit_view)
    mPlayerBlurCover = ViewBindUtils.bindWidget(this, R.id.iv_instream_blur_cover)
    mPlayerCover = ViewBindUtils.bindWidget(this, R.id.iv_instream_cover)
    mLoadingViewContainer = ViewBindUtils.bindWidget(this, R.id.fl_loading)
    mLoadingView = ViewBindUtils.bindWidget(this, R.id.loading_view)
    mLoadingBg = ViewBindUtils.bindWidget(this, R.id.loading_bg)
    mLlCountDown = ViewBindUtils.bindWidget(this, R.id.ll_count_down)
    mTvAdDesc = ViewBindUtils.bindWidget(this, R.id.tv_advertisement)
    mLoadingView.setStyle(true, null)
  }