path:ks-features/ft-feed/detail/src/com/yxcorp/gifshow/detail/nonslide/presenter/log/PhotoDetailLoggerPresenter.javacode:@Override
        public void becomesAttachedOnPageSelected() {
          SlidePlayViewModel slidePlayViewModel = mSlidePlayViewModel;
          if (slidePlayViewModel == null || !slidePlayViewModel.isSourceTypeFeed()) {
            return;
          }
          int curPosition = slidePlayViewModel.getCurrentItemRealPositionInAdapter();
          int count = slidePlayViewModel.getRealCountInAdapter();
          if (curPosition == count - 1) {
            NetworkQualityEstimator.Metrics metrics = NetworkQualityEstimator.getMetrics();
            mLogger.setFeedStuckNetInfo(NetworkQualityEstimator.getScore(),
                metrics.downstreamThroughputKbps, metrics.serverRttMs);
          }
        }
path:ks-features/ft-feed/featured-detail/src/main/java/com/yxcorp/gifshow/featured/detail/featured/milano/HomeMilanoBaseContainerFragment.javacode:@Override
  @SuppressWarnings("MethodCyclomaticComplexity")
  public void onDestroyView() {
    super.onDestroyView();
    SlidePlayDegradeApi.setNeedDegrade(false);
    if (mLaunchLoadRunnable != null) {
      mHandler.removeCallbacks(mLaunchLoadRunnable);
    }
    if (mBindPresenterRunnable != null) {
      RunnableSchedulerAfterLaunchFinish.removeScheduleRunInUIThread(mBindPresenterRunnable);
    }
    // keep上会报mLaunchTracker为空,先加个空保护
    if (mLaunchStateCallback != null && mLaunchTracker != null) {
      mLaunchTracker.removeLaunchStateCallback(mLaunchStateCallback);
    }
    if (mSlidePlayViewModel != null) {
      mSlidePlayViewModel.lifecycle().traverseContainerFragmentDisappear();
    }
    mWaitBindPresenter = false;
    if (mPresenter != null) {
      mPresenter.destroy();
    }
    if (mMilanoPresenterGroup != null) {
      mMilanoPresenterGroup.destroy();
    }
    if (mSelectDisposable != null && !mSelectDisposable.isDisposed()) {
      mSelectDisposable.dispose();
    }
    if (mMenuSlideStateDisposable != null && !mMenuSlideStateDisposable.isDisposed()) {
      mMenuSlideStateDisposable.dispose();
    }
    // keep上报mMenuSlideState空,先空保护一下
    if (mMenuSlideState != null) {
      mMenuSlideState.discard();
    }
    if (mCallerContext != null && mCallerContext.mGlobalParams != null) {
      mCallerContext.mGlobalParams.clear();
    }
    LoadMoreDecisionHelper.get(this).release();
    if (mContanierFactory != null) {
      mContanierFactory.destroyContainerFragment();
    }
    mIsInited = false;
    mIsManualDoInit = false;
    mIsManualDoInitOnPageUnSelect = false;
    FoldDeviceAdaptHelper.removeFoldDeviceChangeMonitor(mMonitor);
  }
path:ks-components/photo-detail/detail-slide/src/main/java/com/kwai/component/photo/detail/slide/presenter/SlidePlayDetailFlowPresenter.javacode:@Override
  @SuppressWarnings("MethodCyclomaticComplexity")
  protected void onBind() {
    super.onBind();
    mNetworkStateVm =
        WidgetViewModel.findNullable(mFragment, DetailNetworkStateViewModel.class);
    mLoadingVm =
        WidgetViewModel.findNullable(mFragment, DetailLoadingViewModel.class);
    mSlidePlayViewModel = SlidePlayViewModel.get(mFragment);
    if (mDetailParam.mPhoto == null) {
      // 判断是否从kwai链enableDomino进入详情页
      // 这种情况下，不走请求pagelist的逻辑，走原逻辑
      // 之后考虑把 SlidePlayDetailRecommendFlowPresenter 的逻辑挪到其 pagelist 里
      // 从而在此处可以直接请求刷新pagelist
      boolean isKwaiDomino =
          "true".equals(
              DetailRouterSchemeUtil.getSchemaParameter(getActivity().getIntent(), "enableDomino"));
      if (!isKwaiDomino) {
        SlidePlayDataFetcherImpl fetcher =
            SlidePlayDataFetcherImpl.getFetcher(mDetailParam.mSlidePlayId);
        if (fetcher != null) {
          mPageList = fetcher.getPageList();
          if (mPageList.isEmpty()) {
            mPageList.registerObserver(mPageListObserver);
            if (mRetryNetworkEmptyTipsView != null) {
              mRetryNetworkEmptyTipsView.setOnClickListener(v -> {
                mPageList.refresh();
              });
            }
            if (mPageList.isEmpty()) {
              mPageList.refresh();
            }
            return;
          }
        }
      }

      Uri uri = getActivity().getIntent().getData();
      if (uri != null && uri.isHierarchical() && !TextUtils.isEmpty(uri.getLastPathSegment())) {
        KwaiUriUtil.identifyPromoteTag(uri);
        mPhotoId = uri.getLastPathSegment();
        String rootCommentId = SafetyUriCalls.getQueryParameterFromUri(uri, "rootCommentId");
        String commentId = SafetyUriCalls.getQueryParameterFromUri(uri, "commentId");
        if (!TextUtils.isEmpty(commentId)) {
          QComment comment = new QComment();
          try {
            comment.transparentParam = new JSONObject().put("sourceType", "KWAI").put("schemaUrl", uri.toString());
          } catch (JSONException e) {
            KLogKsComment.get().e(TAG, e);
          }
          comment.mId = commentId;
          comment.mRootCommentId = rootCommentId;
          mDetailParam.getDetailCommonParam().setComment(comment);
        }
        mSchemaExpTag = SafetyUriCalls.getQueryParameterFromUri(uri, KwaiUriConstant.EXP_TAG);
        mServerExpTag =
            SafetyUriCalls.getQueryParameterFromUri(uri, KwaiUriConstant.SERVER_EXP_TAG);
        String fromH5Page = SafetyUriCalls.getQueryParameterFromUri(uri, KwaiUriConstant.H5_PAGE);
        String utmSource =
            SafetyUriCalls.getQueryParameterFromUri(uri, KwaiUriConstant.H5_UTM_SOURCE);

        mDetailParam.getDetailLogParam().setSchemaInfo(
            TextUtils.defaultIfEmpty(fromH5Page,
                mDetailParam.getDetailLogParam().getPageUrlParam("h5_page")),
            TextUtils.defaultIfEmpty(utmSource,
                mDetailParam.getDetailLogParam().getPageUrlParam("utm_source")));

        queryPhotoInfo(() -> {
          QPhotoMediaPlayerCacheManager.releasePreModule(mDetailParam.mPhoto);
          mFlowEndCallback.run();
          onLoadPhotoInfoDone();
        }, throwable -> {
          String userId = SafetyUriCalls.getQueryParameterFromUri(uri, "userId");
          String backUri = SafetyUriCalls.getQueryParameterFromUri(uri, PUSH_BACK_URI);
          if (TextUtils.isEmpty(userId) || !TextUtils.isEmpty(backUri)) {
            // 无网情况下弹出toast提示
            if (throwable instanceof RetrofitException
                && ((RetrofitException) throwable).mCause instanceof NetworkException) {
              KSToast.applyStyle(R.style.style_toast_text, CommonUtil.string(R.string.network_failed_tip));
            }
            checkDonationReturnConfig(uri);
            getActivity().finish();
            return;
          }
          startProfileActivity(userId);
          getActivity().finish();
        });
      } else if (mDetailParam.isFromDomino()) {
        KLogger.i(TAG, "from kwai://domino link");
      } else if (handlerOtherLogicWithNoPhoto()) {
        KLogger.i(TAG, "handlerOtherLogic");
      } else {
        getActivity().finish();
      }
      return;
    }

    mPhotoId = mDetailParam.mPhoto.getPhotoId();
    mServerExpTag = mDetailParam.mPhoto.getServerExpTag();
    querySlideKCardPhotos();
  }
path:ks-features/ft-live/live-features/live-common/src/main/java/com/kuaishou/live/core/basic/liveslide/item/LiveGlobalPlaybillViewItem.javacode:@Override
      public void onLiveStreamFeedGetFailed() {
        SlidePlayViewModel slidePlayViewModel =
            SlidePlayViewModel.get(fragment().getParentFragment());
        if (slidePlayViewModel != null) {
          LiveDebugLoggerV2.i(LiveLogTag.LIVE_OFFICIAL_PROGRAMME,
              "LiveGlobalPlaybillViewItem onLiveStreamFeedGetFailed");
          mSlidePlayFragment.getLiveStreamFeed().mLiveStreamModel.mIsProgrammeOfficialAccount = false;
          slidePlayViewModel.replaceCurrentItem(new QPhoto(mSlidePlayFragment.getLiveStreamFeed()),
              true, "LiveGlobalPlaybillViewItem");
        }
      }
path:ks-features/ft-live/live-external/live-collection/src/main/java/com/kuaishou/live/preview/item/util/LivePreviewSlideUtil.javacode:public static void removeAttachListener(@NonNull BaseFragment fragment,
      @NonNull PhotoDetailAttachChangedListener listener) {
    SlidePlayViewModel slidePlayViewModel =
        SlidePlayViewModel.getNullable(fragment.requireParentFragment());
    if (slidePlayViewModel != null) {
      slidePlayViewModel.removeListener(fragment, listener);
    }
  }
path:ks-features/ft-live/live-shell/src/main/java/com/kuaishou/live/fragment/LivePluginGrootPlayFragment.javacode:@Override
      public void onLoadSuccess() {
        //SlidePlayViewModel.get(LivePluginGrootPlayFragment.this.getParentFragment())
        // .refreshPageList(false);

        // 插件加载时长
        long duration = System.currentTimeMillis() - mLoadBeginTime;
        // mLoadBeginTime > 0 表示页面可见，不是静默预加载
        if (mLoadBeginTime > 0 && duration > 0) {
          // 上报插件加载性能埋点
          LivePluginLogger.logLiveAudiencePluginCustomEvent(
              LivePluginLogger.getLiveAudiencePluginLoadSource(mPage2),
              duration,
              mHasDownloaded,
              true);
        }

        LiveDebugLoggerV2.i(LiveLogTag.LIVE_PLUGIN.appendTag(TAG),
            "onLoadSuccess",
            "page2", mPage2);
        if (!mHasReplaceItem) {
          // 替换当前fragment
          replaceItem();
        }
      }
path:ks-features/ft-feed/detail/src/com/yxcorp/gifshow/detail/SlidingTaskScheduler/SlidingTaskSchedulerPresenter.javacode:@Override
  protected void onBind() {
    super.onBind();
    mEnableSlideObiwanSuppress = DetailExperimentUtils.enableSlideObiwanSuppression();
    mEnableSlideDoFrameFirstOptimizer = DetailExperimentUtils.getDoframeRearrangeConfig().getEnable();
    mEnableGcBlock = DetailExperimentUtils.getGcBlockConfig() != null
        && DetailExperimentUtils.getGcBlockConfig().enableGcBlock;
    if (mEnableGcBlock) {
      mDisableGcBlockInLowPhone =
          DetailExperimentUtils.getGcBlockConfig() != null
              && !DetailExperimentUtils.getGcBlockConfig().enableInLowPhone
              && Singleton.get(PhoneLevelUtils.class).isLowPhone();
      mIsArm64 = AbiUtil.isArm64();
      mIsTarVersion = isTarVersion();
      mFreeHeapLimitByte = getFreeHeapLimitByte();
      mGcBlockSec = getGcBlockSec();
    }
    mEnableJitBlock = DetailExperimentUtils.getJitBlockConfig() != null
        && DetailExperimentUtils.getJitBlockConfig().enableJitBlock;
    if (mEnableJitBlock) {
      mDisableJitBlockInLowPhone =
          DetailExperimentUtils.getJitBlockConfig() != null
              && !DetailExperimentUtils.getJitBlockConfig().enableInLowPhone
              && Singleton.get(PhoneLevelUtils.class).isLowPhone();
      mJitBlockSec = getJitBlockSec();
    }
    mSlidePlayViewModel = SlidePlayViewModel.get(mFragment);
    VerticalViewPager viewPager = mSlidePlayViewModel.getViewPager();
    if (viewPager == null) {
      return;
    }
    viewPager.setSlideStateListener(new SlideStateListener() {
      @Override
      public void onSlideStart() {
        if (mEnableSlideDoFrameFirstOptimizer) {
          DoFrameFirstOptimizer.enable(DetailExperimentUtils.getDoframeRearrangeConfig().getDisablePostAtFrontMessage());
        }
        AwesomeDispatchSceneUtil.dealSlideAweDispatch(AwesomeDispatch.APP_SCENE_PHOTO_DETAIL_SLIDE);
        doGcBlock();
        doJitBlock();
        KLogDetail.get().i(TAG, "up down scroll begin");
        // 开启日志延迟写入
        if ((mEnableSlideObiwanSuppress || PerfModeExtHelper.enableObiwanSupress()) &&
            obiwanLogger != null) {
          obiwanLogger.pauseAddLog();
        }
        KLogDetail.get().i("OBWSLOG", "up down scroll begin");
      }

      @Override
      public void onSlideStop() {
        // 结束日志延迟写入
        if ((mEnableSlideObiwanSuppress || PerfModeExtHelper.enableObiwanSupress()) &&
            obiwanLogger != null) {
          obiwanLogger.resumeAddLog();
        }
        KLogDetail.get().i("OBWSLOG", "up down scroll end");
        KLogDetail.get().i(TAG, "up down scroll end");
        if (mEnableSlideDoFrameFirstOptimizer) {
          DoFrameFirstOptimizer.disable();
        }
      }
    });
  }