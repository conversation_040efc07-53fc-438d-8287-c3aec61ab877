path:ks-features/ft-platform/platform-test-config/src/main/java/com/kuaishou/platform/testconfig/PerformanceTestConfigPage.javacode:private void innerMethod2() {
        double result = 0;
        for (int i = 0; i < 300000000; i++) {
          result += Math.PI * Math.PI * Math.PI * Math.PI * Math.PI;
        }
      }
path:ks-features/ft-live/live-libraries/live-presenter-scatter/src/main/java/com/smile/gifmaker/mvps/presenter/LiveScatterPresenterControl.ktcode:override fun unbind() {
    LiveDebugLoggerV2.i(TAG, "unbind")
    // 直接执行unbind方法，不添加到任务队列，减少函数调用层次，优化耗时
    flushUnbind()
  }
path:ks-components/photo-detail/detail-awesomedispatch/src/main/java/com/kwai/component/photo/detail/awesomedispatch/dispatch/AwesomeDispatchManager.javacode:@Override
    public void run() {
      running = true;
      PerformanceTask currCpuLevel = null;
      PerformanceTask currGpuLevel = null;
      PerformanceTask currIoLevel = null;
      HashSet<PerformanceTask> currBindCore = new HashSet<>();
      long nextWakeInterval = DEFAULT_WAKE_INTERVAL;
      final ArrayList<PerformanceTask> listStartTask = new ArrayList<>();
      final HashMap<Integer, PerformanceTaskStop> setStopTask = new HashMap<>();
      final HashSet<Integer> setStartedTask = new HashSet<>();

      while (running) {
        final long startLoop = System.currentTimeMillis();
        final int queueSize = taskQueue.size();
        AwesomeDispatchLog.d(TAG, "startLoop 任务队列大小:" + queueSize + " startTask:" + listStartTask.size() +
            " 下一次唤醒时间:" + nextWakeInterval);

        for (int i = 0; i < (queueSize == 0 ? 1 : queueSize); i++) {
          Object object = null;
          try {
            object = taskQueue.poll(nextWakeInterval, TimeUnit.MILLISECONDS);
          } catch (Exception e) {
            AwesomeDispatchLog.e(TAG, "taskQueue poll: " + e.getMessage());
            object = null;
          }
          if (object == null) {
            break;
          } else if (object instanceof PerformanceTask) {
            listStartTask.add((PerformanceTask) object);
            setStartedTask.add(((PerformanceTask) object).hashCode());
          } else if (object instanceof PerformanceTaskStop) {
            int hashCode = ((PerformanceTaskStop) object).hashCode;
            if (setStartedTask.contains(hashCode)) {
              setStopTask.put(hashCode, (PerformanceTaskStop) object);
            }
          } else {
            AwesomeDispatchLog.e(TAG, "taskQueue poll invalid object");
          }
        }
        nextWakeInterval = DEFAULT_WAKE_INTERVAL;

        PerformanceTask maxCpuLevel = null;
        PerformanceTask maxGpuLevel = null;
        PerformanceTask maxIoLevel = null;
        HashSet<PerformanceTask> needBindCore = new HashSet<>();
        final long now = System.currentTimeMillis();

        AwesomeDispatchLog.d(TAG, "InLoop startSize:" + listStartTask.size());

        for (int i = 0; i < listStartTask.size(); i++) {
          final PerformanceTask task = listStartTask.get(i);
          final long curProcessTime = System.currentTimeMillis();
          if (task == null) {
            continue;
          }

          if (setStopTask.containsKey(task.hashCode())) {
            task.stopTime = curProcessTime;
            setStopTask.remove(task.hashCode());
          }

          final long lifeLeftInterval = task.stopTime - curProcessTime;
          if (lifeLeftInterval <= 0) {
            AwesomeDispatchLog.d(TAG, "任务结束的时间 InLoop STOP:" + i + "/" + listStartTask.size() + " task:" +
                task.toString(curProcessTime));
            listStartTask.remove(task);
            setStartedTask.remove(task.hashCode());
            i--;
            continue;
          }
          nextWakeInterval = Math.min(nextWakeInterval, lifeLeftInterval);

          final long waitStartInterval = task.startTime - curProcessTime;
          if (waitStartInterval > 0) {  //  task waiting start
            AwesomeDispatchLog.d(TAG, "InLoop WAIT:" + i + "/" + listStartTask.size() + " task:" +
                task.toString(curProcessTime));
            nextWakeInterval = Math.min(nextWakeInterval, waitStartInterval);
            continue;
          }

          AwesomeDispatchLog.d(TAG, "InLoop RUN:" + i + "/" + listStartTask.size() + " task:" +
              task.toString(curProcessTime));

          if (task.cpuLevel > 0
              && ((maxCpuLevel == null) || (maxCpuLevel.cpuLevel > task.cpuLevel) ||
              (maxCpuLevel.cpuLevel == task.cpuLevel && maxCpuLevel.stopTime < task.stopTime))) {
            AwesomeDispatchLog.d(TAG, "maxCpuLevel");
            maxCpuLevel = task;
          }

          if (task.gpuLevel > AwesomeDispatch.GPU_LEVEL_0
              && ((maxGpuLevel == null) || (maxGpuLevel.gpuLevel > task.gpuLevel) ||
              (maxGpuLevel.gpuLevel == task.gpuLevel && maxGpuLevel.stopTime < task.stopTime))) {
            AwesomeDispatchLog.d(TAG, "maxGpuLevel");
            maxGpuLevel = task;
          }
          if (task.ioLevel > AwesomeDispatch.IO_LEVEL_0
              && ((maxIoLevel == null) || (maxIoLevel.ioLevel > task.ioLevel) ||
              (maxIoLevel.ioLevel == task.ioLevel && maxIoLevel.stopTime < task.stopTime))) {
            AwesomeDispatchLog.d(TAG, "maxIoLevel");
            maxIoLevel = task;
          }

          for (int tid : task.bindTids) {
            if (
path:ks-feature-apis/post-api/src/record/main/java/com/kwai/gifshow/post/api/core/util/PostDynamicSOUtils.javacode:@Override
    public void onLoadingDialogDismiss() {
      // 加载弹窗消失时，回调次数>=0, 下一个状态回调肯定是onSucceed()、onShowFailDialog()
      //、onComplete()中的一个。如果onSucceed、onShowFailDialog都没有调用到，那就是手动取消的。
      KLogPostApi.get().i(DIALOG_TAG, "onLoadingDialogDismiss");
    }
path:ks-features/ft-platform/platform-test-config/src/main/java/com/kuaishou/platform/testconfig/PerformanceTestConfigPage.javacode:private void innerMethod() {
        double result = 0;
        for (int i = 0; i < 100000000; i++) {
          result += Math.PI * Math.PI * Math.PI * Math.PI * Math.PI;
        }
        innerMethod2();
        for (int i = 0; i < 100000000; i++) {
          result += Math.PI * Math.PI * Math.PI * Math.PI * Math.PI;
        }
      }
path:ks-components/page-cost-record/src/main/java/com/yxcorp/gifshow/page/cost/StageTraceRecyclerFragmentMixin.ktcode:override fun onChanged() {
            //firstPage=true始终会调用此生命周期
            if (owner.pageList.latestPage != null) {
              //==null的case可能是预热时引发的假数据notify
              TracePool.of(it).end(StageRegistry.Required.DATA_FETCH)
              //此时在adapter observer回调链中最后会触发RV的requestLayout
              //即会监听下一个VSYNC信号然后进行RV渲染（RV OnLayout）
              //所以在下个方法内部会监听下一个VSYNC信号中Traversal接入（COMMIT）的
              //时机，以那个时机的耗时作为UI耗时
              onStageTraceReadyToRender(owner.pageList.latestPage)
              //内部逆序遍历，不用防御concurrent问题
              unregisterAdapterDataObserver(this)
            }
          }
path:ks-components/photo-detail/detail-awesomedispatch/src/main/java/com/kwai/component/photo/detail/awesomedispatch/dispatch/AwesomeDispatchManager.javacode: = task;
          }
          if (task.ioLevel > AwesomeDispatch.IO_LEVEL_0
              && ((maxIoLevel == null) || (maxIoLevel.ioLevel > task.ioLevel) ||
              (maxIoLevel.ioLevel == task.ioLevel && maxIoLevel.stopTime < task.stopTime))) {
            AwesomeDispatchLog.d(TAG, "maxIoLevel");
            maxIoLevel = task;
          }

          for (int tid : task.bindTids) {
            if (tid <= 0) {
              continue;
            }
            needBindCore.add(task);
            break;
          }
        }

        final long endLoop = System.currentTimeMillis();


        HashSet<PerformanceTask> stopSet = new HashSet<>();
        stopSet.addAll(currBindCore);
        stopSet.removeAll(needBindCore);

        //check the current level
        //if current level is the same with the request, continue
        if (maxCpuLevel == currCpuLevel && maxGpuLevel == currGpuLevel &&
            maxIoLevel == currIoLevel &&
            currBindCore.size() == needBindCore.size() && stopSet.isEmpty()) {//same
          AwesomeDispatchLog.d(TAG, "EndLoop Nothing Changed, Continue.");
          continue;
        }

        int reqCpuLevel = 0;
        int reqGpuLevel = 0;
        int reqIOLevel = 0;
        int reqScene = 0;
        long reqAction = 0;
        int reqCallerTid = 0;
        int reqTimeStamp = Integer.MAX_VALUE;

        int cancelTidCount = 0;//need unbind tids count
        for (PerformanceTask performanceTask : stopSet) {
          if (performanceTask.bindTids == null) {
            continue;
          }
          cancelTidCount += performanceTask.bindTids.length;
        }

        if (cancelTidCount > 0) {
          int[] arrCancelTid = new int[cancelTidCount];
          int idxStop = 0;
          for (Iterator<PerformanceTask> it = stopSet.iterator(); it.hasNext(); ) {
            PerformanceTask task = it.next();
            final long curProcessTime = System.currentTimeMillis();
            AwesomeDispatchLog.i(TAG, "!cancelBindCore task:" + task.toString(curProcessTime));
            if (task.stopTime > curProcessTime) {
              AwesomeDispatchLog.e(TAG, "stopTime:" + (task.stopTime - curProcessTime) + ". Error !");
              continue;
            }
            if (!task.isNeedBindTids()) {
              AwesomeDispatchLog.e(TAG, "bindTids:" + task.bindTidsToString());
            }
            for (int tid : task.bindTids) {
              arrCancelTid[idxStop] = tid;
              AwesomeDispatchLog.e(TAG, "arrCancelTid:" + tid);
              idxStop++;
            }
          }
          AwesomeDispatchLog.e(TAG, "cancelCpuCoreForThread");
          releaseCpuCoreForThread(arrCancelTid, Process.myTid(),
              SystemClock.elapsedRealtimeNanos());
        }

        currBindCore = needBindCore; // always include all

        int tidsLen = 0;//need bind tids count
        for (PerformanceTask performanceTask : currBindCore) {
          if (performanceTask.bindTids == null) {
            continue;
          }
          tidsLen += performanceTask.bindTids.length;
        }

        int[] tidList = new int[tidsLen];
        int idxStart = 0;
        for (Iterator<PerformanceTask> it = currBindCore.iterator(); it.hasNext(); ) {
          PerformanceTask task = it.next();
          AwesomeDispatchLog.i(TAG, "requestBindCore task:" + task.toString(now));
          final long curProcessTime = System.currentTimeMillis();

          if (task.stopTime <= curProcessTime) {
            AwesomeDispatchLog.e(TAG, "stopTime:" + (task.stopTime - curProcessTime) + ". Error !");
            continue;
          }

          if (!task.isNeedBindTids()) {
            AwesomeDispatchLog.e(TAG, "bindTids:" + task.bindTidsToString());
          }
          for (int tid : task.bindTids) {
            tidList[idxStart] = tid;
            AwesomeDispatchLog.e(TAG, "tidList:" + tid);
            idxStart++;
          }
          reqScene = task.scene;
          AwesomeDispatchLog.e(TAG, "reqScene1:" + reqScene + "");

          reqAction = task.action;
          reqCallerTid = task.callerTid;
          reqTimeStamp = Math.min((int) (task.stopTime - now), reqTimeStamp);
        }

        if (maxIoLevel == null) { // here curr should be timeout.
          if (currIoLevel != null) {
            AwesomeDispatchLog.i(TAG, "!cancelHighIOFreq task:" + currIoLevel.toString(now));
            releaseBoostDdr(currIoLevel