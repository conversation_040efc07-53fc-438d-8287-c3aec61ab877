path:ks-features/ft-social/pymk-slide-play/src/main/java/com/yxcorp/gifshow/pymk/slide/play/empty/PymkDetailSlidePlayRecyclerFragment.javacode:private void adjustScrollStatusOnTouchViewPager(
      @NonNull final KwaiGrootViewPager viewPager,
      @NonNull final AtomicBoolean lastTopMost,
      @NonNull final Supplier<Boolean> isTopMostSupplier) {
    mTouchCallback = new GrootViewPagerTouchCallback() {
      private float mLastX;
      private float mLastY;

      @Override
      public void onTouch(@NonNull final MotionEvent e) {
        switch (e.getActionMasked()) {
          case MotionEvent.ACTION_DOWN: {
            mLastX = e.getX();
            mLastY = e.getY();
            break;
          }
          case MotionEvent.ACTION_UP: {
            final float yOffset = e.getY() - mLastY;
            final float xOffset = e.getX() - mLastX;
            final boolean isScrollingDown = yOffset > 0
                && yOffset > ViewConfiguration.get(getContext()).getScaledTouchSlop()
                && yOffset > 2 * Math.abs(xOffset);
            final boolean isTopMost = isTopMostSupplier.get();
            lastTopMost.set(isTopMost);
            log("onTouch[%1$s]-dir[%2$s]", isTopMost, isScrollingDown);
            mScrollStateConsumer.accept(isScrollingDown && isTopMost);
            break;
          }
        }
      }
    };
    viewPager.addGrootViewPagerTouchCallback(mTouchCallback);
  }
path:ks-features/ft-feed/detail/src/com/yxcorp/gifshow/detail/presenter/touch/NasaExpandViewPagerTouchAreaPresenter.ktcode:override fun onBind() {
    mSlidePlayViewModel = SlidePlayViewModel.get(mFragment)
    mViewPager = mSlidePlayViewModel.getViewPageAs(GrootViewPager::class.java)
    if (mViewPager != null && mSwipeLayout != null) {
      mSlidePlayViewModel.addOnViewPagerTranslateYListener(mOnViewPagerTranslateYListener)
      addToAutoDisposes(mFragment.compositeLifecycleState.observeReallySelect().subscribe {
        if (it) {
          mSwipeLayout?.addTouchDetector(mTouchDetector)
        } else {
          mSwipeLayout?.removeTouchDetector(mTouchDetector)
        }
      })
      mMoreTrendingListShowPublisher?.observeOnMain()?.subscribe({ isShow: Boolean ->
          mDisableBitSet.set(DISABLE_FLAG_MORE_TRENDING_PANEL, isShow)
        }, Functions.ERROR_CONSUMER)?.let { addToAutoDisposes(it) }

      addToAutoDisposes(mMilanoContainerEventBus.mCommentFragmentVisiblePublisher.subscribe {
          isShow ->
        mDisableBitSet.set(DISABLE_FLAG_GENERAL, isShow)
      })

      mScreenMilanoProtocol?.observeScreenClearResult { event ->
        mDisableBitSet.set(DISABLE_FLAG_CLEAR_SCREEN, !event.mIsUnclear)
      }?.let { addToAutoDisposes(it) }
    }
  }
path:ks-features/ft-feed/detail/src/com/yxcorp/gifshow/detail/slidev2/presenter/NasaSpeedPlayPresenter.javacode:@Override
        public void becomesDetachedOnPageSelected() {
          // 兜底
          handleGuide(false);
          // detach重置角标状态
          mAtlasPositionOriginVisible = View.GONE;
          if (mSpeedGuideInAnim != null && mSpeedGuideOutAnim != null) {
            mSpeedGuideInAnim.cancel();
            mSpeedGuideOutAnim.cancel();
          }
          if (SwitchConfigManager.getInstance().getBooleanValue(
              CoronaAllConfigKey.SERIAL_LONG_PRESS_AUTO_PLAY, false)
              && CoronaSerialUtil.isStandardSerial(mPhoto)) {
            if (mIsLongPressing.get()) {
              // 长按倍速下
              // 兜底恢复原始倍速
              changeSpeed(PhotoSpeedPlaySource.LONG_PRESS, mOriginSpeed);
            }
            // 发送一个取消事件,打断一下滑动事件序列
            // (不然滑动后,合集自动播放下一集的时候ViewPager的位置会有变化)
            // 为何要post下呢?有可能会报FragmentManager is already executing transactions,下一个信号执行,保证事务执行完成
            getRootView().post(() -> dispatchTouchCancelEvent());
          }
          ActivityDestroyOptHelper.disableRunIfFinishingJava(getActivity(), () -> {
            mIsAttached = false;
            handleLongPressStatus(false, true);
            mSpeedView.setPressListener(null);
            KLogDetail.get().i(TAG, "兜底恢复倍速状态" + mPhoto.getUserName());
            mSlidePlayViewModel.unregisterPageListObserver(mPageListObserver);
            mSidebarStatusHelper.removeDetailSidebarStatusListener(mSidebarStatusListener);
          });
        }
path:ks-features/ft-feed/detail/src/com/yxcorp/gifshow/detail/slidev2/widget/PressControlSpeedHelper.ktcode:fun dispatchTouchEvent(ev: MotionEvent): Boolean {
    KLogDetail.get().i(TAG, "helper dispatchTouchEvent action ${ev.action}, ${ev.rawX} ${ev.rawY}" +
        "${ev.x} ${ev.y}")
    if (ev.action == MotionEvent.ACTION_DOWN) {
      resetState(true)
      LongPressChecker.recordDownEvent(ev)
      KLogDetail.get().i(TAG, "触摸屏幕还原$mIsLongPressing")
      mRealEnableTouch = mEnableTouch
      mDownX = ev.rawX
      mDownY = ev.rawY
    }
    if (!mRealEnableTouch) {
      KLogDetail.get().i(TAG, "拦截事件" + ev.action)
      return false
    }
    if (!disableLowPhonePressSpeedPlayOpt) {
      var width = 64
      if (NasaExperimentUtils.longPressSpeedZoneSize > 0) {
        width = when {
          RightActionBarAdaptUtils.getWidthDp(context) <= 360 -> NasaExperimentUtils.longPressSpeedZoneSize
          RightActionBarAdaptUtils.getWidthDp(context) >= 390 -> NasaExperimentUtils.longPressSpeedZoneSize + 20
          else -> NasaExperimentUtils.longPressSpeedZoneSize + 10
        }
      }
      if (PadUtil.isPadAdaptation()) {
        width = PadSpeedPlayManager.getShadowWidthDpValue().toInt()
      }
      if (mSidebarWidth != 0) {
        width = SIDEBAR_AREA_WIDTH
      }
      if (ev.action == MotionEvent.ACTION_DOWN) {
        var deviceWidth = ViewUtil.getDisplayWidth(context as Activity)
        var deviceHeight = ViewUtil.getDisplayHeight(context)
        if (deviceWidth == 0) {
          deviceWidth = FoldScreenDeviceUtils.getScreenShortAxis()
        }
        if (deviceHeight == 0) {
          deviceHeight = FoldScreenDeviceUtils.getScreenLongAxis()
        }
        if (PadUtil.isPadAdaptation()) {
          // Pad上存在横竖屏切换，所以要实时赋值
          deviceWidth = PadSizeUtil.getScreenWidth()
          deviceHeight = PadSizeUtil.getScreenHeight()
        }
        val sidebarWidth = if (mSidebarWidth == -1) 0 else mSidebarWidth
        val leftRect = Rect(0, 0, CommonUtil.dip2px(width.toFloat()), deviceHeight)
        val rightRect = Rect(deviceWidth - sidebarWidth - CommonUtil.dip2px(width.toFloat()),
            0, deviceWidth - sidebarWidth, deviceHeight)
        if ((mSlideSmallScreenLongPressSpeedType == 2 && mSidebarWidth != 0) ||
          leftRect.contains(ev.rawX.toInt(), ev.rawY.toInt()) || rightRect.contains(ev.rawX.toInt(), ev.rawY.toInt())) {
          mIsInTouchArea = true
          KLogDetail.get().i(TAG, "在长按倍速区域内")
        } else {
          mIsInTouchArea = false
          KLogDetail.get().i(TAG, "不在长按倍速区域内:$leftRect--$rightRect--${ev.rawX.toInt()}--${
            ev.rawY
                .toInt()
          }")
        }
      }

      if (mIsInTouchArea) {
        handleSpeedGesture(ev)
      } else {
        if (!disableLowPhonePressSpeedPlayOpt) {
          handleMoreGesture(ev)
        }
      }
      return mIsInTouchArea
    }
    return false
  }
path:ks-features/ft-social/nearby-common/src/main/java/com/yxcorp/gifshow/local/sub/entrance/kingkong/view/HomeEnterCoordinatorLayout.javacode:@Override
  public boolean dispatchTouchEvent(MotionEvent ev) {
    switch (ev.getActionMasked()) {
      case MotionEvent.ACTION_DOWN:

        mLastMotionX = ev.getX();
        mLastMotionY = ev.getY();
        mIsTouch = true;
        mIsHorDragged = false;
        mIsVerDragged = false;
        break;
      case MotionEvent.ACTION_MOVE:
        if (mIsHorDragged || mIsVerDragged) {
          break;
        }

        final int x = (int) ev.getX();
        final int y = (int) ev.getY();
        final int xDiff = (int) Math.abs(x - mLastMotionX);
        final int yDiff = (int) Math.abs(y - mLastMotionY);
        if (yDiff > xDiff && yDiff > mTouchSlop) {
          mIsVerDragged = true;
        } else if (xDiff > mTouchSlop && isIncludeClip(mLastMotionX, mLastMotionY)) {
          mIsHorDragged = true;
        }
        mLastMotionX = x;
        mLastMotionY = y;
        break;
      case MotionEvent.ACTION_UP:
        if (mRefreshLayout != null && mIsHorDragged) {
          mIsHorDragged = false;
          mRefreshLayout.onTouchEvent(ev);
        }
        mLastMotionY = 0;
        mIsTouch = false;
        mIsHorDragged = false;
        mIsVerDragged = false;
        break;
    }
    //处理侧滑layout事件
    disposeSliding(ev);
    //处理viewpager事件
    disposeViewPager(ev);
    return super.dispatchTouchEvent(ev);
  }
path:ks-applications/kwai-android/src/androidTest/java/com/yxcorp/gifshow/test/feed/log/flowoperateloc/SlidePlayFlowOperateLocLogTest.javacode:@Test
  public void testSlidePlayFlowOperateLocLog() {
    Log.i(TAG, "testSlidePlayFlowOperateLocLog");

    HomeFeaturedMilanoContainerFragment nasaSlidePlayFragment = (HomeFeaturedMilanoContainerFragment) mFragmentAttachRule
        .findFrag(f -> f instanceof HomeFeaturedMilanoContainerFragment);

    List<ClientEvent.ShowEvent> showEventList = mLogCollectHelper.getDataList();
    AtomicReference<VerticalViewPager> viewPagerAtomicReference = new AtomicReference<>();
    ViewInteraction viewInteraction;
    viewInteraction = Espresso.onView(ViewMatchers.withClassName
        (StringEndsWith.endsWith("KwaiGrootViewPager")));


    // 步骤1: 冷启动到精选页上报1条FlowOperateLoc日志
    viewInteraction.check((view, noViewFoundException) -> {
      viewPagerAtomicReference.set((VerticalViewPager) view);
          doCheckResult("进入到精选页", "mock_ks_order_1",
              FeedExt.getCommonMeta(getCurrPhoto(nasaSlidePlayFragment).getEntity()).mKsOrderId,
              showEventList, 1);
        }
    );

    // 步骤2：往下滑动一屏 新增1条(共2条)FlowOperateLoc日志
    viewPagerAtomicReference.get().addOnPageChangeListener(
        getOnPageChangeListener(nasaSlidePlayFragment, viewPagerAtomicReference.get(),
            "滑动到第二屏", "mock_ks_order_2",
            showEventList, 2));
    viewPagerAtomicReference.get().post(
        () -> SlidePlayViewModel.get(nasaSlidePlayFragment).playNext(true));
    waitToPageChange(nasaSlidePlayFragment);

    // 步骤3：再往下滑动一屏 没有新增(依然2条)FlowOperateLoc日志
    viewPagerAtomicReference.get().addOnPageChangeListener(
        getOnPageChangeListener(nasaSlidePlayFragment, viewPagerAtomicReference.get(),
            "滑动到第三屏", "",
            showEventList, 2));
    viewPagerAtomicReference.get().post(
        () -> SlidePlayViewModel.get(nasaSlidePlayFragment).playNext(true));
    waitToPageChange(nasaSlidePlayFragment);
  }
path:ks-applications/kwai-android/src-perf-aspectj/yxcorp/gifshow/apm/SysTraceHelper.javacode:能在systrace上监控
      + "|| execution(* com.yxcorp.gifshow.recycler.fragment.RecyclerFragment+.*(..))"
      + "|| execution(* com.kwai.framework.network.KwaiParams.*(..))"
      + "|| execution(*  com.kuaishou.android.vader.Vader.*(..))"
      + "|| execution(*  com.yxcorp.gifshow.nasa.featured.*(..))"
      + "|| execution(*  com.yxcorp.gifshow.homepage.presenter.splash.SplashPresenter.on*(..))"
      + "|| execution(* com.yxcorp.gifshow.apm.TabApmTracker.on*(..))"
      + "|| execution(* com.kwai.framework.player..*(..))"
      + "|| execution(* com.kwai.framework.player_kpmid..*(..))"
      + "|| execution(* com.yxcorp.gifshow.detail.slidev2..*(..))"
      + "|| execution(* com.yxcorp.gifshow.detail.slideplay..*(..))"
      + "|| execution(* com.kwai.video.player.mid.builder..*(..))"
      + "|| execution(* com.kwai.video.player.kwai_player..*(..))"
      + "|| execution(* android.text.TextUtils.*(..))"
      + "|| execution(* com.kwai.video.player.kwai_player..*(..))"
      + "|| execution(* com.kwai.video.player.mid.config..*(..))"
      + "|| execution(* com.kwai.video.player.mid.manifest..*(..))"
      + "|| execution(* com.kwai.video.player.mid.util..*(..))"
      + "|| execution(* com.kwai.library.groot.api.viewmodel.SlidePlayViewModel.*(..))"
      + "|| execution(* com.yxcorp.gifshow.util.GrootSwitchUtils.*(..))"
      + "|| execution(* com.kwai.sdk.switchconfig..*(..))"
      + "|| execution(* com.kwai.framework.testconfig.*(..))"
      + "|| execution(* com.yxcorp.gifshow.detail.presenter..*(..))"
      + "|| execution(* com.yxcorp.gifshow.featured.detail..*(..))"
      + "|| execution(* com.kwai.component.photo.detail.slide..*(..))"
      + "|| execution(*  com.yxcorp.gifshow.detail.nonslide..*(..))"
      + "|| execution(*  com.gifshow.kuaishou.floatwidget..*(..))"
      + "|| execution(*  com.kwai.slide.play.detail..*(..))"
      + "|| execution(*  com.kwai.library.slide.base..*(..))"
      + "|| execution(*  com.kuaishou.android.model..*(..))"
      + "|| execution(*  com.kuaishou.live.core.show.gift..*(..))"
      + "|| execution(*  com.yxcorp.gifshow.reminder.nasa..*(..))"
      + "|| execution(*  com.yxcorp.experiment..*(..))"
      + "|| execution(*  com.kwai.sdk.switchconfig..*(..))"
      + "|| execution(*  com.kuaishou.commercial..*(..))"
      + "|| execution(*  com.mini.entrance.initmodule..*(..))"
      + "|| execution(*  com.xiaomi.push.service..*(..))"
      + "|| execution(*  com.yxcorp.gifshow.pendant.tk.bridge.PendantNative2JsInvoker..*(..))"
      + "|| execution(*  com.yxcorp.gifshow.pendant.widget.TkTaskPendant..*(..))"
      + "|| execution(*  com.tachikoma.core.bridge.TKJSContext..*(..))"
      + "|| execution(*  com.kwai.library.groot..*(..))"
      + "|| execution(*  com.yxcorp.gifshow.detail.slideplay.nasa.vm.SlidePage.*(..))"
      + "|| execution(*  com.kwai.slide.play.detail.base..*(..))"
      + "|| execution(*  com.kuaishou.gifshow.kswebview..*(..))"
      + "|| execution(*  com.kuaishou.webkit..*(..))"
      + "|| execution(*  com.kwai.yoda..*(..))"
  )
  public void executionPoint() {
  }