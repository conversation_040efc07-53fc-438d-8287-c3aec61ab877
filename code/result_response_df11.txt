path:ks-features/ft-search/search-base/src/main/java/com/yxcorp/plugin/search/utils/FrameDelayTask.javacode:@Override
      public void doFrame(long frameTimeNanos) {
        t.mFrameCount++;
        if (t.mFrameCount < DELAY_FRAME_COUNT) {
          Choreographer.getInstance().postFrameCallback(this);
        } else {
          try {
            t.isExecuted = true;
            executeTask(t);
          } catch (Throwable throwable) {
            SearchExceptionMonitor.monitorException("FrameDelayTask",
                "executeTask exception: " + throwable);
          } finally {
            Choreographer.getInstance().removeFrameCallback(this);
          }
        }
      }
path:ks-features/ft-corona/corona-krn/src/main/java/com/yxcorp/gifshow/corona/krn/view/CoronaConnerFrameLayout.javacode:@Override
      public void doFrame(long frameTimeNanos) {
        manuallyLayoutChildren();
        getViewTreeObserver().dispatchOnGlobalLayout();
        Choreographer.getInstance().postFrameCallback(this);
      }
path:ks-features/ft-live/live-features/live-anchor/src/main/java/com/kuaishou/live/core/show/performance/LiveFrameRateMonitor.javacode:@Override
  public void doFrame(long frameTimeNanos) {
    mFrameCount++;
    if (mIsDetecting) {
      // 注册下一帧回调
      Choreographer.getInstance().postFrameCallback(this);
    }
  }
path:ks-components/scheduler-tti/src/main/java/com/kwai/component/tti/IdleHandlerTask.javacode:@Override
        public void doFrame(long frameTimeNanos) {
          if (frameTimeNanos - mLastCheckTime < AVAILABLE_FRAME_INTERVAL) {
            mAvailableCheckTimes++;
          } else {
            mAvailableCheckTimes = 0;
          }
          Log.i(TAG, "gap = " + (frameTimeNanos - mLastCheckTime));

          mLastCheckTime = frameTimeNanos;
          mTotalCheckTimes++;

          Log.i(TAG, "mAvailableCheckTimes = " + mAvailableCheckTimes + "; mTotalCheckTimes = " + mTotalCheckTimes);
          if (mAvailableCheckTimes < AVAILABLE_COUNT && mTotalCheckTimes < MAX_CHECK_COUNT) {
            Choreographer.getInstance().postFrameCallback(this);
            return;
          }

          mTotalCheckTimes = 0;
          mAvailableCheckTimes = 0;

          boolean result = queueIdle();
          if (result) {
            Choreographer.getInstance().postFrameCallback(this);
          } else {
            Choreographer.getInstance().removeFrameCallback(this);
          }
        }
path:ks-components/scheduler-tti/src/main/java/com/kwai/component/tti/IdleHandlerTask.javacode:public void start() {
    if (TTI_MAIN_TASK_SCHEDULE_STRATEGY_SUPPLIER.get() == CHOREOGRAPHER_STRATEGY) {
      Choreographer.getInstance().postFrameCallback(new Choreographer.FrameCallback() {
        private static final int AVAILABLE_COUNT = 5; // 连续5帧流畅
        private static final int MAX_CHECK_COUNT = 300; // 300帧，5s间隔
        private static final long AVAILABLE_FRAME_INTERVAL = 42_000_000L; // 最大间隔时间 42ms

        private int mAvailableCheckTimes = 0;
        private int mTotalCheckTimes = 0;

        private long mLastCheckTime;

        @Override
        public void doFrame(long frameTimeNanos) {
          if (frameTimeNanos - mLastCheckTime < AVAILABLE_FRAME_INTERVAL) {
            mAvailableCheckTimes++;
          } else {
            mAvailableCheckTimes = 0;
          }
          Log.i(TAG, "gap = " + (frameTimeNanos - mLastCheckTime));

          mLastCheckTime = frameTimeNanos;
          mTotalCheckTimes++;

          Log.i(TAG, "mAvailableCheckTimes = " + mAvailableCheckTimes + "; mTotalCheckTimes = " + mTotalCheckTimes);
          if (mAvailableCheckTimes < AVAILABLE_COUNT && mTotalCheckTimes < MAX_CHECK_COUNT) {
            Choreographer.getInstance().postFrameCallback(this);
            return;
          }

          mTotalCheckTimes = 0;
          mAvailableCheckTimes = 0;

          boolean result = queueIdle();
          if (result) {
            Choreographer.getInstance().postFrameCallback(this);
          } else {
            Choreographer.getInstance().removeFrameCallback(this);
          }
        }
      });
    } else {
      //兼容低版本api
      Handler handler = new Handler(Looper.getMainLooper());
      handler.post(() -> Looper.myQueue().addIdleHandler(IdleHandlerTask.this));
    }
  }
path:ks-components/page-cost-record/src/main/java/com/yxcorp/gifshow/page/cost/decorators/AutoEndUIStageByChoreographer.javacode:@Override
                public void invokeFailure() {
                    //Choreographer的调用出现了异常，通过数据层面（负的UI耗时）体现这一问题
                    Log.e(TAG, "onPinAdded: failure");
                    if (getTracer() != null) {
                        getTracer().end(StageRegistry.Required.UI, now - 200);
                    }
                }
path:ks-components/photo-detail/detail-awesomedispatch/src/main/java/com/kwai/component/photo/detail/awesomedispatch/dispatch/AwesomeDispatchManager.javacode:@Override
    public void run() {
      running = true;
      PerformanceTask currCpuLevel = null;
      PerformanceTask currGpuLevel = null;
      PerformanceTask currIoLevel = null;
      HashSet<PerformanceTask> currBindCore = new HashSet<>();
      long nextWakeInterval = DEFAULT_WAKE_INTERVAL;
      final ArrayList<PerformanceTask> listStartTask = new ArrayList<>();
      final HashMap<Integer, PerformanceTaskStop> setStopTask = new HashMap<>();
      final HashSet<Integer> setStartedTask = new HashSet<>();

      while (running) {
        final long startLoop = System.currentTimeMillis();
        final int queueSize = taskQueue.size();
        AwesomeDispatchLog.d(TAG, "startLoop 任务队列大小:" + queueSize + " startTask:" + listStartTask.size() +
            " 下一次唤醒时间:" + nextWakeInterval);

        for (int i = 0; i < (queueSize == 0 ? 1 : queueSize); i++) {
          Object object = null;
          try {
            object = taskQueue.poll(nextWakeInterval, TimeUnit.MILLISECONDS);
          } catch (Exception e) {
            AwesomeDispatchLog.e(TAG, "taskQueue poll: " + e.getMessage());
            object = null;
          }
          if (object == null) {
            break;
          } else if (object instanceof PerformanceTask) {
            listStartTask.add((PerformanceTask) object);
            setStartedTask.add(((PerformanceTask) object).hashCode());
          } else if (object instanceof PerformanceTaskStop) {
            int hashCode = ((PerformanceTaskStop) object).hashCode;
            if (setStartedTask.contains(hashCode)) {
              setStopTask.put(hashCode, (PerformanceTaskStop) object);
            }
          } else {
            AwesomeDispatchLog.e(TAG, "taskQueue poll invalid object");
          }
        }
        nextWakeInterval = DEFAULT_WAKE_INTERVAL;

        PerformanceTask maxCpuLevel = null;
        PerformanceTask maxGpuLevel = null;
        PerformanceTask maxIoLevel = null;
        HashSet<PerformanceTask> needBindCore = new HashSet<>();
        final long now = System.currentTimeMillis();

        AwesomeDispatchLog.d(TAG, "InLoop startSize:" + listStartTask.size());

        for (int i = 0; i < listStartTask.size(); i++) {
          final PerformanceTask task = listStartTask.get(i);
          final long curProcessTime = System.currentTimeMillis();
          if (task == null) {
            continue;
          }

          if (setStopTask.containsKey(task.hashCode())) {
            task.stopTime = curProcessTime;
            setStopTask.remove(task.hashCode());
          }

          final long lifeLeftInterval = task.stopTime - curProcessTime;
          if (lifeLeftInterval <= 0) {
            AwesomeDispatchLog.d(TAG, "任务结束的时间 InLoop STOP:" + i + "/" + listStartTask.size() + " task:" +
                task.toString(curProcessTime));
            listStartTask.remove(task);
            setStartedTask.remove(task.hashCode());
            i--;
            continue;
          }
          nextWakeInterval = Math.min(nextWakeInterval, lifeLeftInterval);

          final long waitStartInterval = task.startTime - curProcessTime;
          if (waitStartInterval > 0) {  //  task waiting start
            AwesomeDispatchLog.d(TAG, "InLoop WAIT:" + i + "/" + listStartTask.size() + " task:" +
                task.toString(curProcessTime));
            nextWakeInterval = Math.min(nextWakeInterval, waitStartInterval);
            continue;
          }

          AwesomeDispatchLog.d(TAG, "InLoop RUN:" + i + "/" + listStartTask.size() + " task:" +
              task.toString(curProcessTime));

          if (task.cpuLevel > 0
              && ((maxCpuLevel == null) || (maxCpuLevel.cpuLevel > task.cpuLevel) ||
              (maxCpuLevel.cpuLevel == task.cpuLevel && maxCpuLevel.stopTime < task.stopTime))) {
            AwesomeDispatchLog.d(TAG, "maxCpuLevel");
            maxCpuLevel = task;
          }

          if (task.gpuLevel > AwesomeDispatch.GPU_LEVEL_0
              && ((maxGpuLevel == null) || (maxGpuLevel.gpuLevel > task.gpuLevel) ||
              (maxGpuLevel.gpuLevel == task.gpuLevel && maxGpuLevel.stopTime < task.stopTime))) {
            AwesomeDispatchLog.d(TAG, "maxGpuLevel");
            maxGpuLevel = task;
          }
          if (task.ioLevel > AwesomeDispatch.IO_LEVEL_0
              && ((maxIoLevel == null) || (maxIoLevel.ioLevel > task.ioLevel) ||
              (maxIoLevel.ioLevel == task.ioLevel && maxIoLevel.stopTime < task.stopTime))) {
            AwesomeDispatchLog.d(TAG, "maxIoLevel");
            maxIoLevel = task;
          }

          for (int tid : task.bindTids) {
            if (