path:ks-kernels/kwai-players/src/main/java/com/kwai/framework/player/core/KwaiMediaPlayerWrapper.javacode:protected void attachKwaiMediaPlayer(@NonNull KwaiMediaPlayer kwaiMediaPlayer,
      boolean notifyPlayer) {
    logMsg("attach player: " + kwaiMediaPlayer + " , notify " + notifyPlayer);
    if (kwaiMediaPlayer == null) {
      Throwable throwable = new IllegalArgumentException("attach player is null!!");
      ExceptionHandler.handleCaughtException(throwable);
      Async.submit(new ReportRunnable("attach null player", throwable));
      return;
    }
    // 保证一个资源的回收
    innerDetach();
    mKwaiMediaPlayer = kwaiMediaPlayer;
    if (mKwaiMediaPlayer != null) {
      mLoggerWrapper.attach(mKwaiMediaPlayer.getPlayerLogger());
    }
    registerPlayerListeners();
    if (notifyPlayer) {
      notifyPlayer();
    }

    Set<PlayerAttachChangeListener> listeners =
        mPlayerWrapperListenerWrapperListenerProxy.getListeners();
    if (listeners != null) {
      for (PlayerAttachChangeListener tmp : listeners) {
        tmp.onAttach();
      }
    }
  }
path:ks-features/ft-growth/float-widget/src/nebula/java/com/gifshow/kuaishou/floatwidget/presenter/FloatWidgetGoldCoinTimerPresenter.javacode:@Override
  protected void onBind() {
    mSlidePlayViewModel = SlidePlayViewModel.getNullable(mFragment.getParentFragment());
    mOldPlayerState = KwaiMediaPlayer.PLAYER_STATE_IDLE;
    if (mSlidePlayViewModel != null) {
      mSlidePlayViewModel.addListener(mFragment, mAttachChangedListener);
    } else if (mAttachListeners != null && !mAttachListeners.contains(mAttachChangedListener)) {
      mAttachListeners.add(mAttachChangedListener);
    }
    if (mSwipeProfileInterceptorList != null) {
      mSwipeProfileInterceptorList.add(mSwipeProfileInterceptor);
    }

    if (mPhoto.isVideoType() ||
        (PendantExpHelper.INSTANCE.enablePendantShowFromImageFeed() && mPhoto.isImageType())) {
      mPlayModule.getPlayer().addPlayerStateChangedListener(mStateChangedListener);
      mPlayModule.getPlayer().addOnInfoListener(mOnInfoListener);
      PluginManager.get(NebulaLoggerPlugin.class).playerStateStart(mPhoto.getPhotoId());
    }
    addToAutoDisposes(RxBus.INSTANCE.toObservable(LoginEvent.class, RxBus.ThreadMode.MAIN)
        .subscribe(this::onLoginEventMainThread));
  }
path:ks-components/qphoto-player/src/main/java/com/yxcorp/gifshow/detail/qphotoplayer/impl/QPhotoSwitchMediaPlayerImpl.javacode:public void attachQPhotoMediaPlayer(QPhotoMediaPlayer qPhotoMediaPlayer, QPhoto qPhoto) {
    mQPhotoMediaPlayer = qPhotoMediaPlayer;
    getPlayerLogger().setDuration(QPhotoContentUtil.getVideoDuration(qPhoto));
    setLooping(!PayVideoModelUtil.isTrailPlay(qPhoto.getEntity()));
    setScreenOnWhilePlaying(true);

    attachKwaiMediaPlayer(mQPhotoMediaPlayer);
    if (mFirstAttachPlayer) {
      mFirstAttachPlayer = false;
    } else {
      for (OnMediaPlayerChangedListener listener : mOnMediaPlayerChangeListeners) {
        listener.onMediaPlayerChanged();
      }
    }
  }
path:ks-kernels/kwai-players/src/main/java/com/kwai/framework/player/core/KwaiMediaPlayerWrapper.javacode:protected void attachKwaiMediaPlayer(@NonNull KwaiMediaPlayer kwaiMediaPlayer) {
    attachKwaiMediaPlayer(kwaiMediaPlayer, true);
  }
path:ks-features/ft-x/gamecenter/src/main/java/com/kwai/game/core/subbus/gamecenter/player/ZtGameMediaPlayerImpl.javacode:@Override
  protected void attachKwaiMediaPlayer(KwaiMediaPlayer kwaiMediaPlayer) {
    super.attachKwaiMediaPlayer(kwaiMediaPlayer);
    if (mSetMuteBeforeAttach) {
      setPlayerMute();
    }
  }
path:ks-features/ft-live/live-external/live-playback/src/main/java/com/kuaishou/live/playback/playmodule/impl/LivePlaybackMediaPlayerImpl.javacode:public void attachQPhotoMediaPlayer(
      QPhotoMediaPlayer qPhotoMediaPlayer, QPhoto qPhoto) {
    mCurrentQPhoto = qPhoto;
    mQPhotoMediaPlayer = qPhotoMediaPlayer;
    getPlayerLogger().setDuration(QPhotoContentUtil.getVideoDuration(qPhoto));
    attachKwaiMediaPlayer(mQPhotoMediaPlayer);
    if (mQPhotoMediaPlayer != null) {
      for (OnPlaySourceSwitchListener listener : mOnPlaySourceSwitchListeners) {
        mQPhotoMediaPlayer.addOnPlaySourceSwitchListener(listener);
      }
      mQPhotoMediaPlayer.addQualityChangeListener(qualityChangeListener);
    }

    if (mFirstAttachPlayer) {
      mFirstAttachPlayer = false;
    } else {
      for (LivePlaybackOnMediaPlayerChangedListener listener : mOnMediaPlayerChangeListeners) {
        listener.onMediaPlayerChanged();
      }
    }
  }
path:ks-features/ft-live/live-features/live-audience/src/main/java/com/kuaishou/live/audience/component/multipk/LiveAudienceMultiPkPresenter.ktcode:recordRenderTime(LiveComponentBizType.LIVE_PK)
      }

      override fun getUserTag(userId: String?): String? {
        return liveServiceManager.getService(LiveInteractiveUserTagService::class.java)
          .getUserTag(userId)
      }

      override fun getGiftPanelItems(): List<GiftPanelItemInfo>? {
        if (liveAudienceGiftBoxService == null) {
          return liveGiftBoxService?.getCacheItemData()
        } else {
          liveAudienceGiftBoxService?.giftPanelItems?.let {
            val giftList = ArrayList<GiftPanelItemInfo>()
            for (giftPanelItem in it) {
              // 因为只给pk触发的快捷礼物用，很多字段都是用不到的，因此传null即可
              val giftInfo = GiftPanelItemInfo(
                0,
                0,
                giftPanelItem.gift,
                giftPanelItem.mItemType,
                UIGiftPanelItem(),
                GiftPanelItemAction(0, null, null),
                GiftPanelItem(giftPanelItem.mItemType),
                "",
                "",
                null,
                null,
                false
              )
              giftList.add(giftInfo)
            }
            return giftList
          }
          return null
        }
      }

      override fun registerUserTagUpdateListener(listener: LiveInteractiveUserTagUpdateListener) {
        liveServiceManager.getService(LiveInteractiveUserTagService::class.java)
          .registerUpdateListener(listener)
      }

      override fun unregisterUserTagUpdateListener(listener: LiveInteractiveUserTagUpdateListener) {
        liveServiceManager.getService(LiveInteractiveUserTagService::class.java)
          .unregisterUpdateListener(listener)
      }

      override fun getChatId(): String {
        return liveServiceManager.getService(LiveMultiInteractManager::class.java)
          .liveInteractRtcManager
          .chatId ?: TextUtils.EMPTY_STRING
      }

      override fun showMultiPkPreparePanel(startPkExtraInfo: LiveMultiPkStartPkExtraInfo) {
        // 观众端空实现
      }

      override fun openPkWishListEntry(@LiveMultiPKWishListType wishListType: String) {
        if (TextUtils.equals(wishListType, LiveMultiPKWishListType.WISH_LIST_PANEL)) {
          mLiveAudienceWishListService.showWishListFragment()
        }
      }

      override fun getCellConfig(
        isDuetPk: Boolean,
        isAnchorSelf: Boolean
      ): LiveMultiPkCellConfig {
        return if (isAnchorSelf) { // 当前主播的样式和其他主播不同
          LiveMultiPkCellConfig(
            showUserInfo = !isDuetPk, // 双人pk不展示当前主播的信息条
            showMuteIcon = false,
            showFollowIcon = false
          )
        } else {
          LiveMultiPkCellConfig(
            showUserInfo = true,
            showMuteIcon = true,
            showFollowIcon = true
          )
        }
      }

      override fun getCurrentServerTime(): Long {
        return liveServerTimeProvider.currentServerTimeMs
      }

      override fun updateRecommendGiftView(event: LiveRecommendGiftEvent) {
        recommendGiftService?.apply {
          if (event.resetBottomBar) {
            resetBottomBarPkRecommendGiftView(event)
          } else {
            showRecommendGiftView(event)
          }
        }
      }

      override fun subscribeShowSprintAnimEvent(): Observable<LiveRecommendSendGiftEvent>? {
        return recommendGiftService?.subscribeSendGiftEvent()
      }

      override fun observeRecommendGiftInfo(): Observable<LivePkRecommendGiftInfo>? {
        return mLiveAudienceStatusObtainService.timeConsumingAudienceStatus
          .filter { it.mLivePkRecommendGiftInfo != null }
          .map { it.mLivePkRecommendGiftInfo }
      }

      override val currentMagicFaceId: String
        get() = ""

      override fun registerRouter(host: String, router: LiveRouterCallback) {
        liveRouterManager.registerRouter(host, router)
      }

      override fun unregisterRouter(host: String) {
        liveRouterManager.unregisterRouter(host)
      }

      override fun getExtensionLogParam(): Map<String, String> {
        return mapOf("livePlayFragmentId" to liveAudienceParam?.mLivePlayFragmentId.toString())
      }

      override fun onShowMvpUserInfoView() {
        showMpvInfoViewSubject.onNext(true)
      }

      /**
       * 是否允许降级（观众端显示降级 UI）
       * 默认为 true
       */
      override fun isDowngradeEnabled(): Boolean {
        return SwitchConfigManager.getInstance().getSource(LiveSwitchConfigConstant.SOURCE_LIVE)
          .getBooleanValue(LiveSwitchConfigKey.ENABLE_AUDIENCE_MULTI_INTERACT_PREF_DOWNGRADE, true)
      }

      override val deviceScore: Int
        get() = <EMAIL>

     