path:ks-components/qphoto-player/src/main/java/com/kwai/components/playerkit/QPhotoPlayerKitDataSource.javacode:@Override
  public void attachTo(@NotNull WayneBuildData buildData) {
    // 主站设置的是retry count 从0开始。而实际给播放器是从1开始。在之前的逻辑中在创建播放器时会对index++。看这个是否放到中间件呢？
    buildData.setPlayIndex(buildData.getPlayIndex() + 1);
    //透传reco 预估的作品观看时长
    if (mBuildData != null && mBuildData.mQPhoto != null) {
      buildData.setBizStrategyData(SERVER_WATCH_TIME, mBuildData.mQPhoto.getWatchTime());
      if (mBuildData.mQPhoto.getEntity() != null) {
        buildData.setBizStrategyData(APP_WATCH_TIME,
            FeedExt.getPredictWatchTime(mBuildData.mQPhoto.getEntity()));
      }
    }
    // 目前有两处用到新中间件的地方会使用到这个listener，是否能都放到PlayerBuildData 里面去设置。是否合理呢？
    buildData.setInnerPlayerLifeCycleListener(new InnerPlayerLifeCycleListener() {
      @Override
      public void onPlayerCreating(@NonNull KwaiPlayerVodBuilder kwaiPlayerVodBuilder) {
//        if (mBuildData.mPlayerExtBuilderConfigInvoker != null) {
//          mBuildData.mPlayerExtBuilderConfigInvoker.config(kwaiPlayerVodBuilder);
//        }
      }

      @Override
      public void onPlayerCreated(@NonNull IKwaiMediaPlayer iKwaiMediaPlayer, int i,
          String s) {
        Singleton.get(LaunchTracker.class).onKernelPlayerCreate();
      }

      @Override
      public void onPlayerReleased() {
      }

      @Override
      public void onPlayerPriorityChanged(@Nullable InstancePriority oldPriority,
          InstancePriority newPriority) {
      }

    });
    // 在播放qphoto 之前更新一下数据源。对齐 qphoto player设置
    // 作者自己发的作品,查看是否有缓存,如果有,优先使用本地缓存.
    if (mBuildData.mQPhoto.isVideoType() && mBuildData.mQPhoto.isMine()) {
      mBuildData.mQPhoto.updateLocalFileIfAny();
    }
    mMediaTypeAttached = QPhotoMediaType.getMediaType(mBuildData.mQPhoto);
    // 这个应该放在最后，因为 config会创建switcher。没有设置的配置会影响switcher的创建。
    configBuildDataWithQPhotoMediaType(mMediaTypeAttached, buildData, mBuildData);
    // wayneimpl
    // 如果后续下掉adapter的代码，一定要注意这里的写法。
    KpMidUtil3.configDataFetcherForWayneImpl(buildData, buildData.getSwitcher());
  }
path:ks-features/ft-platform/kwai-init/src/main/java/com/yxcorp/gifshow/init/module/ThreadInverseInitModule.ktcode:private fun startPriorityInversion() {
    RxObservableUtils.dispose(disposableTimer)

    disposableTimer = Observable.timer(10, TimeUnit.MILLISECONDS)
      .repeat(Long.MAX_VALUE)        // 一直循环
      .subscribeOn(KwaiSchedulers.ASYNC)
      .doOnDispose {
        // 运行在主线程
        KLogKwaiInit.get().i(TAG, "onDispose")
        Async.submit {
          processDispose()
        }
      }
      .doOnComplete {
        KLogKwaiInit.get().i(TAG, "onComplete")    // 理论上不会执行到这里
      }
      .subscribe {
        // 每间隔10ms执行一次
        try {
          val beginTimestamp = System.currentTimeMillis()
          val ownerThread = getLockOwnerThread(Looper.getMainLooper().thread)  // 获取主线程是否被锁
          tryAlterThreadPriority(ownerThread, beginTimestamp)
        } catch (tr: Throwable) {
          val stringBuilder = StringBuilder()
          for (stackTraceElement in tr.stackTrace) {
            stringBuilder.append(stackTraceElement.toString())
            stringBuilder.append("\n")
          }
          KLogKwaiInit.get().i(TAG, "exception: $stringBuilder")

          Logger.logCustomEvent(
            "NASA_PREFETCH_CACHE_RERANK_ORDER",
            JsonStringBuilder.newInstance()
              .addProperty("scene", "ThreadMonitorAnalyseInitModule")
              .addProperty("type", "exception")
              .build(),
            ClientEvent.CustomEvent.CustomEventBiz.CONSUME
          )
        }
      }
  }
path:ks-components/tuna-plc-base/src/main/java/com/yxcorp/gifshow/detail/plc/mix/PLCLogHelper.javacode:@Override
        public void becomesAttachedOnPageSelected() {
          mHasPlayedEndForFirstTime = false;
          mCurrentPlayTime = 0;
          mDuration = mPlayer == null ? 0 : mPlayer.getDuration();
          mProgressUpdateHandler = new ScheduleHandler(INTERVAL_MS, () -> {
            if (mDuration == 0) {
              mDuration = mPlayer == null ? 0 : mPlayer.getDuration();
            }
            mCurrentPlayTime += INTERVAL_MS;
            if (!mHasPlayedEndForFirstTime && mCurrentPlayTime >= mDuration) {
              mHasPlayedEndForFirstTime = true;
              destroyProgressHandler();
            }
          });
          if (mPlayer != null) {
            mPlayerPlayOrNotHelper =
                new KwaiPlayerPlayOrNotHelper(mPlayer, playing -> {
                  if (playing) {
                    startProgressHandler();
                  } else {
                    stopProgressHandler();
                  }
                });
            if (mPlayerPlayOrNotHelper.isPlaying()) {
              startProgressHandler();
            }
          }
        }
path:ks-kernels/kwai-players/src/main/java/com/kwai/framework/player/KsMediaPlayerInitModule.javacode:@Override
  public void execute() {
    final Application application = AppEnv.getAppContext();
    installPlayerLoader();

    try {
      setKsMediaPlayerInitConfig();
      KLogKwaiPlayers.get().i(LOG_TAG, "[PlayerLibrary] setAwesomeCacheInitConfig/setKsMediaPlayerInitConfig done.");
    } catch (Exception e) {
      e.printStackTrace();
      ExceptionHandler.handleCaughtException(e);
      KLogKwaiPlayers.get().i(LOG_TAG, "[PlayerLibrary] " + e);
    }

    UiPlugins.setContentFrameConfig(new KwaiContentFrameConfigImpl());
    RunnableSchedulerAfterLaunchFinish
        .scheduleRunInWorkThread(() -> LocalVideoInfoHelper.getInstance(), "LocalVideoInfo", true);
    GothamPluginsConfiger.configPlugins();
    installKwaiVPPDownloader();

    if (SwitchConfigManager.getInstance().getBooleanValue("initWayne", false)) {
      initWaynePlayer();
    }
    Runnable initRunable = new Runnable() {
      @Override
      public void run() {
        initWaynePlayer();
      }
    };
    InjectInitor.getInstance().injectInitorRunable(initRunable);

    // WebRTCPreload
    RunnableSchedulerAfterLaunchFinish
        .scheduleRunInWorkThread(PhotoPlayerConfig::preloadWebRTC, "WebRTCPreload", false);

    // PlayerCorePreload
    RunnableSchedulerAfterLaunchFinish
        .scheduleRunInWorkThread(PhotoPlayerConfig::preloadAemon, "PlayerCorePreload", false);

    RunnableSchedulerAfterLaunchFinish.scheduleRunInWorkThread(this::initVodKlpConfig,
        "initVodKlpConfig", Priority.HIGH, Collections.singletonList("ALL"));
  }
path:ks-components/photo-detail/detail-core/src/main/java/com/kwai/component/photo/detail/core/task/DetailCountDownPresenter.javacode:@Override
  protected void onBind() {
    addToAutoDisposes(RxBus.INSTANCE.toObservable(RestoreTaskEvent.class, RxBus.ThreadMode.MAIN)
        .subscribe(this::handleRestoreTaskEvent));
    mPlayerPlayOrNotHelper = new KwaiPlayerPlayOrNotHelper(mPlayModule.getPlayer(), playing -> {
      if (mTaskManager == null) {
        return;
      }
      int hashCode = mPlayerPlayOrNotHelper != null ? mPlayerPlayOrNotHelper.hashCode() : -1;
      KLogDetailCore.get().i("DetailCountDownPresenter",
          "onPlayingChange playing=" + playing + ",mPlayerPlayOrNotHelper=" + hashCode);
      //激励侧用于判断当前页面是否在播放
      PlayerFeedManager playerFeedManager = GetterToolkitsKt.getPlayerFeedManager();
      if (playing) {
        GetterToolkitsKt.getWatchPhotoCompleteManager().bindBaseFeed(mQPhoto);
        //激励侧用于判断当前页面正在播放的photo
        playerFeedManager.onPhotoPlay(getActivity(), mQPhoto.getEntity());
        mTaskManager.onPhotoPlay(getActivity(), mQPhoto.getEntity(), mFragment.getPage2());
      } else {
        playerFeedManager.onPhotoPause(getActivity(), mQPhoto.getEntity());
        mTaskManager.onPhotoPause(getActivity(), mQPhoto.getEntity());
      }
    });
    addToAutoDisposes(mFragment.getCompositeLifecycleState().observeReallySelect()
        .subscribe(aBoolean -> {
          int hashCode = mPlayerPlayOrNotHelper != null ? mPlayerPlayOrNotHelper.hashCode() : -1;
          KLogDetailCore.get().i("DetailCountDownPresenter",
              "observeReallySelect isSelect=" + aBoolean + ",mPlayerPlayOrNotHelper=" + hashCode);
          if (mTaskManager == null) {
            return;
          } else if (aBoolean) {
            mTaskManager.onPhotoPageSelected(getActivity(), mQPhoto.getEntity());
            return;
          }
          mTaskManager.onPhotoPause(getActivity(), mQPhoto.getEntity());
        }));
    boolean isReallySelect = mFragment.getCompositeLifecycleState().isReallySelect();
    int hashCode = mPlayerPlayOrNotHelper != null ? mPlayerPlayOrNotHelper.hashCode() : -1;
    boolean isPlaying = mPlayerPlayOrNotHelper != null && mPlayerPlayOrNotHelper.isPlaying();
    KLogDetailCore.get().i("DetailCountDownPresenter",
        "onBind isReallySelect=" + isReallySelect + "isPlaying=" + isPlaying +
            ",mPlayerPlayOrNotHelper=" + hashCode);
    if (isPlaying && mTaskManager != null) {
      GetterToolkitsKt.getWatchPhotoCompleteManager().bindBaseFeed(mQPhoto);
      GetterToolkitsKt.getPlayerFeedManager().onPhotoPlay(getActivity(), mQPhoto.getEntity());
      mTaskManager.onPhotoPlay(getActivity(), mQPhoto.getEntity(), mFragment.getPage2());
    }
    GetterToolkitsKt.getWatchPhotoCompleteManager().bindPlayerInfoListener(mPlayModule.getPlayer());
  }
path:ks-features/ft-feed/detail/src/com/yxcorp/gifshow/detail/slidev2/autoplay/presenter/BaseAutoPlayOptPresenter.ktcode:IsAttached) {
            return@subscribe
          }
          if (event.mStatus == QcrPanelEvent.DIALOG_SHOW) {
            notifyAutoPlaySetChange(true, AutoPlayStopType.STOP_BY_EMOTION_EDITOR_FLOAT_DIALOG)
          } else if (event.mStatus == QcrPanelEvent.DIALOG_DISMISS) {
            notifyAutoPlaySetChange(false, AutoPlayStopType.STOP_BY_EMOTION_EDITOR_FLOAT_DIALOG)
          }
        })

    //监听进度条拖动
    mMilanoContainerEventBus?.let {
      addToAutoDisposes(it.mProgressbarDragEvent.subscribe { isSeeking ->
        notifyAutoPlaySetChange(isSeeking, AutoPlayStopType.STOP_BY_SEEK_POSITION)
      })
    }

    // 监听共创面板
    addToAutoDisposes(
      RxBus.INSTANCE.toObservable(
        CoCreatePanelShowEvent::class.java
      )
        .observeOn(KwaiSchedulers.MAIN)
        .subscribe { event: CoCreatePanelShowEvent ->
          notifyAutoPlaySetChange(event.show, AutoPlayStopType.STOP_BY_COCREATE_PANEL_SHOW)
        })

    // 监听推荐面板
    addToAutoDisposes(
      RxBus.INSTANCE.toObservable(
        RecommendPanelShowEvent::class.java
      )
        .observeOn(KwaiSchedulers.MAIN)
        .subscribe { event: RecommendPanelShowEvent ->
          notifyAutoPlaySetChange(event.show, AutoPlayStopType.STOP_BY_RECOMMEND_PANEL_SHOW)
        })


    // 监听章节面板
    addToAutoDisposes(RxBus.INSTANCE.toObservable(ChapterPanelShowEvent::class.java)
      .observeOn(KwaiSchedulers.MAIN)
      .subscribe { event: ChapterPanelShowEvent ->
        notifyAutoPlaySetChange(event.show, AutoPlayStopType.STOP_BY_CHAPTER_PANEL_SHOW)
      })

    // 监听图片下载面板
    addToAutoDisposes(
      RxBus.INSTANCE.toObservable(
        DownloadPicDialogEvent::class.java
      )
        .observeOn(KwaiSchedulers.MAIN)
        .subscribe { event: DownloadPicDialogEvent ->
          notifyAutoPlaySetChange(event.mEvent == DownloadPicDialogEvent.PIC_DIALOG_SHOW, AutoPlayStopType.STOP_BY_PIC_DOWNLOAD_DIALOG)
        })

    /**
     * 评论面板
     *
     * 存在的问题：通知不太准确，评论面板收起，animStart就通知了隐藏。如果在评论动画期间切换作品，会导致小窗无法正常收起。
     * 评论动画结束后才会设置ViewPager可滚动，所以目前依赖于在[currentSupportAutoPlay]中
     * 通过[SlidePlayViewModel.isViewPagerOperationProhibited]#兜底避免此问题
     */
    addToAutoDisposes(
      mFragmentLocalBus.observable(DetailEventId.DETAIL_SLIDE_COMMENT_FRAGMENT_VISIBLE)
        .subscribe { isVisible: Boolean ->
          notifyAutoPlaySetChange(isVisible, AutoPlayStopType.STOP_BY_COMMENT)
        })
    addToAutoDisposes(
      mFragmentLocalBus.observable(DetailEventId.DETAIL_SLIDE_AI_TEXT_FRAGMENT_VISIBLE)
        .subscribe { isVisible: Boolean ->
          notifyAutoPlaySetChange(isVisible, AutoPlayStopType.STOP_BY_AI_TEXT)
        })

    // 监听侧边栏
    addToAutoDisposes(
      MenuViewModel
      .get((activity as FragmentActivity?)!!)
      .openState
      .subscribe { open: Boolean ->
        if (!mIsAttached) {
          return@subscribe
        }
        notifyAutoPlaySetChange(open, AutoPlayStopType.STOP_BY_SLIDE_MENU)
      })
    //监听不感兴趣细化原因面板
    addToAutoDisposes(
      RxBus.INSTANCE.toObservable(NegativeReasonPanelShowEvent::class.java)
        .observeOn(KwaiSchedulers.MAIN)
        .subscribe {
          if (!mIsAttached) {
            return@subscribe
          }
          notifyAutoPlaySetChange(it.isShowing, AutoPlayStopType.STOP_BY_NEGATIVE_REASON_PANEL)
        }
    )
    //监听Tab编辑面板
    addToAutoDisposes(RxBus.INSTANCE.toObservable(TabEditDialogShowEvent::class.java)
      .observeOn(KwaiSchedulers.MAIN)
      .subscribe {
        if (!mIsAttached) {
          return@subscribe
        }
        notifyAutoPlaySetChange(it.isShow, AutoPlayStopType.STOP_BY_TAB_EDIT_DIALOG)
      })

    // 监听弹幕输入面板
    mDanmakuDisposable = mBarrageKitWrapper.getDanmakuKitObservable(false, false, null)
      .observeOn(KwaiSchedulers.MAIN)
      .subscribe { danmakuKit: DanmakuKit ->
        mDanmakuKit = danmakuKit
        danmakuKit.addCallback(mDanmakuCallback)
      }

    // 弹幕互动气泡
    addToAutoDisposes(RxBus.INSTANCE.toObservable(DanmakuLikePop
path:ks-features/ft-corona/corona-core/src/main/java/com/yxcorp/gifshow/corona/detail/player/CoronaDetailPlayerPresenter.javacode:@Override
  protected void onBind() {
    super.onBind();
    initSessionKeyGenerator();
    mPlayerView.setDataParams(new LVCommonPlayerParams(mPhoto,
        new WeakReference<>(mFragment),
        mPlayerLogger.getStatEventUuid(),
        0f,
        false,
        false,
        true,
        false,
        true,
        GLOBAL,
        true,
        getShareSessionTag(),
        mDetailGlobalCallerContext.mSessionKeyGenerator,
        false,
        false));
    CoronaCommonPhotoUtil.putHistory(mPhoto);
    RxBus.INSTANCE.post(new GlobalPhotoPlayEvent(mFragment.getPage2(), mPhoto.mEntity));
    LVCommonMessageDispatcher panelDispatcher =
        mPlayerView.getMPlayerContext().getDispatcher(LVCommonMessageDispatcher.class);
    mActionService =
        mPlayerView.getMPlayerContext().getService(LVCommonPlayActionServiceInterface.class);
    if (!mPageState.isScreenLanScape()) {
      mCoronaDetailDebugInfo
          .add("Photo id is cached = " + QPhotoMediaPlayerCacheManager.isQPhotoCached(mPhoto));
      tickVideoPlayerInitBeginEvent(mStartParam);
      if (mDetailConfig != null
          && mDetailConfig.mSynchronizePlayerState) {
        mPlayerView.initPlayer(false);
        trySynchronizePlayerState();
      } else {
        mPlayerView.initPlayer(true);
      }
      tickVideoPlayerInitEndEvent(mStartParam);
      CoronaLogUtils
          .logD(TimeCostRecord.TAG_CORONA_DETAIL_FULL_TIME_COST, "CoronaDetailPlayerPresenter-player init end");
      mPlayerLogger.setLVCommonVse(mPlayerView.getMPlayerContext().getMLogger());
      if(CoronaCommonFeedUtil.isTvPhoto(mPhoto)) {
        if (!NetworkUtilsCached.isNetworkConnected()) {
          addExtraMsg(mStartParam, "networkConnect", "false");
        }
      }
      mPlayerLogger.logPrepareStart();
    }
    mPlayerView.loadPlayerUIElement();
    // 放映厅作品暂停播放上报
    if (CoronaCommonFeedUtil.isTvPhoto(mPhoto)) {
      panelDispatcher.addControlCenterButtonClickConsumer(isStart -> {
        if (!isStart) {
          mCoronaDetailLogger.logPlayerCenterButtonPause(mPhoto);
        }
      });
    }
    if (mPlayerView.isPrepared()) {
      mOnPreparedLogListener.onPrepared(mPlayerView.getPlayer().getIKwaiMediaPlayer());
    } else {
      mPlayerView.getPlayer().addOnPreparedListener(mOnPreparedLogListener);
    }
    mPlayerView.getPlayer().addOnInfoListener(mOnInfoListener);
    mPlayerView.getPlayer().addPlayerStateChangedListener(mPlayerStateChangedListener);

    // opt(zhangxiang): 因为 attach 的 player是上个页面共享的player，必须在player attach 之后创建，否则收不到回调。
    // 原因是attach之前创建helper，helper内部的mIsRenderingStart无法设置为true
    if (mPlayerView.getPlayer().isPlaying()) {
      mPlayerLogger.logPlayerActualStart();
    }

    mLifecycleState = new FragmentCompositeLifecycleState(mFragment);
    observerLifeCircle();
    if (mPhoto.isHdr()) {
      mPlayerView.getMContentFrame().setEnableUseCoverWhenPause(false);
    }

    panelDispatcher.addControlVisibleChangedConsumer(lvCommonControlPanelData -> {
      if (lvCommonControlPanelData == null) {
        return;
      }
      if (lvCommonControlPanelData.isVisible()) {
        logSeekBarShow();
        logLandscapeButtonShow();
      }
    });

    mPlayerView.getMPlayerContext().getService(LVCommonHandleViewService.class)
        .addProgressChangeInterceptor(mPlayerSeekInterceptor);

    mBitmapSupplier.set(mSupplier);
    mPlayerViewSupplierRef.set(mPlayerViewSupplier);
    mLVCommonWaterMaskContainer.setPlayerView(mPlayerView);
    panelDispatcher.addControlVisibleChangedConsumer(mPlayerView.getTipsHelper());
    panelDispatcher.addControlVisibleChangedConsumer(mLVCommonWaterMaskContainer);

    mPlayerLogger.setEnable(true);
    mPlayerLogger.logPlayerAttach();
    if (mStartParam.mStartTime != 0) {
      // 如果设置了启动时间
      mPlayerLogger.logClickToFirstFrameStart(mStartParam.mStartTime);
      // 恢复值,防止详情页内部切换视频,还有原来的值
      mStartParam.mStartTime = 0;
    } else {
      mPlayerLogger.logClickToFirstFrameStart();
    }

    addToAutoDisposes(mFragment.lifecycle().subscribe((fragmentEvent) -> {
      if (fragmentEvent == FragmentEvent.RESUME) {
        mPlayerLogger.logPageForeground();
      } else if (fragmentEvent == FragmentEvent.PAUSE) {
        mPlayerLogger.logPageBackground();
      }
    }, Functions.ERROR_CONSUMER));

    addToAutoDisposes(HeadsetPlugMonitor.register(AppEnv.APP, true)
        .subscribe(useEar