path:ks-features/ft-platform/mini/src/main/java/com/mini/biz/pushpreview/ToolsPushPreviewFragment.javacode:private void doBind() {
    mDisposable = Observable.create((ObservableOnSubscribe<PacketData>) emitter -> {
          PacketData packetData = new PacketData();
          packetData.setCommand(ToolsPushPreviewHelper.KEY_KWAI_LINK_BIND_DEVICE);
          JSONObject data = new JSONObject();
          data.put("action", "doBind");
          data.put("bindId", mBindId);
          byte[] bytes = data.toString().getBytes();
          packetData.setData(bytes);
          PacketData responseData = KwaiSignalManager.getInstance().getKwaiLinkClient().sendSync(packetData, 15000);
          if (responseData == null) {
            throw new RuntimeException("数据错误");
          }
          if (responseData.getErrorCode() != 200000) {
            throw new RuntimeException(responseData.getErrorMsg());
          }
          emitter.onNext(responseData);
          emitter.onComplete();
        }).subscribeOn(KwaiSchedulers.ASYNC)
        .observeOn(KwaiSchedulers.MAIN)
        .doOnSubscribe(disposable -> mIsBinding = true)
        .doOnNext(packetData -> mIsBinding = false)
        .doOnError(throwable -> mIsBinding = false)
        .subscribe(bindInfo -> {
          PreferenceUtil.getPreferences().edit().putBoolean(
              ToolsPushPreviewHelper.KEY_PREFERENCE_BIND_DEVICE, true).apply();
          ToolsPushPreviewHelper.getInstance().registerKwaiLinkListener();
          if (ActivityUtils.isActivityAvailable(getActivity())) {
            getActivity().finish();
          }
        }, throwable -> {
          if (ActivityUtils.isActivityAvailable(getActivity())) {
            Toast.makeText(getActivity(), throwable.getMessage(), Toast.LENGTH_LONG).show();
          }
        });
  }
path:ks-components/slide-play-detail-framework/src/main/java/com/kwai/slide/play/detail/base/BaseGroup.ktcode:@JvmOverloads fun bind() {
    KLogDetailFramework.get().e("VM_BaseGroup", "bind start isBind = $isBind, ${getClassName()}")
    if (isBind) {
      throw Exception("${javaClass.name}  Already bind")
    }
    try {
      elementOrderList.forEach {
        if (!(it is DispatchBaseElement && it.isDispatchEnable())) {
          it.tryShow()
        }
        it.isRealShow = false
        it.bind()
      }
    } catch (e: Exception) {
      KLogDetailFramework.get().e("VM_BaseGroup", "bind elementOrderList throw Exception ", e)
      throw e
    }

    isBind = true
    refreshAll()
    KLogDetailFramework.get().e("VM_BaseGroup", "bind end, ${getClassName()}")
  }
path:ks-features/ft-feed/kwai-growth/src/main/java/com/yxcorp/gifshow/growth/push/live/LiveNotificationDialogPresenter.ktcode:override fun onBind() {
    super.onBind()
    Log.i("LiveNotificationDialogPresenter", "onBind${this.hashCode()}")
    selectedTs = SystemClock.elapsedRealtime()
    try {
      if (!KwaiNotificationManager.isNotificationsEnabled(AppEnv.getAppContext()) && abSwitch) {
        registerListener()
      }
    } catch (e: Exception) {
    }
  }
path:ks-kernels/framework-download/src/main/java/com/kwai/framework/download/KwaiDownloadNotificationInfo.javacode:default void onNotificationClick(int downloadTaskId, @NotificationType int notificationType, Intent intent) {}
path:ks-features/ft-social/message/src/main/java/com/yxcorp/gifshow/message/widget/reaction/ReactionsLayoutV2.javacode:public void bindReactions(List<KwaiIMEmoticonReaction> reactions,
      ReactionEventCallBack callBack, boolean isSend, KwaiMsg msg
  ) {
    mIsSend = isSend;
    mManager.refreshReactionLayout(reactions, callBack, msg);
  }
path:ks-features/ft-live/live-features/live-common/src/main/java/com/kuaishou/live/redpacket/core/ui/view/lottery/rollpage/RedPacketConditionRollUserView.javacode:@Override
      public void onChanged(CDNUrl[] cdnUrls) {
        /*
         * 大图治理专项：
         * https://docs.corp.kuaishou.com/k/home/<USER>/fcAAYhcw7Q0pGWliiYowg9T4I
         * 增加兜底逻辑，防止布局没有完成时取不到宽度。
         */
        if (ViewCompat.isLaidOut(mAvatarView)) {
          bindAvatarViewUrl(cdnUrls);
        } else {
          mAvatarView.post(() -> bindAvatarViewUrl(cdnUrls));
        }
      }
path:ks-applications/kwai-android/src-perf-aspectj/yxcorp/gifshow/apm/SysTraceHelper.javacode:能在systrace上监控
      + "|| execution(* com.yxcorp.gifshow.recycler.fragment.RecyclerFragment+.*(..))"
      + "|| execution(* com.kwai.framework.network.KwaiParams.*(..))"
      + "|| execution(*  com.kuaishou.android.vader.Vader.*(..))"
      + "|| execution(*  com.yxcorp.gifshow.nasa.featured.*(..))"
      + "|| execution(*  com.yxcorp.gifshow.homepage.presenter.splash.SplashPresenter.on*(..))"
      + "|| execution(* com.yxcorp.gifshow.apm.TabApmTracker.on*(..))"
      + "|| execution(* com.kwai.framework.player..*(..))"
      + "|| execution(* com.kwai.framework.player_kpmid..*(..))"
      + "|| execution(* com.yxcorp.gifshow.detail.slidev2..*(..))"
      + "|| execution(* com.yxcorp.gifshow.detail.slideplay..*(..))"
      + "|| execution(* com.kwai.video.player.mid.builder..*(..))"
      + "|| execution(* com.kwai.video.player.kwai_player..*(..))"
      + "|| execution(* android.text.TextUtils.*(..))"
      + "|| execution(* com.kwai.video.player.kwai_player..*(..))"
      + "|| execution(* com.kwai.video.player.mid.config..*(..))"
      + "|| execution(* com.kwai.video.player.mid.manifest..*(..))"
      + "|| execution(* com.kwai.video.player.mid.util..*(..))"
      + "|| execution(* com.kwai.library.groot.api.viewmodel.SlidePlayViewModel.*(..))"
      + "|| execution(* com.yxcorp.gifshow.util.GrootSwitchUtils.*(..))"
      + "|| execution(* com.kwai.sdk.switchconfig..*(..))"
      + "|| execution(* com.kwai.framework.testconfig.*(..))"
      + "|| execution(* com.yxcorp.gifshow.detail.presenter..*(..))"
      + "|| execution(* com.yxcorp.gifshow.featured.detail..*(..))"
      + "|| execution(* com.kwai.component.photo.detail.slide..*(..))"
      + "|| execution(*  com.yxcorp.gifshow.detail.nonslide..*(..))"
      + "|| execution(*  com.gifshow.kuaishou.floatwidget..*(..))"
      + "|| execution(*  com.kwai.slide.play.detail..*(..))"
      + "|| execution(*  com.kwai.library.slide.base..*(..))"
      + "|| execution(*  com.kuaishou.android.model..*(..))"
      + "|| execution(*  com.kuaishou.live.core.show.gift..*(..))"
      + "|| execution(*  com.yxcorp.gifshow.reminder.nasa..*(..))"
      + "|| execution(*  com.yxcorp.experiment..*(..))"
      + "|| execution(*  com.kwai.sdk.switchconfig..*(..))"
      + "|| execution(*  com.kuaishou.commercial..*(..))"
      + "|| execution(*  com.mini.entrance.initmodule..*(..))"
      + "|| execution(*  com.xiaomi.push.service..*(..))"
      + "|| execution(*  com.yxcorp.gifshow.pendant.tk.bridge.PendantNative2JsInvoker..*(..))"
      + "|| execution(*  com.yxcorp.gifshow.pendant.widget.TkTaskPendant..*(..))"
      + "|| execution(*  com.tachikoma.core.bridge.TKJSContext..*(..))"
      + "|| execution(*  com.kwai.library.groot..*(..))"
      + "|| execution(*  com.yxcorp.gifshow.detail.slideplay.nasa.vm.SlidePage.*(..))"
      + "|| execution(*  com.kwai.slide.play.detail.base..*(..))"
      + "|| execution(*  com.kuaishou.gifshow.kswebview..*(..))"
      + "|| execution(*  com.kuaishou.webkit..*(..))"
      + "|| execution(*  com.kwai.yoda..*(..))"
  )
  public void executionPoint() {
  }