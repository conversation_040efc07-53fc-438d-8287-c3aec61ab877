#!/bin/bash
# 环境变量设置脚本

echo "设置RAG+Qwen项目环境变量..."

# API Keys (从您的代码中提取的)
export WQ_API_KEY="4kqyobx9g9jjixgk0xaqool9ldkc5e83qxl7"
export QWEN_API_KEY="ofdvapet0gfvdtnpdbr5pffd2qvn44sxt32h"
export ANALYSIS_API_KEY="ofdvapet0gfvdtnpdbr5pffd2qvn44sxt32h"
export SUMMARY_API_KEY="ofdvapet0gfvdtnpdbr5pffd2qvn44sxt32h"

# KwaiPilot配置
export KWAI_PILOT_TOKEN="DDKmVYBIF2P9aN"
export KWAI_PILOT_USER="lvaolin"

# API Base URLs
export WANQING_API_BASE="https://wanqing-api.staging.kuaishou.com/api/agent/v1/apps"
export KWAI_GATEWAY_BASE="https://ai-gateway.corp.kuaishou.com"

# 模型IDs
export MODEL_QWEN_ANALYSIS="app-msvz3y-1748316514060935132"
export MODEL_CODE_ANALYSIS="app-ad7yvp-1748252835514881827"
export MODEL_SUMMARY="app-4kdpqh-1748337711643805362"

echo "✅ 环境变量设置完成"
echo "使用方法："
echo "source setup_env.sh"
echo "或者"
echo ". setup_env.sh"
